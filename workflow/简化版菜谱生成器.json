{"type": "coze-workflow-clipboard-data", "source": {"workflowId": "7509079720160837646", "flowMode": 0, "spaceId": "7485774234682507273", "isDouyin": false, "host": "www.coze.cn"}, "json": {"nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": -700, "y": 300}}, "data": {"nodeMeta": {"title": "开始", "description": "工作流开始节点"}, "inputs": {}, "outputs": [{"name": "input", "type": "string", "description": "用户输入的菜名"}]}}, {"id": "110001", "type": "3", "meta": {"position": {"x": -400, "y": 280}}, "data": {"nodeMeta": {"title": "菜谱生成大模型", "description": "生成详细的菜谱内容"}, "inputs": {"inputParameters": [{"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "**用户输入**：{{input}}\n**你的输出**：请为这道菜生成详细的制作方法，包括食材准备、制作步骤、关键技巧和营养价值。", "rawMeta": {"type": 1}}}}, {"name": "input", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}], "llmParam": [{"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "DeepSeek-V3-0324", "rawMeta": {"type": 1}}}}, {"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": 1742989917, "rawMeta": {"type": 2}}}}, {"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": 0.8, "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": 4096, "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": 2, "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "你是一位精通中餐制作的资深厨师，拥有30年的烹饪经验。你熟悉各种菜系的制作方法，能够提供详细、实用的烹饪指导。请用口语化、生活化的中文表达，让普通家庭也能轻松学会制作美味佳肴。\n\n请按照以下格式输出菜谱：\n\n## 菜名\n\n### 🥘 食材准备\n- 主料：\n- 辅料：\n- 调料：\n\n### 👨‍🍳 制作步骤\n1. 步骤一\n2. 步骤二\n...\n\n### 💡 关键技巧\n- 技巧要点\n\n### 🌟 营养价值\n- 营养说明\n\n### 🔄 变体建议\n- 口味变化建议", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": 3, "rawMeta": {"type": 2}}}}]}, "outputs": [{"name": "output", "type": "string", "description": "生成的菜谱内容"}], "settingOnError": {"switch": true, "processType": 1, "timeoutMs": 600000, "retryTimes": 1}}}, {"id": "120001", "type": "13", "meta": {"position": {"x": -50, "y": 300}}, "data": {"inputs": {"content": {"type": "string", "value": {"type": "literal", "content": "{{output}}\n\n📝 菜谱生成完毕！正在为您制作精美的菜谱卡片...", "rawMeta": {"type": 1}}}, "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "110001", "name": "output"}, "rawMeta": {"type": 1}}}}], "streamingOutput": true, "callTransferVoice": false, "chatHistoryWriting": "historyWrite"}}}, {"id": "130001", "type": "4", "meta": {"position": {"x": 300, "y": 200}}, "data": {"nodeMeta": {"title": "营养计算", "subtitle": "插件:<PERSON><PERSON>"}, "inputs": {"apiParam": [{"name": "apiID", "input": {"type": "string", "value": {"type": "literal", "content": "7328314516272463939", "rawMeta": {"type": 1}}}}, {"name": "apiName", "input": {"type": "string", "value": {"type": "literal", "content": "calculate", "rawMeta": {"type": 1}}}}, {"name": "pluginID", "input": {"type": "string", "value": {"type": "literal", "content": "7328314516272463923", "rawMeta": {"type": 1}}}}, {"name": "pluginName", "input": {"type": "string", "value": {"type": "literal", "content": "<PERSON><PERSON>", "rawMeta": {"type": 1}}}}], "inputDefs": [{"description": "营养计算查询", "name": "input", "required": true, "type": "string"}], "inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "literal", "content": "nutritional information for typical Chinese dish serving", "rawMeta": {"type": 1}}}}]}, "outputs": [{"name": "output", "type": "string", "description": "营养信息"}], "settingOnError": {"switch": true, "processType": 1, "timeoutMs": 180000, "retryTimes": 1}}}, {"id": "140001", "type": "4", "meta": {"position": {"x": 300, "y": 380}}, "data": {"nodeMeta": {"title": "菜谱卡片设计", "subtitle": "插件:创客贴智能设计"}, "inputs": {"apiParam": [{"name": "apiID", "input": {"type": "string", "value": {"type": "literal", "content": "7351267469065027625", "rawMeta": {"type": 1}}}}, {"name": "apiName", "input": {"type": "string", "value": {"type": "literal", "content": "ckt_intelligent_design", "rawMeta": {"type": 1}}}}, {"name": "pluginID", "input": {"type": "string", "value": {"type": "literal", "content": "7351267469065011241", "rawMeta": {"type": 1}}}}, {"name": "pluginName", "input": {"type": "string", "value": {"type": "literal", "content": "创客贴智能设计", "rawMeta": {"type": 1}}}}], "inputDefs": [{"description": "设计需求描述", "name": "query", "required": true, "type": "string"}], "inputParameters": [{"name": "query", "input": {"type": "string", "value": {"type": "literal", "content": "美食菜谱卡片设计，中式料理风格，温馨家庭感", "rawMeta": {"type": 1}}}}]}, "outputs": [{"name": "output", "type": "string", "description": "设计图片"}], "settingOnError": {"switch": true, "processType": 1, "timeoutMs": 180000, "retryTimes": 1}}}, {"id": "150001", "type": "13", "meta": {"position": {"x": 650, "y": 300}}, "data": {"inputs": {"content": {"type": "string", "value": {"type": "literal", "content": "🍽️ **完整菜谱已生成！**\n\n{{recipe}}\n\n📊 **营养参考**：\n{{nutrition}}\n\n🎨 **精美卡片**：\n{{design}}\n\n✨ 您的专属菜谱已经准备就绪，祝您烹饪愉快！", "rawMeta": {"type": 1}}}, "inputParameters": [{"name": "recipe", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "110001", "name": "output"}, "rawMeta": {"type": 1}}}}, {"name": "nutrition", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "130001", "name": "output"}, "rawMeta": {"type": 1}}}}, {"name": "design", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "140001", "name": "output"}, "rawMeta": {"type": 1}}}}], "streamingOutput": false, "callTransferVoice": false, "chatHistoryWriting": "historyWrite"}}}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "110001"}, {"sourceNodeID": "110001", "targetNodeID": "120001"}, {"sourceNodeID": "120001", "targetNodeID": "130001"}, {"sourceNodeID": "120001", "targetNodeID": "140001"}, {"sourceNodeID": "130001", "targetNodeID": "150001"}, {"sourceNodeID": "140001", "targetNodeID": "150001"}], "bounds": {"x": -700, "y": 200, "width": 1350, "height": 180}}}