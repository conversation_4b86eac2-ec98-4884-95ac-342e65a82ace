{"type": "coze-workflow-clipboard-data", "source": {"workflowId": "7509079720160837645", "flowMode": 0, "spaceId": "7485774234682507273", "isDouyin": false, "host": "www.coze.cn"}, "json": {"nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": -800, "y": 300}}, "data": {"nodeMeta": {"title": "开始", "description": "工作流开始节点"}, "inputs": {}, "outputs": [{"name": "input", "type": "string", "description": "用户输入的创作主题"}]}}, {"id": "110001", "type": "3", "meta": {"position": {"x": -500, "y": 280}}, "data": {"nodeMeta": {"title": "内容创作大模型", "description": "基于主题生成创意内容"}, "inputs": {"inputParameters": [{"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "请基于以下主题创作一篇高质量的内容：{{input}}\n\n要求：\n1. 内容结构清晰，逻辑性强\n2. 语言生动有趣，易于理解\n3. 包含实用的信息和见解\n4. 字数控制在800-1200字\n5. 适合目标受众阅读", "rawMeta": {"type": 1}}}}, {"name": "input", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}], "llmParam": [{"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "DeepSeek-V3-0324", "rawMeta": {"type": 1}}}}, {"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": 1742989917, "rawMeta": {"type": 2}}}}, {"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": 0.8, "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": 8192, "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": 2, "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "你是一位专业的内容创作专家，擅长创作各种类型的高质量内容。你的写作风格生动有趣，逻辑清晰，能够准确把握读者需求，创作出既有深度又有趣味的内容。请根据用户提供的主题，创作出符合要求的优质内容。", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": 3, "rawMeta": {"type": 2}}}}]}, "outputs": [{"name": "output", "type": "string", "description": "生成的创意内容"}], "settingOnError": {"switch": true, "processType": 1, "timeoutMs": 600000, "retryTimes": 1}}}, {"id": "120001", "type": "13", "meta": {"position": {"x": -150, "y": 300}}, "data": {"inputs": {"content": {"type": "string", "value": {"type": "literal", "content": "✅ 内容创作完成！\n\n{{output}}\n\n正在进行数据验证和配图生成，请稍候...", "rawMeta": {"type": 1}}}, "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "110001", "name": "output"}, "rawMeta": {"type": 1}}}}], "streamingOutput": true, "callTransferVoice": false, "chatHistoryWriting": "historyWrite"}}}, {"id": "130001", "type": "4", "meta": {"position": {"x": 200, "y": 180}}, "data": {"nodeMeta": {"title": "Wolfram Alpha数据验证", "subtitle": "插件:calculate"}, "inputs": {"apiParam": [{"name": "apiID", "input": {"type": "string", "value": {"type": "literal", "content": "7328314516272463939", "rawMeta": {"type": 1}}}}, {"name": "apiName", "input": {"type": "string", "value": {"type": "literal", "content": "calculate", "rawMeta": {"type": 1}}}}, {"name": "pluginID", "input": {"type": "string", "value": {"type": "literal", "content": "7328314516272463923", "rawMeta": {"type": 1}}}}, {"name": "pluginName", "input": {"type": "string", "value": {"type": "literal", "content": "<PERSON><PERSON>", "rawMeta": {"type": 1}}}}, {"name": "pluginVersion", "input": {"type": "string", "value": {"type": "literal", "content": "", "rawMeta": {"type": 1}}}}], "inputDefs": [{"description": "数学表达式，如果不是英文需先翻译", "name": "input", "required": true, "type": "string"}], "inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "literal", "content": "Extract and verify any numerical data or statistics mentioned in the content", "rawMeta": {"type": 1}}}}]}, "outputs": [{"name": "output", "type": "string", "description": "数据验证结果"}], "settingOnError": {"switch": true, "processType": 1, "timeoutMs": 180000, "retryTimes": 1}}}, {"id": "140001", "type": "4", "meta": {"position": {"x": 200, "y": 380}}, "data": {"nodeMeta": {"title": "创客贴智能设计", "subtitle": "插件:ckt_intelligent_design"}, "inputs": {"apiParam": [{"name": "apiID", "input": {"type": "string", "value": {"type": "literal", "content": "7351267469065027625", "rawMeta": {"type": 1}}}}, {"name": "apiName", "input": {"type": "string", "value": {"type": "literal", "content": "ckt_intelligent_design", "rawMeta": {"type": 1}}}}, {"name": "pluginID", "input": {"type": "string", "value": {"type": "literal", "content": "7351267469065011241", "rawMeta": {"type": 1}}}}, {"name": "pluginName", "input": {"type": "string", "value": {"type": "literal", "content": "创客贴智能设计", "rawMeta": {"type": 1}}}}, {"name": "pluginVersion", "input": {"type": "string", "value": {"type": "literal", "content": "", "rawMeta": {"type": 1}}}}], "inputDefs": [{"description": "设计主题或需求描述", "name": "query", "required": true, "type": "string"}], "inputParameters": [{"name": "query", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}]}, "outputs": [{"name": "output", "type": "string", "description": "生成的设计图片"}], "settingOnError": {"switch": true, "processType": 1, "timeoutMs": 180000, "retryTimes": 1}}}, {"id": "150001", "type": "13", "meta": {"position": {"x": 550, "y": 300}}, "data": {"inputs": {"content": {"type": "string", "value": {"type": "literal", "content": "🎉 智能内容创作完成！\n\n📝 **原创内容**：\n{{content}}\n\n🔍 **数据验证**：\n{{verification}}\n\n🎨 **配图设计**：\n{{design}}\n\n✨ 您的内容已经准备就绪，可以直接使用或进一步编辑！", "rawMeta": {"type": 1}}}, "inputParameters": [{"name": "content", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "110001", "name": "output"}, "rawMeta": {"type": 1}}}}, {"name": "verification", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "130001", "name": "output"}, "rawMeta": {"type": 1}}}}, {"name": "design", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "140001", "name": "output"}, "rawMeta": {"type": 1}}}}], "streamingOutput": false, "callTransferVoice": false, "chatHistoryWriting": "historyWrite"}}}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "110001"}, {"sourceNodeID": "110001", "targetNodeID": "120001"}, {"sourceNodeID": "120001", "targetNodeID": "130001"}, {"sourceNodeID": "120001", "targetNodeID": "140001"}, {"sourceNodeID": "130001", "targetNodeID": "150001"}, {"sourceNodeID": "140001", "targetNodeID": "150001"}], "bounds": {"x": -800, "y": 180, "width": 1350, "height": 200}}}