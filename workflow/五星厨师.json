{"type": "coze-workflow-clipboard-data", "source": {"workflowId": "7509079720160837644", "flowMode": 0, "spaceId": "7485774234682507273", "isDouyin": false, "host": "www.coze.cn"}, "json": {"nodes": [{"id": "117173", "type": "3", "meta": {"position": {"x": -692.4845454002221, "y": 256.1218717049825}}, "data": {"nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "title": "大模型", "subTitle": "大模型"}, "inputs": {"inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}], "llmParam": [{"name": "generationDiversity", "input": {"type": "string", "value": {"type": "literal", "content": "default_val", "rawMeta": {"type": 1}}}}, {"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": "0.8", "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": "16384", "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": "2", "rawMeta": {"type": 2}}}}, {"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "DeepSeek-V3-0324", "rawMeta": {"type": 1}}}}, {"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": "1742989917", "rawMeta": {"type": 2}}}}, {"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "**用户输入**：{{input}}\n**你的输出**：  ", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": "3", "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "你是一位精通中餐制作的资深厨师，擅长将复杂的烹饪步骤拆解为清晰易懂的指导。请根据用户输入的菜名，生成包含以下结构的完整菜谱：  \n\n**角色与风格**  \n- 用口语化、生活化的中文表达，避免专业术语  \n- 步骤条理清晰，关键技巧突出，搭配实用建议  \n- 适当使用表情符号和分隔线增强可读性  \n\n**输出格式**  \n1. ​**标题**：菜名+简短卖点（如：XX的家常做法/秘诀/懒人版）  \n2. ​**食材准备**：分主料/辅料/调料，标注用量（如“勺”“克”或直观描述）  \n3. ​**步骤**：  \n   - 预处理（清洗、切配、腌制）  \n   - 核心烹饪流程（爆香、翻炒、火候控制）  \n   - 调味与收尾技巧  \n4. ​**关键技巧**：3-5条提升成功率的注意事项  \n5. ​**变体建议**：食材替换、口味调整等灵活方案  \n\n**变量处理**  \n- 将{{菜名}}作为输入变量，动态生成对应内容  \n- 若遇到模糊菜名（如“鱼香肉丝”），默认使用最常见做法  \n- 对不存在或不明确的菜名，询问用户是否需要自定义指导  \n\n**示例参考**​（用户提供的黄焖鸡模板）  \n保持相同的信息密度、结构化排版与亲和力  \n\n---  \n\n", "rawMeta": {"type": 1}}}}], "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 600000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "output", "required": false}], "version": "3"}, "_temp": {"bounds": {"x": -872.4845454002221, "y": 256.1218717049825, "width": 360, "height": 166.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "description": "调用大语言模型,使用变量和提示词生成回复", "title": "大模型", "mainColor": "#5C62FF"}}}, {"id": "159465", "type": "3", "meta": {"position": {"x": 180.2239030223758, "y": 256.1218717049825}}, "data": {"nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "title": "大模型_1", "subTitle": "大模型"}, "inputs": {"inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "117173", "name": "output"}, "rawMeta": {"type": 1}}}}], "llmParam": [{"name": "generationDiversity", "input": {"type": "string", "value": {"type": "literal", "content": "default_val", "rawMeta": {"type": 1}}}}, {"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": "0.8", "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": "16384", "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": "2", "rawMeta": {"type": 2}}}}, {"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "DeepSeek-V3-0324", "rawMeta": {"type": 1}}}}, {"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": "1742989917", "rawMeta": {"type": 2}}}}, {"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "{{input}}", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": "3", "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "你是一位专业的HTML内容转换助手，擅长将文本内容重新组织为结构清晰、视觉吸引力强的HTML网页。\n\n当用户提供文本内容时，请执行以下操作：\n\n1. 分析文本内容，识别其中的：\n   - 标题和子标题\n   - 主要章节和段落\n   - 列表项和步骤\n   - 重点内容和关键词\n\n2. 创建一个完整的HTML文档，包含：\n   - 必要的DOCTYPE和HTML结构标签\n   - 适当的meta标签（包括charset和viewport）\n   - 内嵌CSS样式（采用现代化、响应式设计）\n   - 结构化的内容布局\n\n3. 应用以下设计原则：\n   - 使用卡片式布局突出重要内容\n   - 采用视觉层次结构区分不同重要级别的内容\n   - 通过颜色、字体粗细等方式强调关键点\n   - 添加适当的空白和分隔符提高可读性\n   - 使用图标或emoji增强视觉效果\n\n4. 确保输出的HTML：\n   - 语法正确，结构完整\n   - 具有响应式设计，适配不同设备\n   - 视觉美观，配色和谐\n   - 重点内容容易被用户识别\n\n请根据用户的具体内容类型（如食谱、教程、文章等）进行适当的调整，确保输出的HTML符合该类型内容的最佳展示方式。\n\n输出最终HTML代码时，请使用代码块格式并添加简短说明，解释设计思路和重点。", "rawMeta": {"type": 1}}}}], "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 600000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "output", "required": false}], "version": "3"}, "_temp": {"bounds": {"x": 0.22390302237579363, "y": 256.1218717049825, "width": 360, "height": 166.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "description": "调用大语言模型,使用变量和提示词生成回复", "title": "大模型", "mainColor": "#5C62FF"}}}, {"id": "122007", "type": "4", "meta": {"position": {"x": 1317.2186642843978, "y": 202.34299471590572}}, "data": {"nodeMeta": {"description": "上传html内容", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "上传html:html2url", "title": "html2url"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7486017394901581876", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "html2url", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7486017394901565492", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "上传html", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "网页内容", "input": {}, "name": "html", "required": true, "type": "string"}], "inputParameters": [{"name": "html", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "159465", "name": "output"}, "rawMeta": {"type": 1}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "message", "required": false}, {"type": "string", "name": "url", "required": false}]}, "_temp": {"bounds": {"x": 1137.2186642843978, "y": 202.34299471590572, "width": 360, "height": 138.7}, "externalData": {"icon": "https://lf3-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1742973417767865242_cgRXxhuqo5.png?lk3s=cd508e2b&x-expires=1750936686&x-signature=6R3q0W%2B%2FdXQ83LVEt0hXAW%2BQTqU%3D", "apiName": "html2url", "pluginID": "7486017394901565492", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "网页内容", "input": {}, "name": "html", "required": true, "type": "string"}], "outputs": [{"input": {}, "name": "url", "required": false, "type": "string"}, {"input": {}, "name": "message", "required": false, "type": "string"}], "updateTime": 1748333264, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "上传html内容，生成外网能访问的地址。", "title": "html2url", "mainColor": "#CA61FF"}}}, {"id": "144087", "type": "13", "meta": {"position": {"x": -265.3697067968046, "y": 282.1218717049825}}, "data": {"inputs": {"callTransferVoice": true, "chatHistoryWriting": "historyWrite", "content": {"type": "string", "value": {"type": "literal", "content": "{{output}}\n\n内容生成完毕，开始制作卡片，等待1-2分钟....."}}, "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "117173", "name": "output"}, "rawMeta": {"type": 1}}}}], "streamingOutput": true}, "nodeMeta": {"description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "mainColor": "#5C62FF", "subTitle": "输出", "title": "输出"}}, "_temp": {"bounds": {"x": -445.3697067968046, "y": 282.1218717049825, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "title": "输出", "mainColor": "#5C62FF"}}}, {"id": "114757", "type": "13", "meta": {"position": {"x": 597.1013198969328, "y": 282.1218717049825}}, "data": {"inputs": {"callTransferVoice": true, "chatHistoryWriting": "historyWrite", "content": {"type": "string", "value": {"type": "literal", "content": "开始制作卡片"}}, "inputParameters": [], "streamingOutput": false}, "nodeMeta": {"description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "mainColor": "#5C62FF", "subTitle": "输出", "title": "输出_1"}}, "_temp": {"bounds": {"x": 417.10131989693275, "y": 282.1218717049825, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "title": "输出", "mainColor": "#5C62FF"}}}, {"id": "183892", "type": "4", "meta": {"position": {"x": 1061.072522692984, "y": 387.50674701681777}}, "data": {"nodeMeta": {"description": "网页转图片", "icon": "https://p9-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/8d35caff19ff49f9a8c14ff2ba49bc40~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1745724473&x-signature=%2FYeqAPLcjUyTUdgpxa6LXhC0jXI%3D", "subtitle": "html2img:gen_img", "title": "gen_img"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7486447880522776611", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "gen_img", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7486447880522760227", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "html2img", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "高度，默认自适应", "input": {}, "name": "height", "required": false, "type": "integer"}, {"description": "html", "input": {}, "name": "html", "required": true, "type": "string"}, {"description": "默认800", "input": {}, "name": "width", "required": false, "type": "integer"}], "inputParameters": [{"name": "html", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "159465", "name": "output"}, "rawMeta": {"type": 1}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "image_url", "required": false}, {"type": "string", "name": "message", "required": false}]}, "_temp": {"bounds": {"x": 881.072522692984, "y": 387.50674701681777, "width": 360, "height": 138.7}, "externalData": {"icon": "https://lf26-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1743074374246601536_oxCvXCTCuv.png?lk3s=cd508e2b&x-expires=1750936686&x-signature=HuWPoMN%2B7TzkBiJmm9uDR%2BWMSL0%3D", "apiName": "gen_img", "pluginID": "7486447880522760227", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "高度，默认自适应", "input": {}, "name": "height", "required": false, "type": "integer"}, {"description": "html", "input": {}, "name": "html", "required": true, "type": "string"}, {"description": "默认800", "input": {}, "name": "width", "required": false, "type": "integer"}], "outputs": [{"input": {}, "name": "image_url", "required": false, "type": "string"}, {"input": {}, "name": "message", "required": false, "type": "string"}], "updateTime": 1748340067, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "输入html内容直接渲染成图片", "title": "gen_img", "mainColor": "#CA61FF"}}}, {"id": "188604", "type": "13", "meta": {"position": {"x": 1534.1665847196425, "y": 399.5067470168177}}, "data": {"inputs": {"callTransferVoice": true, "chatHistoryWriting": "historyWrite", "content": {"type": "string", "value": {"type": "literal", "content": "![]({{output}})"}}, "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "183892", "name": "image_url"}, "rawMeta": {"type": 1}}}}], "streamingOutput": false}, "nodeMeta": {"description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "mainColor": "#5C62FF", "subTitle": "输出", "title": "预览图片"}}, "_temp": {"bounds": {"x": 1354.1665847196425, "y": 399.5067470168177, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "title": "输出", "mainColor": "#5C62FF"}}}], "edges": [{"sourceNodeID": "117173", "targetNodeID": "144087"}, {"sourceNodeID": "144087", "targetNodeID": "159465"}, {"sourceNodeID": "159465", "targetNodeID": "114757"}, {"sourceNodeID": "114757", "targetNodeID": "122007"}, {"sourceNodeID": "114757", "targetNodeID": "183892"}, {"sourceNodeID": "183892", "targetNodeID": "188604"}]}, "bounds": {"x": -872.4845454002221, "y": 202.34299471590572, "width": 2586.651130119865, "height": 323.86375230091204}}