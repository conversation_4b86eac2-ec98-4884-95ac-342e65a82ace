{"type": "coze-workflow-clipboard-data", "source": {"workflowId": "7508208010213572646", "flowMode": 0, "spaceId": "7485774234682507273", "isDouyin": false, "host": "www.coze.cn"}, "json": {"nodes": [{"id": "121343", "type": "3", "meta": {"position": {"x": -3027.562245841719, "y": 646.0553452394579}}, "data": {"nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "title": "大模型_根据主题生成文案", "subTitle": "大模型"}, "inputs": {"inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}], "llmParam": [{"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": "0.8", "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": "1024", "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": "0", "rawMeta": {"type": 2}}}}, {"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "DeepSeek-V3", "rawMeta": {"type": 1}}}}, {"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": "1738675210", "rawMeta": {"type": 2}}}}, {"name": "generationDiversity", "input": {"type": "string", "value": {"type": "literal", "content": "balance", "rawMeta": {"type": 1}}}}, {"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "主题：{{input}}\n", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": "3", "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "请根据用户提供的【主题】，按照以下结构生成一段历史类短视频口播文案：\n\n1. **悬念开场**：以“【朝代/场景】+ 反常识疑问/断言”开篇，激发观众兴趣（例：“古代【某职业】真的比【对比对象】更【形容词】吗？”）。\n2. **身份代入**：用第二人称“你”描述主角身份、时代背景及面临的致命危机（需包含具体官职/处境/对手）， 不要出现“想象一下”等过渡词，直接进入主题。\n3. **冲突升级**：\n   - 第一层：外部压力（如敌军压境、上级压迫、天灾降临）\n   - 第二层：内部瓦解（如下属背叛、资源短缺、疾病蔓延）\n   - 第三层：道德困境（如忠义两难、屠城抉择、政斗站队）\n4. **破局细节**：主角采取3个递进动作，包含：\n   - 震慑手段（当众处决/焚毁证据）\n   - 心理博弈（离间计/匿名信）\n   - 终极底牌（隐藏密件/借势压人）\n5. **主题收尾**：通过主角结局（惨胜/悲壮失败）引出金句，揭示历史规律（如“权力本质/战争真相/人性弱点”）。\n\n参考文案：\n参考文案1：\n在古代，当一个七品县令真的比做皇帝还爽吗？你是一个七品县令，听着像是芝麻官，实际上却管着一线生杀。你到任第一天，文案积压，吏园散漫，商人压税，盗匪频繁，所有人都在盯着你，想看看你这个读书人究竟能撑几日。于是，你命人将城中四大银行、三大米商召来，说了一句话，自今日起，税从清征，人从严制，谁敢藏奸瞒骗，先抄家，再杀头。众人皆笑，你却不恼，只是冷冷记下每人姓名，派人查账。三日后，你当街砍了一个米商账房，把人头挂在城门口。不久，其余几家主动上门认错，乖乖交税送礼，百姓自此称你为顾青天。你从不自称清官，但你知道，在这地方，拳头和律法并重才叫威望。一个月后，你已经在县城里站稳了脚跟。天还未亮，衙门门口已跪满人。左边是乡绅土豪求你叛帝，右边是盐商粮商给你送礼，你打了个哈欠。走出卧房后堂，十几名婢女簇拥着为你更衣梳发。你才刚坐下，茶就端来了，不是普通茶，是百里外送来的贡品。龙井三千银子一斤，一口下去，香气绕喉。你懒洋洋的抬眼看着堂下跪着的众人，手一挥，先问田地安，其余让他们在外等着。话音未落，师爷立刻应声，堂上传来惊堂木声，升堂在云阳县，穷人看你是神，富人把你当祖宗，就连知府大人来视察，都得先送一船好酒，再送一个歌妓，才敢上门拜访。你笑着收下他来视茶，你派人抬着软轿出城，五里相迎，不是给她面子，而是要让他知道，你虽然是七品，但这云阳县早已是你的天下。你升任第三年的秋天，县衙后库突发火灾，你亲自查探火因，结果意外发现一批私盐帐簿署名竟是你手下最信任的书立李记。你震怒，立即将其缉拿，却在其家中搜出一封未寄出的密信，写明这批私言背后勾结知州卢大人，牵连州府多名官员。你这才意识到，你触碰了这片土地上最不能动的那条线。延说，第二天你才刚出堂，就有人呈上一封匿名举报信，连你的私房收礼记录都一应俱全。你第一次感到这不是污蔑，这是操盘已久的杀局。知州大人紧急来访，私下告诫你有人要动，你背后牵的是京城兵部与阎道衙门，你得自己找活路。你立刻封锁案卷，亲自入库查账，发现那些伪账本竟源自你手下最信任的书立。你将其关入大牢，一审才知他的妻儿被人挟持，只得从命栽赃你，你这才意识到，自己就是那块要被剃掉的棋子。第二日，你还未来得及申辩，便接到了停职审查的诏书。知州亲率提其封了你的书房后堂暗牍，你如过街老鼠，众人避之。你低头无语，却没有认输，跟你早有后手。你连夜写密折命心腹快马送至京中清流大臣门下，可三日后却传来噩耗，送信之人马踏山崖，尸骨无存。此刻，你彻底陷入死局，这时钦差已入陷，兵部主事钦来查办，你被软禁在后堂，你以为你的结局将与前任三位县令无异，都死于意外。正当你思索破局之法时，昔日曾受你父亲庇护的朝中高官命书立偷偷送来一封信，信里竟藏着一张对账残页，印有兵部大员亲笔落款与黑岩交易详情。你明白了？他们大意了。栽赃太快，忘了悔证。7日后，钦差升堂审你。你披发跪堂，血衣染袍，众人以为你彻底崩溃了，你却突然从怀中掏出那张残页，当众高喊，我顾怀珍，问心无愧。今日一命换一局，看天子信谁。钦差当场变色，百姓哗然。而你早已让人将密政副本藏匿。一旦你死，城门口便贴文告公示。这一次，他们不敢动你。钦差低头退堂，3日后急返京城，再五日，圣旨亲临。顾怀贞未曾贪墨，实为中职之臣，留任云阳县专责清查南沿岸。你身披旧袍，眼中血丝未退，但腰杆挺直如初。你看着天轻声念叨我顾怀珍，就算是七品芝麻官，也能让权臣避让百官低头。那些试图吞下你的权贵，一个个被抄家砍头，这回你虽然没有升官，但却赢得了天下最难赢的那一局。\n\n\n参考文案2：\n一个汉朝使者竟在敌国当众砍了国王的人头，却没人敢多说一句话。今天我们讲大汉最狂使者班超，你是一名大汉边疆的普通士兵，略懂一些西域语言。这天长官班超突然发话，你准备一下，后天随我出使西域。你心头一紧，西域远在万里，危险重重，去过的使者几乎全都被砍了。可班超的眼神告诉你，这个任务不容你拒绝，行进的路程异常艰难，漫天风沙遮蔽了天际。一路上，你多次冒出逃跑的念头，可李智告诉你，脱离了大部队也是死路一条。三个月后，你们终于抵达了第一站楼兰。你早已听说过这里的危险，楼兰国王血腥手段让无数使者丧命于此。大殿内，楼兰国王坐在王座上冷眼看着你们周围的侍卫目露凶光，气氛紧张的让你几乎无法呼吸。你担心班超要是说错话会连累大伙被处死，不料他毫不畏惧的走上前，直指国王的鼻子，怒骂道，你这背信弃义的狗东西竟敢欺骗盟友匈奴使团从你这经过，竟然不告诉老子怎么是不是翅膀硬了？说完，班超看向你，冷声道，翻译给他听。你心头猛然一惊，差点喷出一口老血，但还是战战兢兢的翻译给国王听。国王怒吼道，你信不信我现在就砍了你。然而令你意想不到的是，班超不但没有害怕，反而把脖子凑了上去，挑衅道，来来来，朝这里砍不砍你是我孙子。此话一出，你被惊得愣在了原地，冷汗浸湿了三层衣袍，只听班超继续说道，南越杀我汉使，九郡被夷为平地，大碗杀我汉室，国王的脑袋被挂在城门上。今日你若杀我，明日汉军便会举国而出，屠径你楼兰数辈，而我的子孙会受到封赏，我也会名留青史，老子求之不得。你站在旁边心跳如雷鼓，仿佛下一刻就会被拖出去斩首，但令人惊讶的是，楼兰国王非但没有下令砍了你们，竟然还低头认错，表示愿意重新恢复大汉。一时间你被震惊的愣在了原地，我大汉朝啥时候那么硬气了？在别人的地盘上训斥他们的国王，如同老子训斥儿子一样。但更令你惊掉下巴的事还在后头，你们继续前行，来到了善善国。国王的礼遇让你觉得这一次可能是个顺利的任务。然而随着几天的相处，你渐渐察觉到不对劲，善善国王的态度开始变得冷淡，班超肯定也察觉到了这一点，过了几天，他把善善王的亲信叫了过来，语气平静却像刀匈奴的使者来了吧，打算待几天。一句话把对方吓得脸色发白，哆嗦着回道，五六日，你惊愕的看着班超，他没有直接询问匈奴使者是否到来，而是问得极其巧妙，仿佛早已知道一切。你心里发凉，36个人被困异国，如果善善国王决定投靠匈奴，你们所有人都将命丧于此，成为他的投名状。夜晚寒风凛冽，班超召集弟兄们喝酒。酒过三巡，他忽的一拍桌子，兄弟们，我们被困异国，国王已变脸，说不定哪天就把我们绑了送给匈奴。我们死在这，不会有墓碑，不会有人记得。如今这局面，你们说该怎么办？有人先喊了一句，无论是死是生，我们都听你的。接着你们36人齐声应下班超点头，冷静的不像是要杀人的将军，更像个写好了剧本的导演。我去匈奴营上放火，你们埋伏在出口，火势一起，谁跑出来就砍谁，谁敢逃就射谁。深夜风大如刀，班超点燃火把扔进匈奴使者的驻地。火光腾起那一刻，你心跳几乎停了，敌人惊慌出逃，你们早已守在四周，一刀一个。惨叫声4起，匈奴使节头颅飞溅，愚者被活活烧死。次日一早，班超提着一颗焦黑的脑袋，亲自去见善善国王。你跟在他身后，手还在发抖，只见他将人头扔在国王前，冷声道，既然你犹犹豫豫，那老子就替你做个决断，投靠谁国，自己看着办。国王脸色煞白，当即俯首称臣。不久后，单善国归附，震动西域。你实在忍不住轻声问他，我们这样是不是太霸道了？班超没回头，只是眉头一皱。他们弱却不谦卑，贪却不识相。你给他们脸，他们觉得你好欺负。你不拔刀，他们就敢骑在你头上拉屎。接下来的日子里，班超带你们在西域各国大闹一通后，还是不满意，因为这些国家只是迫于压力，表面沉浮。日后一有机会，他们还是会反水。于是他主动向霍光大将军请命，说要干一票大的。霍光大将军听了只淡淡一笑，不错，可以先拿楼兰国王来练练手。你听到这话，整个人僵在原地。练手？你们才多少人？三十几人而已，要去杀一个国王，颠覆一个政权，这不是练手，是送命啊。但你看看班超，他脸上没有一丝犹豫，你开始害怕，甚至比第一次跟他出使还要紧张。但你心里也清楚，一旦你们失败，大汉铁骑必定杀回来，到时候敌国血流成河，而你将作为烈士。这一次，你们带了满车金银，说是天子赏赐，与楼兰国君重修就好。小小楼兰国王见到这些宝物，两眼都直了，笑得比狗还谄媚，酒也越喝越猛，话也越说越飘。班超看向你只是一个眼神，你立刻明白，你悄悄带着另一个兄弟绕到屏风后等候。他走到国王耳边低语，天子密令，需要私下传话。楼兰王晃晃悠悠站起，毫无防备的走向你们。你屏住呼吸，手紧握剑柄，他刚一露头，班超眼中寒光一闪，剑刃贯穿心口。楼兰王连叫都没叫出声，瞪着一双死不瞑目的眼瘫软倒地。屏风外的文武百官当场傻眼，气氛一瞬间炸裂，有人握住了刀柄，有人咬牙低吼，啪板朝一声怒喝，酒被应声砸碎，谁若敢动，老子灭了你楼兰。小波一字一句像从地狱里拉出的声音，你也不知道他们到底怕了谁，怕班超，怕你们，还是怕背后那个庞然大物大汉。总之，他们全傻了，一个个低着头，连呼吸都小心翼翼。你们就这么砍下国王头颅，从人群中走出去，没有人拦，没有人动，你仿佛看见了卫青的铠甲，霍去病的战马，汉武帝的目光，还有那压在整个西域上空的大汉铁骑。你走得越来越稳，步子越来越大，头颅越抬越高，体梁越挺越直。这一刻你真切的为自己身为一个大汉子民而骄傲。\n\n\n参考文案3：\n古代战场上的瘟疫真的比敌人的刀剑更可怕吗？你是一名普通的宋朝士兵，此刻正坚守着一座即将被金军攻破的城池。就在敌军将要爬上城楼之际，你冒死将一架攻程梯狠狠推翻，鲜血混杂着汗水滴落在你的盔甲上，数日的厮杀抵抗使你浑身疼痛，但你依旧拼尽全力。终于熬到了半夜，敌军的攻势退去，你扶着刀柄浑身颤抖。原以为熬过了这一波攻势便能稍作喘息，却不知更可怕的敌人早已悄然逼近。午时3刻，你正趴在墙角休息，突然一具尸体从空中坠落，重重的砸在你脚边，顿时间血浆4溅。只见城外敌军竟然将死去士兵的尸体绑在投石车上，纷纷投入你们驻守的城中。尸体在半空中划过一道诡异的弧线，砸落在城内的街道上，散发出阵阵恶臭。你站在城墙上满心疑惑的看着这一幕，城中士兵甚至好奇的凑近尸体观察，却未料到一场灾难正在悄悄蔓延。夜幕降临，营地里弥漫着诡异的寂静。你窝在狭窄潮湿的帐篷里，伤口隐隐作痛，身边战友低声咳嗽着。你递给他一口水袋，却看到她的额头上渗满细密的汗珠，脸色惨白如纸。兄弟，你没事吧？他摇摇头，勉强挤出一个虚弱的笑容，没事，睡一觉就好了。你安慰自己，或许只是一场风寒罢了。次日清晨，你被惊叫声惊醒。掀开帐篷，映入眼帘的是令人毛骨悚然的一幕。营帐外躺满了奄奄一息的士兵，个个面色青黑，口唇干裂，有人甚至瘫倒在地，挣扎着向前爬动，眼神里充满了绝望。军医惊恐的喊道，不好，是瘟疫。你的心猛然一沉，汗毛倒竖。将军急忙下令隔离病患，但帐篷早已塞满了呻吟的病人，军中恐慌迅速蔓延，没人知道下一个倒下的会不会就是自己。几日之内，军队士气瓦解，敌方的探子传来消息，宋军染棘，已无战力，敌军趁势攻来。你们这些幸存者仓促应战，但手中的刀剑却仿佛重于千金。你看着一个个面容憔悴的战友倒在自己身边，敌人的刀还未靠近，他们便已因疾病虚脱而倒下。城破那日，你被敌人俘虏，眼睁睁看着自己驻守多年的城池被付之一炬。被俘的路上，你听敌兵闲聊，多亏了这一场瘟疫，否则城池哪能这么轻松的攻破，你心中一阵苦涩。押解途中，你与其他战俘被关进一处偏僻村落等待处理。夜晚守卫，昏昏欲睡，你趁机挣脱绳索逃出，却发现逃亡之路更加艰难，一路上尽是荒村野尸，家家闭户并漂遍野。你踩着尸骸踉跄前行，所到之处如同人间地狱。你明白，瘟疫比战争更无情，它不分敌我，只留下一片焦土和尸骨。几经挣扎，你终于逃回故乡，却发现村庄早已空无一人，唯有乌鸦的哀鸣在回响。你失魂落魄的走进家门，地上躺着父母冰冷僵硬的尸体，桌上还有未曾吃完的晚饭。你跪倒在地无声的痛哭。就在此时，你感到身体传来一阵剧痛，低头一看，手臂上赫然出现了与战友一样的黑斑，你颤抖着手摸向额头，已示滚烫如炭火。此刻，你才惊恐的意识到，自己也早已成为瘟疫的牺牲品，只是一直苦苦挣扎，不愿面对。耳边依稀响起昔日战友的叹息，原来我们才是这场战争真正的失败者。临死之际，你终于明白，战争胜负的背后，瘟疫才是真正的主宰。\n\n\n\n**要求**：\n- 每段不超过3句话，多用短句制造紧张节奏\n- 加入至少2处历史专业术语\n- 在关键转折点使用感官描写（气味/触感/视觉冲击）\n- 结尾以“这一刻你终于明白…”句式点题\n- 生成1000字左右口播文案\n- 文案由长短句构成，遇到长句会用逗号分隔成短句，每个短句不能超过19个汉字\n\n\n**输出要求**： 只输出口播字幕文案，不要输出其他任何额外内容，不输出分段说明", "rawMeta": {"type": 1}}}}], "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "content", "required": false}], "version": "3"}, "_temp": {"bounds": {"x": -3207.562245841719, "y": 646.0553452394579, "width": 360, "height": 166.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "description": "调用大语言模型,使用变量和提示词生成回复", "title": "大模型", "mainColor": "#5C62FF"}}}, {"id": "1199098", "type": "3", "meta": {"position": {"x": 139.8112817102542, "y": 531.6936695945778}}, "data": {"nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "title": "大模型_主题生成", "subTitle": "大模型"}, "inputs": {"inputParameters": [{"name": "content", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "121343", "name": "content"}, "rawMeta": {"type": 1}}}}], "llmParam": [{"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": "1", "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": "1024", "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": "2", "rawMeta": {"type": 2}}}}, {"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "DeepSeek-V3", "rawMeta": {"type": 1}}}}, {"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": "1738675210", "rawMeta": {"type": 2}}}}, {"name": "generationDiversity", "input": {"type": "string", "value": {"type": "literal", "content": "default_val", "rawMeta": {"type": 1}}}}, {"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "故事原文内容：\n{{content}}", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": "3", "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "# 角色\n能够深入理解故事文案的情节、人物、场景, 根据故事内容提炼出简洁精准的2个字故事主题素。\n\n## 技能\n### 技能1：生成2个字的主题\n1. 从故事文案中提炼出能够精准概括故事核心内容的2个字故事主题\n\n\n## 限制:\n- 只围绕用户提供的故事文案进行分镜绘画提示词生成和主题提炼，拒绝回答与该任务无关的话题。\n- 主题必须为2个字。 \n- 直接输出主题，不要回复其他额外内容", "rawMeta": {"type": 1}}}}], "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "title", "required": false}], "version": "3"}, "_temp": {"bounds": {"x": -40.18871828974579, "y": 531.6936695945778, "width": 360, "height": 166.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "description": "调用大语言模型,使用变量和提示词生成回复", "title": "大模型", "mainColor": "#5C62FF"}}}, {"id": "121555", "type": "28", "meta": {"position": {"x": 139.8112817102542, "y": 797.7936695945779}, "canvasPosition": {"x": -960.1887182897458, "y": 1197.293669594578}}, "data": {"inputs": {"batchSize": {"type": "integer", "value": {"type": "literal", "content": "100"}}, "concurrentSize": {"type": "integer", "value": {"type": "literal", "content": 3, "rawMeta": {"type": 2}}}, "inputParameters": [{"name": "scenes", "input": {"type": "list", "schema": {"type": "object", "schema": [{"type": "string", "name": "cap"}, {"type": "string", "name": "desc_promopt"}]}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "186126", "name": "scenes"}, "rawMeta": {"type": 103}}}}]}, "nodeMeta": {"description": "通过设定批量运行次数和逻辑，运行批处理体内的任务", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Batch-v2.jpg", "mainColor": "#00B2B2", "subTitle": "批处理", "title": "批处理"}, "outputs": [{"name": "image_list", "input": {"type": "list", "schema": {"type": "string"}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "187299", "name": "image_url"}, "rawMeta": {"type": 1}}}}, {"name": "link_list", "input": {"type": "list", "schema": {"type": "string"}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "182040", "name": "data.link"}, "rawMeta": {"type": 1}}}}, {"name": "duration_list", "input": {"type": "list", "schema": {"type": "integer"}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "178228", "name": "duration"}, "rawMeta": {"type": 2}}}}]}, "blocks": [{"id": "131139", "type": "16", "meta": {"position": {"x": 180.00000000000006, "y": -50.13027284391758}}, "data": {"inputs": {"apiParam": null, "inputParameters": [{"name": "desc_promopt", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "121555", "name": "scenes.desc_promopt"}, "rawMeta": {"type": 1}}}}], "modelSetting": {"custom_ratio": {"height": 768, "width": 1024}, "ddim_steps": 40, "images_reference": {}, "model": 8, "ratio": 0}, "prompt": {"negative_prompt": "", "prompt": "古代惊悚插画风格：{{desc_promopt}}"}, "references": [], "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 60000, "retryTimes": 0}}, "nodeMeta": {"description": "通过文字描述/添加参考图生成图片", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-ImageGeneration-v2.jpg", "subTitle": "图像生成", "title": "图像生成"}, "outputs": [{"type": "string", "assistType": 2, "name": "data", "required": false}, {"type": "string", "name": "msg", "required": false}], "settings": null, "version": ""}, "_temp": {"bounds": {"x": -960.1887182897458, "y": 1147.1633967506605, "width": 360, "height": 166.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-ImageGeneration-v2.jpg", "description": "通过文字描述/添加参考图生成图片", "title": "图像生成", "mainColor": "#FF4DC3"}}}, {"id": "133787", "type": "8", "meta": {"position": {"x": 640, "y": 13.099999999999994}}, "data": {"nodeMeta": {"description": "连接多个下游分支，若设定的条件成立则仅运行对应的分支，若均不成立则只运行“否则”分支", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Condition-v2.jpg", "mainColor": "#00B2B2", "subTitle": "选择器", "title": "选择器"}, "inputs": {"branches": [{"condition": {"logic": 2, "conditions": [{"operator": 10, "left": {"input": {"type": "string", "assistType": 2, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "131139", "name": "data"}}}}}]}}]}}, "_temp": {"bounds": {"x": -500.1887182897458, "y": 1210.393669594578, "width": 360, "height": 140.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Condition-v2.jpg", "description": "连接多个下游分支，若设定的条件成立则仅运行对应的分支，若均不成立则只运行“否则”分支", "title": "选择器", "mainColor": "#00B2B2"}}}, {"id": "187299", "type": "5", "meta": {"position": {"x": 2020, "y": 26}}, "data": {"nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "title": "代码_1", "subTitle": "代码"}, "inputs": {"inputParameters": [{"name": "image1", "input": {"type": "string", "assistType": 2, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "131139", "name": "data"}, "rawMeta": {"type": 7}}}}, {"name": "image2", "input": {"type": "string", "assistType": 2, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "1667619", "name": "data"}, "rawMeta": {"type": 7}}}}], "code": "// 在这里，您可以通过 ‘params’  获取节点中的输入变量，并通过 'ret' 输出结果\n// 'params' 和 'ret' 已经被正确地注入到环境中\n// 下面是一个示例，获取节点输入中参数名为‘input’的值：\n// const input = params.input; \n// 下面是一个示例，输出一个包含多种数据类型的 'ret' 对象：\n// const ret = { \"name\": ‘小明’, \"hobbies\": [“看书”, “旅游”] };\n\nasync function main({ params }: Args): Promise<Output> {\n\n\n    var image1 = params.image1;\n    var image2 = params.image2;\n    if(!image1){\n        image1 = image2;\n    }\n\n\n\n    // 构建输出对象\n    const ret = {\n        \"image_url\":  image1\n    };\n\n    return ret;\n}", "language": 5, "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 60000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "image_url", "required": false}]}, "_temp": {"bounds": {"x": 879.8112817102542, "y": 1223.293669594578, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "description": "编写代码，处理输入变量来生成返回值", "title": "代码", "mainColor": "#00B2B2"}}}, {"id": "109484", "type": "4", "meta": {"position": {"x": 1100, "y": 97.52499999999999}}, "data": {"nodeMeta": {"description": "智能优化图像提示词", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-plugin-better_prompt-v2.jpg", "subtitle": "提示词优化:sd_better_prompt", "title": "sd_better_prompt"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7439197952104726528", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "sd_better_prompt", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7439197952104710144", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "提示词优化", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputParameters": [{"name": "prompt", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "121555", "name": "scenes.desc_promopt"}, "rawMeta": {"type": 1}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "data", "required": false}, {"type": "string", "name": "msg", "required": false}]}, "_temp": {"bounds": {"x": -40.18871828974579, "y": 1294.818669594578, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf26-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/1682717607724762_1737442044729286337_eWiqvwPv2g.jpg?lk3s=cd508e2b&x-expires=1750732907&x-signature=wyl9s2INdu5Af9ytyRQSiTlg9Ac%3D", "apiName": "sd_better_prompt", "pluginID": "7439197952104710144", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7352795533666664448", "inputs": [{"description": "prompt", "input": {}, "name": "prompt", "required": true, "title": "提示词", "type": "string"}], "outputs": [{"input": {}, "name": "data", "required": false, "type": "string"}, {"input": {}, "name": "msg", "required": false, "type": "string"}], "updateTime": 1748140502, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "智能优化图像提示词", "title": "sd_better_prompt", "mainColor": "#CA61FF"}}}, {"id": "1667619", "type": "16", "meta": {"position": {"x": 1560, "y": 71.52499999999999}}, "data": {"inputs": {"apiParam": null, "inputParameters": [{"name": "desc_promopt", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "109484", "name": "data"}, "rawMeta": {"type": 1}}}}], "modelSetting": {"custom_ratio": {"height": 768, "width": 1024}, "ddim_steps": 40, "images_reference": {}, "model": 8, "ratio": 0}, "prompt": {"negative_prompt": "", "prompt": "{{desc_promopt}}"}, "references": [], "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 60000, "retryTimes": 0}}, "nodeMeta": {"description": "通过文字描述/添加参考图生成图片", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-ImageGeneration-v2.jpg", "subTitle": "图像生成", "title": "图像生成_1"}, "outputs": [{"type": "string", "assistType": 2, "name": "data", "required": false}, {"type": "string", "name": "msg", "required": false}], "settings": null, "version": ""}, "_temp": {"bounds": {"x": 419.8112817102542, "y": 1268.818669594578, "width": 360, "height": 166.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-ImageGeneration-v2.jpg", "description": "通过文字描述/添加参考图生成图片", "title": "图像生成", "mainColor": "#FF4DC3"}}}, {"id": "182040", "type": "4", "meta": {"position": {"x": 180, "y": 266.1}}, "data": {"nodeMeta": {"description": "根据音色和文本合成音频", "icon": "https://p26-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/dc534eb73e7046b1807db96af7c0e212~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1749019302&x-signature=ANAaS2aakkuW4eUDLIx7DP8XEoI%3D", "subtitle": "语音合成:speech_synthesis", "title": "speech_synthesis"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7426655854067367946", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "speech_synthesis", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7426655854067351562", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "语音合成", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputParameters": [{"name": "text", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "121555", "name": "scenes.cap"}, "rawMeta": {"type": 1}}}}, {"name": "speed_ratio", "input": {"type": "float", "value": {"type": "literal", "content": 1.2, "rawMeta": {"type": 4}}}}, {"name": "voice_id", "input": {"type": "string", "assistType": 12, "value": {"type": "literal", "content": "7468512265134932019", "rawMeta": {"fileName": "悬疑解说", "type": 18}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "float", "name": "code", "required": false}, {"type": "object", "name": "data", "schema": [{"type": "string", "name": "link", "required": false}], "required": false}, {"type": "string", "name": "log_id", "required": false}, {"type": "string", "name": "msg", "required": false}]}, "_temp": {"bounds": {"x": -960.1887182897458, "y": 1463.3936695945781, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf9-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/372098605791453_1729153029627658847_7vpeyBUsGn.jpeg?lk3s=cd508e2b&x-expires=1750732907&x-signature=MDDEwoWq4NIvEXxFar47efOs6r0%3D", "apiName": "speech_synthesis", "pluginID": "7426655854067351562", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7315016311652941851", "inputs": [{"description": "语音语种，非必填，参考 https://bytedance.larkoffice.com/docx/WdDOdiB1BoRyBNxlkXWcn0n3nLc", "input": {}, "name": "language", "required": false, "type": "string"}, {"defaultValue": "爽快思思/Skye", "description": "音色ID，默认为爽快思思/Skye。详细音色列表参考 https://bytedance.larkoffice.com/docx/WdDOdiB1BoRyBNxlkXWcn0n3nLc, default value is 爽快思思/Skye", "input": {}, "name": "speaker_id", "required": false, "type": "string"}, {"defaultValue": 1, "description": "语速，范围是[0.2,3]，默认为1，通常保留一位小数即可, default value is 1", "input": {}, "name": "speed_ratio", "required": false, "type": "float"}, {"description": "要合成音频的文本内容", "input": {}, "name": "text", "required": true, "type": "string"}, {"assistType": 12, "description": "voice id", "input": {}, "name": "voice_id", "required": false, "type": "string"}], "outputs": [{"input": {}, "name": "code", "required": false, "type": "float"}, {"input": {}, "name": "data", "required": false, "schema": [{"input": {}, "name": "link", "required": false, "type": "string"}], "type": "object"}, {"input": {}, "name": "log_id", "required": false, "type": "string"}, {"input": {}, "name": "msg", "required": false, "type": "string"}], "updateTime": 1748139354, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "根据音色和文本合成音频", "title": "speech_synthesis", "mainColor": "#CA61FF"}}}, {"id": "178228", "type": "4", "meta": {"position": {"x": 640, "y": 254.10000000000002}}, "data": {"nodeMeta": {"description": "剪映草稿，视频合成方案。👆👆👆插件详情页面查看案例", "icon": "https://p6-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/58f8745c794f452a9b0c572c9a4b3e3a~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1749019424&x-signature=3ok2yPv6y4mL9ketOUBg8SWfFhM%3D", "subtitle": "视频合成_剪映小助手:get_audio_duration", "title": "get_audio_duration"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7474863657353117750", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "get_audio_duration", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputParameters": [{"name": "mp3_url", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "182040", "name": "data.link"}, "rawMeta": {"type": 1}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "message", "required": false}, {"type": "integer", "name": "duration", "required": false}]}, "_temp": {"bounds": {"x": -500.1887182897458, "y": 1451.3936695945781, "width": 360, "height": 138.7}, "externalData": {"icon": "https://lf26-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1739242717113014582_ZXUfNgToeJ.png?lk3s=cd508e2b&x-expires=1750732907&x-signature=ipkoaSNLzYRZlG8%2B%2FzNj4eq19so%3D", "apiName": "get_audio_duration", "pluginID": "7457837925833801768", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "音频链接", "input": {}, "name": "mp3_url", "required": true, "type": "string"}], "outputs": [{"input": {}, "name": "duration", "required": false, "type": "integer"}, {"input": {}, "name": "message", "required": false, "type": "string"}], "updateTime": 1748140539, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "获取音频时长", "title": "get_audio_duration", "mainColor": "#CA61FF"}}}], "edges": [{"sourceNodeID": "121555", "targetNodeID": "131139", "sourcePortID": "batch-function-inline-output"}, {"sourceNodeID": "131139", "targetNodeID": "133787"}, {"sourceNodeID": "133787", "targetNodeID": "187299", "sourcePortID": "true"}, {"sourceNodeID": "133787", "targetNodeID": "109484", "sourcePortID": "false"}, {"sourceNodeID": "1667619", "targetNodeID": "187299"}, {"sourceNodeID": "187299", "targetNodeID": "121555", "targetPortID": "batch-function-inline-input"}, {"sourceNodeID": "109484", "targetNodeID": "1667619"}, {"sourceNodeID": "121555", "targetNodeID": "182040", "sourcePortID": "batch-function-inline-output"}, {"sourceNodeID": "182040", "targetNodeID": "178228"}, {"sourceNodeID": "178228", "targetNodeID": "121555", "targetPortID": "batch-function-inline-input"}], "_temp": {"bounds": {"x": -40.18871828974579, "y": 797.7936695945779, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Batch-v2.jpg", "description": "通过设定批量运行次数和逻辑，运行批处理体内的任务", "title": "批处理", "mainColor": "#00B2B2"}}}, {"id": "163547", "type": "5", "meta": {"position": {"x": 1340.3556798722389, "y": 670.3226933175883}}, "data": {"nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "title": "代码", "subTitle": "代码"}, "inputs": {"inputParameters": [{"name": "image_list", "input": {"type": "list", "schema": {"type": "string"}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "121555", "name": "image_list"}, "rawMeta": {"type": 99}}}}, {"name": "audio_list", "input": {"type": "list", "schema": {"type": "string"}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "121555", "name": "link_list"}, "rawMeta": {"type": 99}}}}, {"name": "duration_list", "input": {"type": "list", "schema": {"type": "integer"}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "121555", "name": "duration_list"}, "rawMeta": {"type": 100}}}}, {"name": "scenes", "input": {"type": "list", "schema": {"type": "object", "schema": [{"type": "string", "name": "cap"}, {"type": "string", "name": "desc_promopt"}]}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "186126", "name": "scenes"}, "rawMeta": {"type": 103}}}}, {"name": "title", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "1199098", "name": "title"}, "rawMeta": {"type": 1}}}}, {"name": "role_img_url", "input": {"type": "string", "assistType": 2, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "170966", "name": "data"}, "rawMeta": {"type": 7}}}}], "code": "// 在这里，您可以通过 ‘params’  获取节点中的输入变量，并通过 'ret' 输出结果\n// 'params' 和 'ret' 已经被正确地注入到环境中\n// 下面是一个示例，获取节点输入中参数名为‘input’的值：\n// const input = params.input; \n// 下面是一个示例，输出一个包含多种数据类型的 'ret' 对象：\n// const ret = { \"name\": ‘小明’, \"hobbies\": [“看书”, “旅游”] };\n\nasync function main({ params }: Args): Promise<Output> {\n\n\n    const { image_list, audio_list, duration_list, scenes } = params;\n\n    // 处理音频数据\n    const audioData = [];\n    let audioStartTime = 0;\n    const aideoTimelines = [];\n    let maxDuration = 0;\n\n    const imageData = [];\n    \n    \n    for (let i = 0; i < audio_list.length && i < duration_list.length; i++) {\n        const duration = duration_list[i];\n        audioData.push({\n            audio_url: audio_list[i],\n            duration,\n            start: audioStartTime,\n            end: audioStartTime + duration\n        });\n        aideoTimelines.push({\n            start: audioStartTime,\n            end: audioStartTime + duration\n        });\n\n        if((i-1)%2==0){\n            imageData.push({\n                image_url: image_list[i],\n                start: audioStartTime,\n                end: audioStartTime + duration,\n                width: 1440,\n                height: 1080,\n                in_animation: \"轻微放大\",\n                in_animation_duration: 100000\n            });\n        } else{\n            imageData.push({\n                image_url: image_list[i],\n                start: audioStartTime,\n                end: audioStartTime + duration,\n                width: 1440,\n                height: 1080\n            });\n        }\n       \n        \n        audioStartTime += duration;\n        maxDuration = audioStartTime;\n    }\n\n    const roleImgData = [];\n    roleImgData.push({\n        image_url: params.role_img_url,\n        start: 0,\n        end: duration_list[0],\n        width: 1440,\n        height: 1080\n    });\n\n\n    const captions = scenes.map(item => item.cap);\n    const subtitleDurations = duration_list;\n    \n    const { textTimelines, processedSubtitles } = processSubtitles(\n        captions,\n        subtitleDurations\n    );\n\n\n    // 开场2个字\n    const title = params.title;  // 标题\n    const title_list = [];\n    title_list.push(title);\n    const title_timelimes = [\n        {\n            start: 0,\n            end: duration_list[0]\n        }\n    ];\n    \n    // 开场音效 4884897\n    var kc_audio_url = \"https://p9-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/c04e7b48586a48f1863e421be4b10cf1.MP3~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1777550323&x-signature=T%2BNjvPHPyHnGICvWRFDeFaj17UM%3D&x-wf-file_name=%E6%95%85%E4%BA%8B%E5%BC%80%E5%9C%BA%E9%9F%B3%E6%95%88.MP3\";\n    // 背景音乐 343666938\n    var bg_audio_url =\"https://p3-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/5603dc783a6c4b75a4bf4e1b44086ad5.MP3~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1777550332&x-signature=E1123RzPTMD%2BipseRN4itYxhZyc%3D&x-wf-file_name=%E6%95%85%E4%BA%8B%E8%83%8C%E6%99%AF%E9%9F%B3%E4%B9%90.MP3\";\n\n    const bg_audio_data = [];\n    bg_audio_data.push({\n        audio_url: bg_audio_url,\n        duraion: maxDuration,\n        start: 0,\n        end: maxDuration\n    });\n\n    const kc_audio_data = [];\n    kc_audio_data.push({\n        audio_url: kc_audio_url,\n        duration: 4884897,\n        start: 0,\n        end: 4884897\n    });\n    \n    // 构建输出对象\n    const ret = {\n        \"audioData\": JSON.stringify(audioData),\n        \"bgAudioData\": JSON.stringify(bg_audio_data),\n        \"kcAudioData\": JSON.stringify(kc_audio_data),\n        \"imageData\": JSON.stringify(imageData),\n        \"text_timielines\":textTimelines,\n        \"text_captions\":processedSubtitles,\n        \"title_list\": title_list,\n        \"title_timelimes\": title_timelimes,\n        \"roleImgData\": JSON.stringify(roleImgData)\n    };\n\n    return ret;\n}\n\n\n\nconst SUB_CONFIG = {\n    MAX_LINE_LENGTH: 25,\n    SPLIT_PRIORITY: ['。','！','？','，',',','：',':','、','；',';',' '], // 补充句子结束符\n    TIME_PRECISION: 3\n};\n\nfunction splitLongPhrase(text, maxLen) {\n    if (text.length <= maxLen) return [text];\n    \n    // 严格在maxLen范围内查找分隔符\n    for (const delimiter of SUB_CONFIG.SPLIT_PRIORITY) {\n        const pos = text.lastIndexOf(delimiter, maxLen - 1); // 关键修改：限制查找范围\n        if (pos > 0) {\n            const splitPos = pos + 1;\n            return [\n                text.substring(0, splitPos).trim(),\n                ...splitLongPhrase(text.substring(splitPos).trim(), maxLen)\n            ];\n        }\n    }\n\n    // 汉字边界检查防止越界\n    const startPos = Math.min(maxLen, text.length) - 1;\n    for (let i = startPos; i > 0; i--) {\n        if (/[\\p{Unified_Ideograph}]/u.test(text[i])) {\n            return [\n                text.substring(0, i + 1).trim(),\n                ...splitLongPhrase(text.substring(i + 1).trim(), maxLen)\n            ];\n        }\n    }\n\n    // 强制分割时保证不超过maxLen\n    const splitPos = Math.min(maxLen, text.length);\n    return [\n        text.substring(0, splitPos).trim(),\n        ...splitLongPhrase(text.substring(splitPos).trim(), maxLen)\n    ];\n}\n\nconst processSubtitles = (\n    captions,\n    subtitleDurations,\n    startTimeμs = 0 // 新增参数：起始时间（单位微秒，默认0）\n  ) => {\n    const cleanRegex = /[\\u3000\\u3002-\\u303F\\uff00-\\uffef\\u2000-\\u206F!\"#$%&'()*+\\-./<=>?@\\\\^_`{|}~]/g;\n    \n    let processedSubtitles = [];\n    let processedSubtitleDurations = [];\n    \n    captions.forEach((text, index) => {\n      const totalDuration = subtitleDurations[index];\n      let phrases = splitLongPhrase(text, SUB_CONFIG.MAX_LINE_LENGTH);\n      \n      phrases = phrases.map(p => p.replace(cleanRegex, '').trim())\n                     .filter(p => p.length > 0);\n  \n      if (phrases.length === 0) {\n        processedSubtitles.push('[无内容]');\n        processedSubtitleDurations.push(totalDuration);\n        return;\n      }\n  \n      const totalChars = phrases.reduce((sum, p) => sum + p.length, 0);\n      let accumulatedμs = 0;\n      \n      phrases.forEach((phrase, i) => {\n        const ratio = phrase.length / totalChars;\n        let durationμs = i === phrases.length - 1 \n          ? totalDuration - accumulatedμs\n          : Math.round(totalDuration * ratio);\n  \n        processedSubtitles.push(phrase);\n        processedSubtitleDurations.push(durationμs);\n        accumulatedμs += durationμs;\n      });\n    });\n  \n    // 时间轴生成（从指定起始时间开始）\n    const textTimelines = [];\n    let currentTime = startTimeμs; // 使用传入的起始时间\n    \n    processedSubtitleDurations.forEach(durationμs => {\n      const start = currentTime;\n      const end = start + durationμs;\n      \n      textTimelines.push({\n        start: start, // 直接使用整数\n        end: end\n      });\n      \n      currentTime = end; // 自动累计到下一段\n    });\n  \n    return { textTimelines, processedSubtitles };\n  };\n\n// const processSubtitles = (captions, subtitleDurations) => {\n//     // 修改后的正则：保留逗号、顿号等基本标点\n//     const cleanRegex = /[\\u3000\\u3002-\\u303F\\uff00-\\uffef\\u2000-\\u206F!\"#$%&'()*+\\-./<=>?@\\\\^_`{|}~]/g;\n    \n//     let processedSubtitles = [];\n//     let processedSubtitleDurations = [];\n    \n//     captions.forEach((text, index) => {\n//       const totalDuration = subtitleDurations[index]; // 输入单位为微秒\n//       let phrases = splitLongPhrase(text, SUB_CONFIG.MAX_LINE_LENGTH);\n      \n//       // 清理标点时保留分割用标点\n//       phrases = phrases.map(p => p.replace(cleanRegex, '').trim())\n//                      .filter(p => p.length > 0);\n  \n//       if (phrases.length === 0) {\n//         processedSubtitles.push('[无内容]');\n//         processedSubtitleDurations.push(totalDuration); // 直接存储原始微秒\n//         return;\n//       }\n  \n//       // 时间分配逻辑（微秒级别）\n//       const totalChars = phrases.reduce((sum, p) => sum + p.length, 0);\n//       let accumulatedμs = 0;\n      \n//       phrases.forEach((phrase, i) => {\n//         const ratio = phrase.length / totalChars;\n//         let durationμs = i === phrases.length - 1 \n//           ? totalDuration - accumulatedμs  // 最后一段用剩余时间\n//           : Math.round(totalDuration * ratio); // 四舍五入到整数微秒\n  \n//         processedSubtitles.push(phrase);\n//         processedSubtitleDurations.push(durationμs);\n//         accumulatedμs += durationμs;\n//       });\n//     });\n  \n//     // 生成整数时间轴（微秒级别）\n//     const textTimelines = [];\n//     let currentTime = 0; // 基于微秒的整数累加\n    \n//     processedSubtitleDurations.forEach(durationμs => {\n//       const start = currentTime;\n//       const end = start + durationμs; // 自动保持整数\n      \n//       textTimelines.push({\n//         start: start, // 已经是整数\n//         end: end      // 已经是整数\n//       });\n      \n//       currentTime = end;\n//     });\n  \n//     return { textTimelines, processedSubtitles };\n//   };", "language": 5, "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 60000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "audioData", "required": false}, {"type": "string", "name": "imageData", "required": false}, {"type": "list", "name": "text_tim<PERSON>ines", "schema": {"type": "object", "schema": []}, "required": false}, {"type": "list", "name": "text_captions", "schema": {"type": "string"}, "required": false}, {"type": "list", "name": "title_list", "schema": {"type": "string"}, "required": false}, {"type": "list", "name": "title_timelimes", "schema": {"type": "object", "schema": []}, "required": false}, {"type": "string", "name": "bgAudioData", "required": false}, {"type": "string", "name": "kcAudioData", "required": false}, {"type": "string", "name": "roleImgData", "required": false}]}, "_temp": {"bounds": {"x": 1160.3556798722389, "y": 670.3226933175883, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "description": "编写代码，处理输入变量来生成返回值", "title": "代码", "mainColor": "#00B2B2"}}}, {"id": "1165778", "type": "3", "meta": {"position": {"x": -2150.0674762693866, "y": 754.294139402054}}, "data": {"nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "title": "大模型_分镜", "subTitle": "大模型"}, "inputs": {"inputParameters": [{"name": "content", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "121343", "name": "content"}, "rawMeta": {"type": 1}}}}], "llmParam": [{"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": "0.8", "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": "8192", "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": "2", "rawMeta": {"type": 2}}}}, {"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "DeepSeek-V3", "rawMeta": {"type": 1}}}}, {"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": "1738675210", "rawMeta": {"type": 2}}}}, {"name": "generationDiversity", "input": {"type": "string", "value": {"type": "literal", "content": "default_val", "rawMeta": {"type": 1}}}}, {"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "故事原文内容：\n{{content}}", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": "3", "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "# 角色\n你是一位专业的故事创意转化师，你能够深入理解故事文案的情节、人物、场景等元素，用生动且具体的语言为绘画创作提供清晰的指引。\n\n## 技能\n### 技能1： 生成分镜字幕\n1. 当用户提供故事文案时，仔细分析文案中的关键情节、人物形象、场景特点等要素。\n2. 文案分镜， 生成字幕cap：\n    - 字幕文案分段： 第一句单独生成一个分镜，后续每个段落均由2句话构成，语句简洁明了，表达清晰流畅，同时具备节奏感。\n    - 分割文案后特别注意前后文的关联性与一致性，必须与用户提供的原文完全一致，不得进行任何修改、删减。字幕文案必须严格按照用户给的文案拆分，不能修改提供的内容更不能删除内容\n\n\n===回复示例===\n[{\n          \"cap\":\"字幕文案\"\n}]\n===示例结束===\n\n## 限制:\n- 只围绕用户提供的故事文案进行分镜绘画提示词生成和主题提炼，拒绝回答与该任务无关的话题。\n- 所输出的内容必须条理清晰，分镜绘画提示词要尽可能详细描述画面，主题必须为2个字。 \n- 视频文案及分镜描述必须保持一致。\n- 输出内容必须严格按照给定的 JSON 格式进行组织，不得偏离框架要求。\n- 只对用户提示的内容进行分镜，不能更改原文\n- 严格检查 输出的json格式正确性并进行修正，特别注意json格式不要少括号，逗号等", "rawMeta": {"type": 1}}}}], "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "list", "name": "scenes", "schema": {"type": "object", "schema": [{"type": "string", "name": "cap"}]}, "required": false}], "version": "3"}, "_temp": {"bounds": {"x": -2330.0674762693866, "y": 754.294139402054, "width": 360, "height": 166.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "description": "调用大语言模型,使用变量和提示词生成回复", "title": "大模型", "mainColor": "#5C62FF"}}}, {"id": "186126", "type": "3", "meta": {"position": {"x": -1179.68471721277, "y": 745.7936695945779}}, "data": {"nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "title": "大模型_图像提示词", "subTitle": "大模型"}, "inputs": {"inputParameters": [{"name": "scenes", "input": {"type": "list", "schema": {"type": "object", "schema": [{"type": "string", "name": "cap"}]}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "1165778", "name": "scenes"}, "rawMeta": {"type": 103}}}}], "llmParam": [{"name": "generationDiversity", "input": {"type": "string", "value": {"type": "literal", "content": "default_val", "rawMeta": {"type": 1}}}}, {"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": "1", "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": "16384", "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": "2", "rawMeta": {"type": 2}}}}, {"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "DeepSeek-V3-0324", "rawMeta": {"type": 1}}}}, {"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": "1742989917", "rawMeta": {"type": 2}}}}, {"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "故事分镜字幕信息：{{scenes}}", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": "3", "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "# 角色\n根据分镜字幕cap生成绘画提示词desc_prompt。\n\n## 技能\n### 技能 1:  生成绘画提示\n1. 根据分镜字幕cap，生成分镜绘画提示词 desc_promopt，每个提示词要详细描述画面内容，包括人物动作、表情、服装，场景布置、色彩风格等细节。\n  - 风格要求：古代惊悚插画风格，颜色很深，黑暗中，黄昏，氛围凝重，庄严肃穆，构建出紧张氛围，古代服饰，古装，线条粗狂 ，清晰、人物特写，粗狂手笔，高清，高对比度，色彩低饱和，浅景深\n  - 第一个分镜画面中不要出现人物，只需要一个画面背景\n\n===回复示例===\n[\n  {\n    \"cap\": \"字幕文案\",\n    \"desc_promopt\": \"分镜图像提示词\"\n  }\n]\n===示例结束===\n\n## 限制:\n- 只对用户提供的json内容补充desc_prompt字段，不能更改原文\n- 严格检查输出的 json 格式正确性并进行修正，特别注意 json 格式不要少括号，逗号等", "rawMeta": {"type": 1}}}}], "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "list", "name": "scenes", "schema": {"type": "object", "schema": [{"type": "string", "name": "cap"}, {"type": "string", "name": "desc_promopt"}]}, "required": false}], "version": "3"}, "_temp": {"bounds": {"x": -1359.68471721277, "y": 745.7936695945779, "width": 360, "height": 166.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "description": "调用大语言模型,使用变量和提示词生成回复", "title": "大模型", "mainColor": "#5C62FF"}}}, {"id": "168118", "type": "4", "meta": {"position": {"x": 2079.811281710254, "y": 797.7936695945779}}, "data": {"nodeMeta": {"description": "插件入口-创建草稿", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:create_draft", "title": "create_draft"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837955684515874", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "create_draft", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "高", "input": {}, "name": "height", "required": false, "type": "integer"}, {"description": "关联创作者", "input": {}, "name": "user_id", "required": false, "type": "integer"}, {"description": "宽", "input": {}, "name": "width", "required": false, "type": "integer"}], "inputParameters": [{"name": "height", "input": {"type": "integer", "value": {"type": "literal", "content": 1080, "rawMeta": {"type": 2}}}}, {"name": "user_id", "input": {"type": "integer", "value": {"type": "literal", "content": 1262, "rawMeta": {"type": 2}}}}, {"name": "width", "input": {"type": "integer", "value": {"type": "literal", "content": 1440, "rawMeta": {"type": 2}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "draft_url", "required": false}, {"type": "string", "name": "tip_url", "required": false}]}, "_temp": {"bounds": {"x": 1899.8112817102542, "y": 797.7936695945779, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf9-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1739242717113014582_ZXUfNgToeJ.png?lk3s=cd508e2b&x-expires=1750732907&x-signature=fRiKIJ4u2ZL%2BJHkZ1KWZroTg%2BVI%3D", "apiName": "create_draft", "pluginID": "7457837925833801768", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "高", "input": {}, "name": "height", "required": false, "type": "integer"}, {"description": "关联创作者，用来获取推广分成", "input": {}, "name": "user_id", "required": false, "type": "integer"}, {"description": "宽", "input": {}, "name": "width", "required": false, "type": "integer"}], "outputs": [{"input": {}, "name": "draft_url", "required": false, "type": "string"}, {"input": {}, "name": "tip_url", "required": false, "type": "string"}], "updateTime": 1748140539, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "插件入口-创建草稿", "title": "create_draft", "mainColor": "#CA61FF"}}}, {"id": "125268", "type": "4", "meta": {"position": {"x": 2079.811281710254, "y": 1033.8833279050452}}, "data": {"nodeMeta": {"description": "批量添加音频", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:add_audios", "title": "add_audios"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833834536", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_audios", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "[{\"audio_url\": \"http://example.com/audio1.mp3\",\"duration\":120,\"start\":0,\"end\":12000000,\"audio_effect\":\"教堂\"}]", "input": {}, "name": "audio_infos", "required": true, "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}], "inputParameters": [{"name": "audio_infos", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "163547", "name": "audioData"}, "rawMeta": {"type": 1}}}}, {"name": "draft_url", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "168118", "name": "draft_url"}, "rawMeta": {"type": 1}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "list", "name": "audio_ids", "schema": {"type": "string"}, "required": false}, {"type": "string", "name": "draft_url", "required": false}, {"type": "string", "name": "track_id", "required": false}]}, "_temp": {"bounds": {"x": 1899.8112817102542, "y": 1033.8833279050452, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf9-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1739242717113014582_ZXUfNgToeJ.png?lk3s=cd508e2b&x-expires=1750732907&x-signature=fRiKIJ4u2ZL%2BJHkZ1KWZroTg%2BVI%3D", "apiName": "add_audios", "pluginID": "7457837925833801768", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "[{\"audio_url\": \"http://example.com/audio1.mp3\",\"duration\":120,\"start\":0,\"end\":12000000,\"audio_effect\":\"教堂\"}]", "input": {}, "name": "audio_infos", "required": true, "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}], "outputs": [{"input": {}, "name": "track_id", "required": false, "type": "string"}, {"input": {}, "name": "audio_ids", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"input": {}, "name": "draft_url", "required": false, "type": "string"}], "updateTime": 1748140539, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "批量添加音频", "title": "add_audios", "mainColor": "#CA61FF"}}}, {"id": "180947", "type": "4", "meta": {"position": {"x": 3065.632562327633, "y": 1206.8599092414274}}, "data": {"nodeMeta": {"description": "根据时间线制作字幕数据", "icon": "https://p9-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/6a57866c6eca488eb6a78e3cdb16e668~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1744465010&x-signature=4iDDzKiufQ2Axl4wsESVJu8bc4A%3D", "subtitle": "剪映小助手数据生成器:caption_infos", "title": "caption_infos"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7475829177439191075", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "caption_infos", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7475829177439109155", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "剪映小助手数据生成器", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "文本列表", "input": {}, "name": "texts", "required": true, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"description": "对应剪映的入场动画名字，多个动画请用英文|分割，比如：飞入|放大", "input": {}, "name": "in_animation", "required": false, "type": "string"}, {"description": "关键词颜色", "input": {}, "name": "keyword_color", "required": false, "type": "string"}, {"description": "循环动画时长", "input": {}, "name": "loop_animation_duration", "required": false, "type": "integer"}, {"description": "出场动画时长", "input": {}, "name": "out_animation_duration", "required": false, "type": "integer"}, {"description": "对应剪映的循环动画名字，多个动画请用英文|分割，比如：扫光|晃动", "input": {}, "name": "loop_animation", "required": false, "type": "string"}, {"description": "对应剪映的出场动画名字，多个动画请用英文|分割，比如：消散|闭幕", "input": {}, "name": "out_animation", "required": false, "type": "string"}, {"description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取", "input": {}, "name": "timelines", "required": true, "schema": {"schema": [{"description": "结束时间", "input": {}, "name": "end", "required": true, "type": "integer"}, {"description": "开始时间", "input": {}, "name": "start", "required": true, "type": "integer"}], "type": "object"}, "type": "list"}, {"description": "文字大小", "input": {}, "name": "font_size", "required": false, "type": "integer"}, {"description": "入场动画时长", "input": {}, "name": "in_animation_duration", "required": false, "type": "integer"}, {"description": "关键词字大小", "input": {}, "name": "keyword_font_size", "required": false, "type": "integer"}, {"description": "文本里面的重点词列表", "input": {}, "name": "keywords", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}], "inputParameters": [{"name": "texts", "input": {"type": "list", "schema": {"type": "string"}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "163547", "name": "text_captions"}, "rawMeta": {"type": 99}}}}, {"name": "timelines", "input": {"type": "list", "schema": {"type": "object", "schema": []}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "163547", "name": "text_tim<PERSON>ines"}, "rawMeta": {"type": 103}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "infos", "required": false}]}, "_temp": {"bounds": {"x": 2885.632562327633, "y": 1206.8599092414274, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf9-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1740601667083827169_qvIu8uOxyr.jpeg?lk3s=cd508e2b&x-expires=1750732907&x-signature=7s5pOLnF6TIG3VU2lU5wwY5iSVU%3D", "apiName": "caption_infos", "pluginID": "7475829177439109155", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "文字大小", "input": {}, "name": "font_size", "required": false, "type": "integer"}, {"description": "对应剪映的入场动画名字，多个动画请用英文|分割，比如：飞入|放大", "input": {}, "name": "in_animation", "required": false, "type": "string"}, {"description": "入场动画时长", "input": {}, "name": "in_animation_duration", "required": false, "type": "integer"}, {"description": "关键词颜色", "input": {}, "name": "keyword_color", "required": false, "type": "string"}, {"description": "对应剪映的循环动画名字，多个动画请用英文|分割，比如：扫光|晃动", "input": {}, "name": "loop_animation", "required": false, "type": "string"}, {"description": "循环动画时长", "input": {}, "name": "loop_animation_duration", "required": false, "type": "integer"}, {"description": "对应剪映的出场动画名字，多个动画请用英文|分割，比如：消散|闭幕", "input": {}, "name": "out_animation", "required": false, "type": "string"}, {"description": "关键词字大小", "input": {}, "name": "keyword_font_size", "required": false, "type": "integer"}, {"description": "文本里面的重点词列表", "input": {}, "name": "keywords", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"description": "出场动画时长", "input": {}, "name": "out_animation_duration", "required": false, "type": "integer"}, {"description": "文本列表", "input": {}, "name": "texts", "required": true, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取", "input": {}, "name": "timelines", "required": true, "schema": {"schema": [{"description": "结束时间", "input": {}, "name": "end", "required": true, "type": "integer"}, {"description": "开始时间", "input": {}, "name": "start", "required": true, "type": "integer"}], "type": "object"}, "type": "list"}], "outputs": [{"input": {}, "name": "infos", "required": false, "type": "string"}], "updateTime": 1748103709, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "根据时间线制作字幕数据", "title": "caption_infos", "mainColor": "#CA61FF"}}}, {"id": "109941", "type": "4", "meta": {"position": {"x": 2079.811281710254, "y": 1288.6731571707974}}, "data": {"nodeMeta": {"description": "批量添加图片", "icon": "https://p6-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/324427c7589a426286a9774fa69f64a2~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1744472328&x-signature=9GXsgm2HH29uW0ihgoymUTYxCmc%3D", "subtitle": "视频合成_剪映小助手:add_images", "title": "add_images"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833883688", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_images", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/Pgm9wXA4EipKhYkeEQJcLBlJnWb?from=from_copylink", "input": {}, "name": "image_infos", "required": true, "type": "string"}, {"description": "x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "移动transform_x", "input": {}, "name": "transform_x", "required": false, "type": "float"}, {"description": "移动transform_y", "input": {}, "name": "transform_y", "required": false, "type": "float"}], "inputParameters": [{"name": "draft_url", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "168118", "name": "draft_url"}, "rawMeta": {"type": 1}}}}, {"name": "image_infos", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "163547", "name": "imageData"}, "rawMeta": {"type": 1}}}}, {"name": "scale_x", "input": {"type": "float", "value": {"type": "literal", "content": 1, "rawMeta": {"type": 4}}}}, {"name": "scale_y", "input": {"type": "float", "value": {"type": "literal", "content": 1, "rawMeta": {"type": 4}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "list", "name": "segment_infos", "schema": {"type": "object", "schema": [{"type": "integer", "name": "end", "required": false}, {"type": "string", "name": "id", "required": false}, {"type": "integer", "name": "start", "required": false}]}, "required": false}, {"type": "string", "name": "track_id", "required": false}, {"type": "string", "name": "draft_url", "required": false}, {"type": "list", "name": "image_ids", "schema": {"type": "string"}, "required": false}, {"type": "list", "name": "segment_ids", "schema": {"type": "string"}, "required": false}]}, "_temp": {"bounds": {"x": 1899.8112817102542, "y": 1288.6731571707974, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf26-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1739242717113014582_ZXUfNgToeJ.png?lk3s=cd508e2b&x-expires=1750732907&x-signature=ipkoaSNLzYRZlG8%2B%2FzNj4eq19so%3D", "apiName": "add_images", "pluginID": "7457837925833801768", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "移动transform_y", "input": {}, "name": "transform_y", "required": false, "type": "float"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/Pgm9wXA4EipKhYkeEQJcLBlJnWb?from=from_copylink", "input": {}, "name": "image_infos", "required": true, "type": "string"}, {"description": "x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "移动transform_x", "input": {}, "name": "transform_x", "required": false, "type": "float"}], "outputs": [{"input": {}, "name": "draft_url", "required": false, "type": "string"}, {"input": {}, "name": "image_ids", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"input": {}, "name": "segment_ids", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"input": {}, "name": "segment_infos", "required": false, "schema": {"schema": [{"input": {}, "name": "id", "required": false, "type": "string"}, {"input": {}, "name": "start", "required": false, "type": "integer"}, {"input": {}, "name": "end", "required": false, "type": "integer"}], "type": "object"}, "type": "list"}, {"input": {}, "name": "track_id", "required": false, "type": "string"}], "updateTime": 1748140539, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "批量添加图片", "title": "add_images", "mainColor": "#CA61FF"}}}, {"id": "158201", "type": "4", "meta": {"position": {"x": 3051.9781163850516, "y": 1646.3139266906142}}, "data": {"nodeMeta": {"description": "批量添加字幕", "icon": "https://p26-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/324427c7589a426286a9774fa69f64a2~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1744464780&x-signature=qS0o%2FY1NDMFTjtJAQ%2FHNSTjQEzw%3D", "subtitle": "视频合成_剪映小助手:add_captions", "title": "add_captions"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833850920", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_captions", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "scale_x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "scale_y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "文字颜色：#ff1837", "input": {}, "name": "text_color", "required": false, "type": "string"}, {"description": "transform_x位置", "input": {}, "name": "transform_x", "required": false, "type": "float"}, {"description": "transform_y位置", "input": {}, "name": "transform_y", "required": false, "type": "float"}, {"description": "字幕的对齐方式，0左对齐，1 居中对齐，2右对齐", "input": {}, "name": "alignment", "required": false, "type": "integer"}, {"description": "字间距，默认0", "input": {}, "name": "letter_spacing", "required": false, "type": "float"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "字体列表：https://krxc4izye0.feishu.cn/wiki/SmnrwabXriG7JckEzyGcChk4nDd", "input": {}, "name": "font", "required": false, "type": "string"}, {"description": "默认：15", "input": {}, "name": "font_size", "required": false, "type": "integer"}, {"description": "行间距，默认0", "input": {}, "name": "line_spacing", "required": false, "type": "float"}, {"description": "0 默认。1富文本样式", "input": {}, "name": "style_text", "required": false, "type": "integer"}, {"description": "边框颜色，eg：#fe8a80", "input": {}, "name": "border_color", "required": false, "type": "string"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/HhQrw3BFhi1XGOkkJCBcfkqGnwf?from=from_copylink", "input": {}, "name": "captions", "required": true, "type": "string"}], "inputParameters": [{"name": "captions", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "180947", "name": "infos"}, "rawMeta": {"type": 1}}}}, {"name": "draft_url", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "168118", "name": "draft_url"}, "rawMeta": {"type": 1}}}}, {"name": "alignment", "input": {"type": "integer", "value": {"type": "literal", "content": 1, "rawMeta": {"type": 2}}}}, {"name": "border_color", "input": {"type": "string", "value": {"type": "literal", "content": "#000000", "rawMeta": {"type": 1}}}}, {"name": "font_size", "input": {"type": "integer", "value": {"type": "literal", "content": 7, "rawMeta": {"type": 2}}}}, {"name": "text_color", "input": {"type": "string", "value": {"type": "literal", "content": "#FFFFFF", "rawMeta": {"type": 1}}}}, {"name": "transform_x", "input": {"type": "float", "value": {"type": "literal", "content": 0, "rawMeta": {"type": 4}}}}, {"name": "transform_y", "input": {"type": "float", "value": {"type": "literal", "content": -810, "rawMeta": {"type": 4}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "draft_url", "required": false}, {"type": "list", "name": "segment_ids", "schema": {"type": "string"}, "required": false}, {"type": "list", "name": "segment_infos", "schema": {"type": "object", "schema": [{"type": "integer", "name": "end", "required": false}, {"type": "string", "name": "id", "required": false}, {"type": "integer", "name": "start", "required": false}]}, "required": false}, {"type": "list", "name": "text_ids", "schema": {"type": "string"}, "required": false}, {"type": "string", "name": "track_id", "required": false}]}, "_temp": {"bounds": {"x": 2871.9781163850516, "y": 1646.3139266906142, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf26-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1739242717113014582_ZXUfNgToeJ.png?lk3s=cd508e2b&x-expires=1750732907&x-signature=ipkoaSNLzYRZlG8%2B%2FzNj4eq19so%3D", "apiName": "add_captions", "pluginID": "7457837925833801768", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "默认：15", "input": {}, "name": "font_size", "required": false, "type": "integer"}, {"description": "行间距，默认0", "input": {}, "name": "line_spacing", "required": false, "type": "float"}, {"description": "scale_y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "文字颜色：#ff1837", "input": {}, "name": "text_color", "required": false, "type": "string"}, {"description": "字幕的对齐方式，0左对齐，1 居中对齐，2右对齐", "input": {}, "name": "alignment", "required": false, "type": "integer"}, {"description": "边框颜色，eg：#fe8a80", "input": {}, "name": "border_color", "required": false, "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "字体列表：https://krxc4izye0.feishu.cn/wiki/SmnrwabXriG7JckEzyGcChk4nDd", "input": {}, "name": "font", "required": false, "type": "string"}, {"description": "transform_x位置", "input": {}, "name": "transform_x", "required": false, "type": "float"}, {"description": "transform_y位置", "input": {}, "name": "transform_y", "required": false, "type": "float"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/HhQrw3BFhi1XGOkkJCBcfkqGnwf?from=from_copylink", "input": {}, "name": "captions", "required": true, "type": "string"}, {"description": "字间距，默认0", "input": {}, "name": "letter_spacing", "required": false, "type": "float"}, {"description": "scale_x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "0 默认。1富文本样式", "input": {}, "name": "style_text", "required": false, "type": "integer"}], "outputs": [{"input": {}, "name": "draft_url", "required": false, "type": "string"}, {"input": {}, "name": "segment_ids", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"input": {}, "name": "segment_infos", "required": false, "schema": {"schema": [{"input": {}, "name": "end", "required": false, "type": "integer"}, {"input": {}, "name": "id", "required": false, "type": "string"}, {"input": {}, "name": "start", "required": false, "type": "integer"}], "type": "object"}, "type": "list"}, {"input": {}, "name": "text_ids", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"input": {}, "name": "track_id", "required": false, "type": "string"}], "updateTime": 1748140539, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "批量添加字幕", "title": "add_captions", "mainColor": "#CA61FF"}}}, {"id": "104782", "type": "4", "meta": {"position": {"x": 3932.2402531050725, "y": 797.7936695945779}}, "data": {"nodeMeta": {"description": "保存草稿\n", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:save_draft", "title": "save_draft"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837955684548642", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "save_draft", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "用户ID。如果填写了这个ID，新用户产生的月费就会按照比例归属到这个账号下。", "input": {}, "name": "user_id", "required": false, "type": "integer"}], "inputParameters": [{"name": "draft_url", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "168118", "name": "draft_url"}, "rawMeta": {"type": 1}}}}, {"name": "user_id", "input": {"type": "integer", "value": {"type": "literal", "content": 1262, "rawMeta": {"type": 2}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "draft_url", "required": false}, {"type": "string", "name": "message", "required": false}]}, "_temp": {"bounds": {"x": 3752.2402531050725, "y": 797.7936695945779, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf9-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1739242717113014582_ZXUfNgToeJ.png?lk3s=cd508e2b&x-expires=1750732907&x-signature=fRiKIJ4u2ZL%2BJHkZ1KWZroTg%2BVI%3D", "apiName": "save_draft", "pluginID": "7457837925833801768", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "用户ID。如果填写了这个ID，新用户产生的月费就会按照比例归属到这个账号下。", "input": {}, "name": "user_id", "required": false, "type": "integer"}], "outputs": [{"input": {}, "name": "draft_url", "required": false, "type": "string"}, {"input": {}, "name": "message", "required": false, "type": "string"}], "updateTime": 1748140539, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "保存草稿\n", "title": "save_draft", "mainColor": "#CA61FF"}}}, {"id": "1204256", "type": "4", "meta": {"position": {"x": 3065.632562327632, "y": 1412.5617897495574}}, "data": {"nodeMeta": {"description": "根据时间线制作字幕数据", "icon": "https://p9-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/6a57866c6eca488eb6a78e3cdb16e668~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1744465010&x-signature=4iDDzKiufQ2Axl4wsESVJu8bc4A%3D", "subtitle": "剪映小助手数据生成器:caption_infos", "title": "caption_infos_标题2个字"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7475829177439191075", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "caption_infos", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7475829177439109155", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "剪映小助手数据生成器", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "文本列表", "input": {}, "name": "texts", "required": true, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"description": "对应剪映的入场动画名字，多个动画请用英文|分割，比如：飞入|放大", "input": {}, "name": "in_animation", "required": false, "type": "string"}, {"description": "关键词颜色", "input": {}, "name": "keyword_color", "required": false, "type": "string"}, {"description": "循环动画时长", "input": {}, "name": "loop_animation_duration", "required": false, "type": "integer"}, {"description": "出场动画时长", "input": {}, "name": "out_animation_duration", "required": false, "type": "integer"}, {"description": "对应剪映的循环动画名字，多个动画请用英文|分割，比如：扫光|晃动", "input": {}, "name": "loop_animation", "required": false, "type": "string"}, {"description": "对应剪映的出场动画名字，多个动画请用英文|分割，比如：消散|闭幕", "input": {}, "name": "out_animation", "required": false, "type": "string"}, {"description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取", "input": {}, "name": "timelines", "required": true, "schema": {"schema": [{"description": "结束时间", "input": {}, "name": "end", "required": true, "type": "integer"}, {"description": "开始时间", "input": {}, "name": "start", "required": true, "type": "integer"}], "type": "object"}, "type": "list"}, {"description": "文字大小", "input": {}, "name": "font_size", "required": false, "type": "integer"}, {"description": "入场动画时长", "input": {}, "name": "in_animation_duration", "required": false, "type": "integer"}, {"description": "关键词字大小", "input": {}, "name": "keyword_font_size", "required": false, "type": "integer"}, {"description": "文本里面的重点词列表", "input": {}, "name": "keywords", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}], "inputParameters": [{"name": "texts", "input": {"type": "list", "schema": {"type": "string"}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "163547", "name": "title_list"}, "rawMeta": {"type": 99}}}}, {"name": "timelines", "input": {"type": "list", "schema": {"type": "object", "schema": []}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "163547", "name": "title_timelimes"}, "rawMeta": {"type": 103}}}}, {"name": "in_animation", "input": {"type": "string", "value": {"type": "literal", "content": "弹入", "rawMeta": {"type": 1}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "infos", "required": false}]}, "_temp": {"bounds": {"x": 2885.632562327632, "y": 1412.5617897495574, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf9-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1740601667083827169_qvIu8uOxyr.jpeg?lk3s=cd508e2b&x-expires=1750732907&x-signature=7s5pOLnF6TIG3VU2lU5wwY5iSVU%3D", "apiName": "caption_infos", "pluginID": "7475829177439109155", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "文字大小", "input": {}, "name": "font_size", "required": false, "type": "integer"}, {"description": "对应剪映的入场动画名字，多个动画请用英文|分割，比如：飞入|放大", "input": {}, "name": "in_animation", "required": false, "type": "string"}, {"description": "入场动画时长", "input": {}, "name": "in_animation_duration", "required": false, "type": "integer"}, {"description": "关键词颜色", "input": {}, "name": "keyword_color", "required": false, "type": "string"}, {"description": "对应剪映的循环动画名字，多个动画请用英文|分割，比如：扫光|晃动", "input": {}, "name": "loop_animation", "required": false, "type": "string"}, {"description": "循环动画时长", "input": {}, "name": "loop_animation_duration", "required": false, "type": "integer"}, {"description": "对应剪映的出场动画名字，多个动画请用英文|分割，比如：消散|闭幕", "input": {}, "name": "out_animation", "required": false, "type": "string"}, {"description": "关键词字大小", "input": {}, "name": "keyword_font_size", "required": false, "type": "integer"}, {"description": "文本里面的重点词列表", "input": {}, "name": "keywords", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"description": "出场动画时长", "input": {}, "name": "out_animation_duration", "required": false, "type": "integer"}, {"description": "文本列表", "input": {}, "name": "texts", "required": true, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取", "input": {}, "name": "timelines", "required": true, "schema": {"schema": [{"description": "结束时间", "input": {}, "name": "end", "required": true, "type": "integer"}, {"description": "开始时间", "input": {}, "name": "start", "required": true, "type": "integer"}], "type": "object"}, "type": "list"}], "outputs": [{"input": {}, "name": "infos", "required": false, "type": "string"}], "updateTime": 1748103709, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "根据时间线制作字幕数据", "title": "caption_infos", "mainColor": "#CA61FF"}}}, {"id": "1182713", "type": "4", "meta": {"position": {"x": 3039.798799764117, "y": 1882.8421321021997}}, "data": {"nodeMeta": {"description": "批量添加字幕", "icon": "https://p26-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/324427c7589a426286a9774fa69f64a2~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1744464780&x-signature=qS0o%2FY1NDMFTjtJAQ%2FHNSTjQEzw%3D", "subtitle": "视频合成_剪映小助手:add_captions", "title": "add_captions_开场2个字"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833850920", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_captions", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "scale_x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "scale_y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "文字颜色：#ff1837", "input": {}, "name": "text_color", "required": false, "type": "string"}, {"description": "transform_x位置", "input": {}, "name": "transform_x", "required": false, "type": "float"}, {"description": "transform_y位置", "input": {}, "name": "transform_y", "required": false, "type": "float"}, {"description": "字幕的对齐方式，0左对齐，1 居中对齐，2右对齐", "input": {}, "name": "alignment", "required": false, "type": "integer"}, {"description": "字间距，默认0", "input": {}, "name": "letter_spacing", "required": false, "type": "float"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "字体列表：https://krxc4izye0.feishu.cn/wiki/SmnrwabXriG7JckEzyGcChk4nDd", "input": {}, "name": "font", "required": false, "type": "string"}, {"description": "默认：15", "input": {}, "name": "font_size", "required": false, "type": "integer"}, {"description": "行间距，默认0", "input": {}, "name": "line_spacing", "required": false, "type": "float"}, {"description": "0 默认。1富文本样式", "input": {}, "name": "style_text", "required": false, "type": "integer"}, {"description": "边框颜色，eg：#fe8a80", "input": {}, "name": "border_color", "required": false, "type": "string"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/HhQrw3BFhi1XGOkkJCBcfkqGnwf?from=from_copylink", "input": {}, "name": "captions", "required": true, "type": "string"}], "inputParameters": [{"name": "captions", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "1204256", "name": "infos"}, "rawMeta": {"type": 1}}}}, {"name": "draft_url", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "168118", "name": "draft_url"}, "rawMeta": {"type": 1}}}}, {"name": "alignment", "input": {"type": "integer", "value": {"type": "literal", "content": 1, "rawMeta": {"type": 2}}}}, {"name": "border_color", "input": {"type": "string", "value": {"type": "literal", "content": "#000000", "rawMeta": {"type": 1}}}}, {"name": "font", "input": {"type": "string", "value": {"type": "literal", "content": "梦桃体", "rawMeta": {"type": 1}}}}, {"name": "font_size", "input": {"type": "integer", "value": {"type": "literal", "content": 40, "rawMeta": {"type": 2}}}}, {"name": "letter_spacing", "input": {"type": "float", "value": {"type": "literal", "content": 26, "rawMeta": {"type": 4}}}}, {"name": "text_color", "input": {"type": "string", "value": {"type": "literal", "content": "#FFFFFF", "rawMeta": {"type": 1}}}}, {"name": "transform_x", "input": {"type": "float", "value": {"type": "literal", "content": 0, "rawMeta": {"type": 4}}}}, {"name": "transform_y", "input": {"type": "float", "value": {"type": "literal", "content": 0, "rawMeta": {"type": 4}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "draft_url", "required": false}, {"type": "list", "name": "segment_ids", "schema": {"type": "string"}, "required": false}, {"type": "list", "name": "segment_infos", "schema": {"type": "object", "schema": [{"type": "integer", "name": "end", "required": false}, {"type": "string", "name": "id", "required": false}, {"type": "integer", "name": "start", "required": false}]}, "required": false}, {"type": "list", "name": "text_ids", "schema": {"type": "string"}, "required": false}, {"type": "string", "name": "track_id", "required": false}]}, "_temp": {"bounds": {"x": 2859.798799764117, "y": 1882.8421321021997, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf26-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1739242717113014582_ZXUfNgToeJ.png?lk3s=cd508e2b&x-expires=1750732907&x-signature=ipkoaSNLzYRZlG8%2B%2FzNj4eq19so%3D", "apiName": "add_captions", "pluginID": "7457837925833801768", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "默认：15", "input": {}, "name": "font_size", "required": false, "type": "integer"}, {"description": "行间距，默认0", "input": {}, "name": "line_spacing", "required": false, "type": "float"}, {"description": "scale_y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "文字颜色：#ff1837", "input": {}, "name": "text_color", "required": false, "type": "string"}, {"description": "字幕的对齐方式，0左对齐，1 居中对齐，2右对齐", "input": {}, "name": "alignment", "required": false, "type": "integer"}, {"description": "边框颜色，eg：#fe8a80", "input": {}, "name": "border_color", "required": false, "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "字体列表：https://krxc4izye0.feishu.cn/wiki/SmnrwabXriG7JckEzyGcChk4nDd", "input": {}, "name": "font", "required": false, "type": "string"}, {"description": "transform_x位置", "input": {}, "name": "transform_x", "required": false, "type": "float"}, {"description": "transform_y位置", "input": {}, "name": "transform_y", "required": false, "type": "float"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/HhQrw3BFhi1XGOkkJCBcfkqGnwf?from=from_copylink", "input": {}, "name": "captions", "required": true, "type": "string"}, {"description": "字间距，默认0", "input": {}, "name": "letter_spacing", "required": false, "type": "float"}, {"description": "scale_x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "0 默认。1富文本样式", "input": {}, "name": "style_text", "required": false, "type": "integer"}], "outputs": [{"input": {}, "name": "draft_url", "required": false, "type": "string"}, {"input": {}, "name": "segment_ids", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"input": {}, "name": "segment_infos", "required": false, "schema": {"schema": [{"input": {}, "name": "end", "required": false, "type": "integer"}, {"input": {}, "name": "id", "required": false, "type": "string"}, {"input": {}, "name": "start", "required": false, "type": "integer"}], "type": "object"}, "type": "list"}, {"input": {}, "name": "text_ids", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"input": {}, "name": "track_id", "required": false, "type": "string"}], "updateTime": 1748140539, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "批量添加字幕", "title": "add_captions", "mainColor": "#CA61FF"}}}, {"id": "1114402", "type": "4", "meta": {"position": {"x": 2079.811281710254, "y": 1799.5141820183585}}, "data": {"nodeMeta": {"description": "批量添加音频", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:add_audios", "title": "add_audios_开场音效"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833834536", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_audios", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "[{\"audio_url\": \"http://example.com/audio1.mp3\",\"duration\":120,\"start\":0,\"end\":12000000,\"audio_effect\":\"教堂\"}]", "input": {}, "name": "audio_infos", "required": true, "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}], "inputParameters": [{"name": "audio_infos", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "163547", "name": "kcAudioData"}, "rawMeta": {"type": 1}}}}, {"name": "draft_url", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "168118", "name": "draft_url"}, "rawMeta": {"type": 1}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "list", "name": "audio_ids", "schema": {"type": "string"}, "required": false}, {"type": "string", "name": "draft_url", "required": false}, {"type": "string", "name": "track_id", "required": false}]}, "_temp": {"bounds": {"x": 1899.8112817102542, "y": 1799.5141820183585, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf9-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1739242717113014582_ZXUfNgToeJ.png?lk3s=cd508e2b&x-expires=1750732907&x-signature=fRiKIJ4u2ZL%2BJHkZ1KWZroTg%2BVI%3D", "apiName": "add_audios", "pluginID": "7457837925833801768", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "[{\"audio_url\": \"http://example.com/audio1.mp3\",\"duration\":120,\"start\":0,\"end\":12000000,\"audio_effect\":\"教堂\"}]", "input": {}, "name": "audio_infos", "required": true, "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}], "outputs": [{"input": {}, "name": "track_id", "required": false, "type": "string"}, {"input": {}, "name": "audio_ids", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"input": {}, "name": "draft_url", "required": false, "type": "string"}], "updateTime": 1748140539, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "批量添加音频", "title": "add_audios", "mainColor": "#CA61FF"}}}, {"id": "1190196", "type": "4", "meta": {"position": {"x": 2079.811281710254, "y": 1996.9421321021996}}, "data": {"nodeMeta": {"description": "批量添加音频", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:add_audios", "title": "add_audios_背景音乐"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833834536", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_audios", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "[{\"audio_url\": \"http://example.com/audio1.mp3\",\"duration\":120,\"start\":0,\"end\":12000000,\"audio_effect\":\"教堂\"}]", "input": {}, "name": "audio_infos", "required": true, "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}], "inputParameters": [{"name": "audio_infos", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "163547", "name": "bgAudioData"}, "rawMeta": {"type": 1}}}}, {"name": "draft_url", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "168118", "name": "draft_url"}, "rawMeta": {"type": 1}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "list", "name": "audio_ids", "schema": {"type": "string"}, "required": false}, {"type": "string", "name": "draft_url", "required": false}, {"type": "string", "name": "track_id", "required": false}]}, "_temp": {"bounds": {"x": 1899.8112817102542, "y": 1996.9421321021996, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf9-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1739242717113014582_ZXUfNgToeJ.png?lk3s=cd508e2b&x-expires=1750732907&x-signature=fRiKIJ4u2ZL%2BJHkZ1KWZroTg%2BVI%3D", "apiName": "add_audios", "pluginID": "7457837925833801768", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "[{\"audio_url\": \"http://example.com/audio1.mp3\",\"duration\":120,\"start\":0,\"end\":12000000,\"audio_effect\":\"教堂\"}]", "input": {}, "name": "audio_infos", "required": true, "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}], "outputs": [{"input": {}, "name": "track_id", "required": false, "type": "string"}, {"input": {}, "name": "audio_ids", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"input": {}, "name": "draft_url", "required": false, "type": "string"}], "updateTime": 1748140539, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "批量添加音频", "title": "add_audios", "mainColor": "#CA61FF"}}}, {"id": "120984", "type": "5", "meta": {"position": {"x": 3065.632562327633, "y": 774.4184559004722}}, "data": {"nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "title": "代码_关键帧", "subTitle": "代码"}, "inputs": {"inputParameters": [{"name": "segment_ids", "input": {"type": "list", "schema": {"type": "string"}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "109941", "name": "segment_ids"}, "rawMeta": {"type": 99}}}}, {"name": "duration_list", "input": {"type": "list", "schema": {"type": "integer"}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "121555", "name": "duration_list"}, "rawMeta": {"type": 100}}}}, {"name": "segment_infos", "input": {"type": "list", "schema": {"type": "object", "schema": [{"type": "integer", "name": "end", "required": false}, {"type": "string", "name": "id", "required": false}, {"type": "integer", "name": "start", "required": false}]}, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "1087428", "name": "segment_infos"}, "rawMeta": {"type": 103}}}}], "code": "import json\n\nasync def main(args: Args) -> Output:\n    params = args.params\n    segment_ids = params['segment_ids']\n    times = params['duration_list']\n    seg = params['segment_infos']\n    \n    # 验证参数长度一致性\n    if len(segment_ids) != len(times):\n        raise ValueError(\"segment_ids与times数组长度不一致\")\n    \n    keyframes = []\n    \n    for idx, seg_id in enumerate(segment_ids):\n        if idx == 0:  # 跳过第一张图片\n            continue\n        \n        # 获取对应音频时长并转换微秒\n        audio_duration = int(float(times[idx]))\n        \n        # 根据循环索引决定缩放方向\n        cycle_idx = idx - 1  # 计算排除第一个元素后的循环索引\n        if cycle_idx % 2 == 0:  # 偶数索引：1.0 -> 1.5\n            start_scale = 1.0\n            end_scale = 1.5\n        else:  # 奇数索引：1.5 -> 1.0\n            start_scale = 1.5\n            end_scale = 1.0\n        \n        # 起始关键帧（0秒位置）\n        keyframes.append({\n            \"offset\": 0,\n            \"property\": \"UNIFORM_SCALE\",\n            \"segment_id\": seg_id,\n            \"value\": start_scale,\n            \"easing\": \"linear\"\n        })\n        \n        # 结束关键帧（同步音频时长）\n        keyframes.append({\n            \"offset\": audio_duration,  # 使用实际音频时长\n            \"property\": \"UNIFORM_SCALE\",\n            \"segment_id\": seg_id,\n            \"value\": end_scale,\n            \"easing\": \"linear\"\n        })\n    \n    \n    # 起始关键帧（0秒位置）\n    keyframes.append({\n        \"offset\": 0,\n        \"property\": \"UNIFORM_SCALE\",\n        \"segment_id\": seg[0]['id'],\n        \"value\": 2,\n        \"easing\": \"linear\"\n    })\n\n    keyframes.append({\n        \"offset\": 533333,\n        \"property\": \"UNIFORM_SCALE\",\n        \"segment_id\": seg[0]['id'],\n        \"value\": 1.2,\n        \"easing\": \"linear\"\n    })\n    \n        \n    # 结束关键帧（同步音频时长）\n    keyframes.append({\n        \"offset\": seg[0]['end']-seg[0]['start'],  # 使用实际音频时长\n        \"property\": \"UNIFORM_SCALE\",\n        \"segment_id\": seg[0]['id'],\n        \"value\": 1.0,\n        \"easing\": \"linear\"\n    })\n    \n\n    return {\n        \"keyFrames\": json.dumps(keyframes)\n    }\n\n", "language": 3, "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 60000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "keyFrames", "required": false}]}, "_temp": {"bounds": {"x": 2885.632562327633, "y": 774.4184559004722, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "description": "编写代码，处理输入变量来生成返回值", "title": "代码", "mainColor": "#00B2B2"}}}, {"id": "146723", "type": "4", "meta": {"position": {"x": 3065.632562327633, "y": 984.7953791474233}}, "data": {"nodeMeta": {"description": "添加关键帧", "icon": "https://p6-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/f013e4af74224258a37363a531aecefe~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1746810641&x-signature=nre7vy5iSw43%2Bkt5noyLIVGGEq4%3D", "subtitle": "视频合成_剪映小助手:add_keyframes", "title": "add_keyframes"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7465608338500452404", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_keyframes", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputParameters": [{"name": "draft_url", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "168118", "name": "draft_url"}, "rawMeta": {"type": 1}}}}, {"name": "keyframes", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "120984", "name": "keyFrames"}, "rawMeta": {"type": 1}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "draft_url", "required": false}]}, "_temp": {"bounds": {"x": 2885.632562327633, "y": 984.7953791474233, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf6-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1739242717113014582_ZXUfNgToeJ.png?lk3s=cd508e2b&x-expires=1750732907&x-signature=Sg4kAIQTT%2Bn0ATxzr%2FiUXeTGir0%3D", "apiName": "add_keyframes", "pluginID": "7457837925833801768", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "关键帧数据，格式： [     {       \"offset\": 5000000,       \"property\": \"KFTypePositionX\",       \"segment_id\": \"acc5b516-046b-4eae-a179-f686f35e70a8\",       \"value\": 0     }   ]", "input": {}, "name": "keyframes", "required": true, "type": "string"}], "outputs": [{"input": {}, "name": "draft_url", "required": false, "type": "string"}], "updateTime": 1748140539, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "添加关键帧", "title": "add_keyframes", "mainColor": "#CA61FF"}}}, {"id": "1301843", "type": "3", "meta": {"position": {"x": -1800.1887182897458, "y": 1650.093669594578}}, "data": {"nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "title": "大模型_主角首图", "subTitle": "大模型"}, "inputs": {"inputParameters": [{"name": "scenes", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "121343", "name": "content"}, "rawMeta": {"type": 1}}}}], "llmParam": [{"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": "0.8", "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": "8192", "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": "2", "rawMeta": {"type": 2}}}}, {"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "DeepSeek-V3", "rawMeta": {"type": 1}}}}, {"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": "1738675210", "rawMeta": {"type": 2}}}}, {"name": "generationDiversity", "input": {"type": "string", "value": {"type": "literal", "content": "default_val", "rawMeta": {"type": 1}}}}, {"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "故事信息：{{scenes}}", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": "3", "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "# 角色\n根据故事信息生成故事主角开场绘画提示词desc_prompt。\n\n## 技能\n### 技能 1:  生成绘画提示\n1. 根据故事信息，生成主角任务绘画提示词 desc_promopt，详细描述人物动作、表情、服装，色彩风格等细节。\n  - 风格要求：古代惊悚插画风格， 背景留白，颜色昏暗，黑暗中，黄昏，氛围凝重，庄严肃穆，构建出紧张氛围，古代服饰，古装，线条粗狂 ，清晰、人物特写，粗狂手笔，高清，高对比度，色彩低饱和，浅景深\n  - 画面只需要出现一个人物，背景留白\n  - 人物需正对屏幕，人物在画面正中间\n\n\n# 限制\n1. 只输出绘画提示词，不要输出其他额外内容", "rawMeta": {"type": 1}}}}], "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "desc_promopt", "required": false}], "version": "3"}, "_temp": {"bounds": {"x": -1980.1887182897458, "y": 1650.093669594578, "width": 360, "height": 166.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "description": "调用大语言模型,使用变量和提示词生成回复", "title": "大模型", "mainColor": "#5C62FF"}}}, {"id": "1866199", "type": "16", "meta": {"position": {"x": -1340.1887182897458, "y": 1650.093669594578}}, "data": {"inputs": {"apiParam": null, "inputParameters": [{"name": "desc_promopt", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "1301843", "name": "desc_promopt"}, "rawMeta": {"type": 1}}}}], "modelSetting": {"custom_ratio": {"height": 768, "width": 1024}, "ddim_steps": 40, "images_reference": {}, "model": 8, "ratio": 0}, "prompt": {"negative_prompt": "", "prompt": "{{desc_promopt}}"}, "references": [], "settingOnError": {"switch": false, "processType": 1, "timeoutMs": 60000, "retryTimes": 0}}, "nodeMeta": {"description": "通过文字描述/添加参考图生成图片", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-ImageGeneration-v2.jpg", "title": "图像生成_主角首图", "subTitle": "图像生成"}, "outputs": [{"type": "string", "assistType": 2, "name": "data", "required": false}, {"type": "string", "name": "msg", "required": false}], "settings": null, "version": ""}, "_temp": {"bounds": {"x": -1520.1887182897458, "y": 1650.093669594578, "width": 360, "height": 166.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-ImageGeneration-v2.jpg", "description": "通过文字描述/添加参考图生成图片", "title": "图像生成", "mainColor": "#FF4DC3"}}}, {"id": "170966", "type": "4", "meta": {"position": {"x": 139.8112817102542, "y": 1664.093669594578}}, "data": {"nodeMeta": {"description": "保留图片前景主体，输出透明背景(.png)", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-plugin-cutout-v2.jpg", "subtitle": "抠图:cutout", "title": "cutout"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7438919188246429731", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "cutout", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7438919188246413347", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "抠图", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputParameters": [{"name": "url", "input": {"type": "string", "assistType": 2, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "1866199", "name": "data"}, "rawMeta": {"type": 7}}}}, {"name": "only_mask", "input": {"type": "string", "value": {"type": "literal", "content": "0"}}}, {"name": "output_mode", "input": {"type": "string", "value": {"type": "literal", "content": "0"}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "assistType": 2, "name": "data", "required": false, "description": "透明背景图，在输出模式为透明背景时生效"}, {"type": "string", "name": "mask", "required": false, "description": "抠图区域蒙板矢量图，在输出模式为蒙版矢量图时生效"}, {"type": "string", "name": "msg", "required": false}]}, "_temp": {"bounds": {"x": -40.18871828974579, "y": 1664.093669594578, "width": 360, "height": 138.7}, "externalData": {"icon": "https://lf26-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/1682717607724762_1737442217436398717_QfrWIv9q70.jpg?lk3s=cd508e2b&x-expires=1750732907&x-signature=hGHQWYDUNo8zwiFY266itDPDQls%3D", "apiName": "cutout", "pluginID": "7438919188246413347", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7352795533666664448", "inputs": [{"description": "自定义抠图内容的提示词，不填时默认保留主体抠图", "input": {}, "name": "prompt", "required": false, "title": "提示词", "type": "string"}, {"assistType": 2, "description": "待抠图的图片", "input": {}, "name": "url", "required": true, "title": "上传图", "type": "image"}, {"defaultValue": 0, "description": "结果图尺寸，支持返回抠图结果尺寸(去除透明长宽)和原图尺寸, enum list is [0,3], default value is 0", "enum": [0, 3], "enumVarNames": ["抠图结果尺寸", "原图尺寸"], "input": {}, "name": "only_mask", "required": false, "title": "产物尺寸", "type": "integer"}, {"defaultValue": 0, "description": "输出图模式，可选透明背景图/蒙版矢量图, enum list is [0,1], default value is 0", "enum": [0, 1], "enumVarNames": ["透明背景图", "蒙版矢量图"], "input": {}, "name": "output_mode", "required": false, "title": "输出图模式", "type": "integer"}], "outputs": [{"assistType": 2, "description": "透明背景图，在输出模式为透明背景时生效", "input": {}, "name": "data", "required": false, "type": "image"}, {"description": "抠图区域蒙板矢量图，在输出模式为蒙版矢量图时生效", "input": {}, "name": "mask", "required": false, "type": "string"}, {"input": {}, "name": "msg", "required": false, "type": "string"}], "updateTime": 1748104530, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "", "title": "cutout", "mainColor": "#CA61FF"}}}, {"id": "1087428", "type": "4", "meta": {"position": {"x": 2072.798717602023, "y": 1544.093669594578}}, "data": {"nodeMeta": {"description": "批量添加图片", "icon": "https://p6-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/324427c7589a426286a9774fa69f64a2~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1744472328&x-signature=9GXsgm2HH29uW0ihgoymUTYxCmc%3D", "subtitle": "视频合成_剪映小助手:add_images", "title": "add_images_首图角色"}, "inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833883688", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_images", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/Pgm9wXA4EipKhYkeEQJcLBlJnWb?from=from_copylink", "input": {}, "name": "image_infos", "required": true, "type": "string"}, {"description": "x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "移动transform_x", "input": {}, "name": "transform_x", "required": false, "type": "float"}, {"description": "移动transform_y", "input": {}, "name": "transform_y", "required": false, "type": "float"}], "inputParameters": [{"name": "draft_url", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "168118", "name": "draft_url"}, "rawMeta": {"type": 1}}}}, {"name": "image_infos", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "163547", "name": "roleImgData"}, "rawMeta": {"type": 1}}}}, {"name": "scale_x", "input": {"type": "float", "value": {"type": "literal", "content": 2, "rawMeta": {"type": 4}}}}, {"name": "scale_y", "input": {"type": "float", "value": {"type": "literal", "content": 2, "rawMeta": {"type": 4}}}}], "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "list", "name": "segment_infos", "schema": {"type": "object", "schema": [{"type": "integer", "name": "end", "required": false}, {"type": "string", "name": "id", "required": false}, {"type": "integer", "name": "start", "required": false}]}, "required": false}, {"type": "string", "name": "track_id", "required": false}, {"type": "string", "name": "draft_url", "required": false}, {"type": "list", "name": "image_ids", "schema": {"type": "string"}, "required": false}, {"type": "list", "name": "segment_ids", "schema": {"type": "string"}, "required": false}]}, "_temp": {"bounds": {"x": 1892.7987176020229, "y": 1544.093669594578, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf26-appstore-sign.oceancloudapi.com/ocean-cloud-tos/plugin_icon/3778360647354254_1739242717113014582_ZXUfNgToeJ.png?lk3s=cd508e2b&x-expires=1750732907&x-signature=ipkoaSNLzYRZlG8%2B%2FzNj4eq19so%3D", "apiName": "add_images", "pluginID": "7457837925833801768", "pluginProductStatus": 1, "pluginProductUnlistType": 0, "pluginType": 1, "spaceID": "7391771569926127635", "inputs": [{"description": "移动transform_y", "input": {}, "name": "transform_y", "required": false, "type": "float"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/Pgm9wXA4EipKhYkeEQJcLBlJnWb?from=from_copylink", "input": {}, "name": "image_infos", "required": true, "type": "string"}, {"description": "x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "移动transform_x", "input": {}, "name": "transform_x", "required": false, "type": "float"}], "outputs": [{"input": {}, "name": "draft_url", "required": false, "type": "string"}, {"input": {}, "name": "image_ids", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"input": {}, "name": "segment_ids", "required": false, "schema": {"assistType": 0, "type": "string"}, "type": "list"}, {"input": {}, "name": "segment_infos", "required": false, "schema": {"schema": [{"input": {}, "name": "id", "required": false, "type": "string"}, {"input": {}, "name": "start", "required": false, "type": "integer"}, {"input": {}, "name": "end", "required": false, "type": "integer"}], "type": "object"}, "type": "list"}, {"input": {}, "name": "track_id", "required": false, "type": "string"}], "updateTime": 1748140539, "latestVersionTs": "0", "latestVersionName": "", "versionName": "", "description": "批量添加图片", "title": "add_images", "mainColor": "#CA61FF"}}}, {"id": "142052", "type": "13", "meta": {"position": {"x": -3469.512958183198, "y": 672.0553452394579}}, "data": {"inputs": {"content": {"type": "string", "value": {"type": "literal", "content": "开始生成故事文案..."}}, "inputParameters": [], "streamingOutput": false, "callTransferVoice": true, "chatHistoryWriting": "historyWrite"}, "nodeMeta": {"description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "mainColor": "#5C62FF", "subTitle": "输出", "title": "输出"}}, "_temp": {"bounds": {"x": -3649.512958183198, "y": 672.0553452394579, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "title": "输出", "mainColor": "#5C62FF"}}}, {"id": "194566", "type": "13", "meta": {"position": {"x": -2557.335490634803, "y": 780.294139402054}}, "data": {"inputs": {"content": {"type": "string", "value": {"type": "literal", "content": "正在创建视频分镜..."}}, "inputParameters": [], "streamingOutput": false, "callTransferVoice": true, "chatHistoryWriting": "historyWrite"}, "nodeMeta": {"description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "mainColor": "#5C62FF", "subTitle": "输出", "title": "输出_1"}}, "_temp": {"bounds": {"x": -2737.335490634803, "y": 780.294139402054, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "title": "输出", "mainColor": "#5C62FF"}}}, {"id": "1578154", "type": "13", "meta": {"position": {"x": -1638.626801452293, "y": 771.7936695945779}}, "data": {"inputs": {"content": {"type": "string", "value": {"type": "literal", "content": "开始生成分镜画面提示词.."}}, "inputParameters": [], "streamingOutput": false, "callTransferVoice": true, "chatHistoryWriting": "historyWrite"}, "nodeMeta": {"description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "mainColor": "#5C62FF", "subTitle": "输出", "title": "输出_2"}}, "_temp": {"bounds": {"x": -1818.626801452293, "y": 771.7936695945779, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "title": "输出", "mainColor": "#5C62FF"}}}, {"id": "1214309", "type": "13", "meta": {"position": {"x": -405.344846217512, "y": 697.7936695945779}}, "data": {"inputs": {"content": {"type": "string", "value": {"type": "literal", "content": "开始生成视频配图，请稍等.."}}, "inputParameters": [], "streamingOutput": false, "callTransferVoice": true, "chatHistoryWriting": "historyWrite"}, "nodeMeta": {"description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "mainColor": "#5C62FF", "subTitle": "输出", "title": "输出_3"}}, "_temp": {"bounds": {"x": -585.344846217512, "y": 697.7936695945779, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "title": "输出", "mainColor": "#5C62FF"}}}, {"id": "106358", "type": "13", "meta": {"position": {"x": 1836.0165018543607, "y": 600.6492856088762}}, "data": {"inputs": {"content": {"type": "string", "value": {"type": "literal", "content": "视频编排中，即将完成..."}}, "inputParameters": [], "streamingOutput": false, "callTransferVoice": true, "chatHistoryWriting": "historyWrite"}, "nodeMeta": {"description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "mainColor": "#5C62FF", "subTitle": "输出", "title": "输出_4"}}, "_temp": {"bounds": {"x": 1656.0165018543607, "y": 600.6492856088762, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "title": "输出", "mainColor": "#5C62FF"}}}, {"id": "904077", "type": "13", "meta": {"position": {"x": 3932.2402531050725, "y": 1036.6262531331708}}, "data": {"inputs": {"content": {"type": "string", "value": {"type": "literal", "content": "视频草稿地址， 请使用剪映小助手下载：\n{{draft_url}}"}}, "inputParameters": [{"name": "draft_url", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "168118", "name": "draft_url"}, "rawMeta": {"type": 1}}}}], "streamingOutput": false, "callTransferVoice": true, "chatHistoryWriting": "historyWrite"}, "nodeMeta": {"description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "mainColor": "#5C62FF", "subTitle": "输出", "title": "输出_5"}}, "_temp": {"bounds": {"x": 3752.2402531050725, "y": 1036.6262531331708, "width": 360, "height": 114.7}, "externalData": {"icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Output-v2.jpg", "description": "节点从“消息”更名为“输出”，支持中间过程的消息输出，支持流式和非流式两种方式", "title": "输出", "mainColor": "#5C62FF"}}}], "edges": [{"sourceNodeID": "142052", "targetNodeID": "121343"}, {"sourceNodeID": "121343", "targetNodeID": "1165778"}, {"sourceNodeID": "121343", "targetNodeID": "1301843"}, {"sourceNodeID": "121343", "targetNodeID": "1199098"}, {"sourceNodeID": "121343", "targetNodeID": "194566"}, {"sourceNodeID": "1199098", "targetNodeID": "163547"}, {"sourceNodeID": "1214309", "targetNodeID": "121555"}, {"sourceNodeID": "121555", "targetNodeID": "163547", "sourcePortID": "batch-output"}, {"sourceNodeID": "170966", "targetNodeID": "163547"}, {"sourceNodeID": "163547", "targetNodeID": "168118"}, {"sourceNodeID": "163547", "targetNodeID": "106358"}, {"sourceNodeID": "194566", "targetNodeID": "1165778"}, {"sourceNodeID": "1165778", "targetNodeID": "186126"}, {"sourceNodeID": "1165778", "targetNodeID": "1578154"}, {"sourceNodeID": "1578154", "targetNodeID": "186126"}, {"sourceNodeID": "186126", "targetNodeID": "1214309"}, {"sourceNodeID": "106358", "targetNodeID": "168118"}, {"sourceNodeID": "168118", "targetNodeID": "125268"}, {"sourceNodeID": "125268", "targetNodeID": "109941"}, {"sourceNodeID": "146723", "targetNodeID": "180947"}, {"sourceNodeID": "180947", "targetNodeID": "1204256"}, {"sourceNodeID": "109941", "targetNodeID": "1087428"}, {"sourceNodeID": "1204256", "targetNodeID": "158201"}, {"sourceNodeID": "158201", "targetNodeID": "1182713"}, {"sourceNodeID": "1182713", "targetNodeID": "104782"}, {"sourceNodeID": "104782", "targetNodeID": "904077"}, {"sourceNodeID": "1087428", "targetNodeID": "1114402"}, {"sourceNodeID": "1114402", "targetNodeID": "1190196"}, {"sourceNodeID": "1190196", "targetNodeID": "120984"}, {"sourceNodeID": "120984", "targetNodeID": "146723"}, {"sourceNodeID": "1301843", "targetNodeID": "1866199"}, {"sourceNodeID": "1866199", "targetNodeID": "170966"}]}, "bounds": {"x": -3649.512958183198, "y": 531.6936695945778, "width": 7761.75321128827, "height": 1579.9484625076216}}