[{"name": "<PERSON><PERSON>", "id": "7328314516272463923", "description": "强大的数学工具", "category": "实用工具", "is_official": true, "is_free": true, "heat": 41498, "entity_id": "", "favorite_count": 0, "readme": {"overview": "强大的数学工具，可以对算式进行计算。", "features": ["*", "使用示例：", "*", "1.什么是2+3的结果"]}, "tools": [{"name": "calculate", "id": "7392902932389167113", "description": "逐步计算数学表达式。", "parameters": [{"description": "输入一个数学表达式。如果输入不是英文，请先将其翻译成英文。", "name": "input", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.97546, "call_amount": 18289}}, {"name": "query", "id": "7392902932389183497", "description": "计算数学表达式的结果", "parameters": [{"description": "数学表达式，需要转成英文，不接受与数学无关的问题 ", "name": "i", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.9754, "call_amount": 20983}}]}, {"name": "语音转文本", "id": "7342819789989052470", "description": "自动分析音频URL，并将音频转录为文本。", "category": "实用工具", "is_official": true, "is_free": true, "heat": 29562, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["语音转文字是一个识别语音url链接，提取语音文本的插件。", "Tools：", "SpeechToText：输入：url：音频文件url", "*"]}, "tools": [{"name": "SpeechToText", "id": "7366504288144785434", "description": "读取音频url链接，并将音频转为文字", "parameters": [{"description": "音频url链接", "name": "url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99997, "call_amount": 3577751}}]}, {"name": "创客贴智能设计", "id": "7351267469065011241", "description": "输入设计需求，即刻生成精美设计图；涵盖手机海报、宣传图、电商图、店铺广告、日签、社交媒体配图等多样场景。", "category": "实用工具", "is_official": true, "is_free": true, "heat": 21581, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["创客贴智能设计插件是一款强大的设计工具，能够根据用户输入的设计需求，即刻生成精美的设计图。该插件涵盖了手机海报、宣传图、电商图、店铺广告、日签、社交媒体配图等多样场景，可以满足用户在不同场景下的设计需求。", "使用说明：", "*", "输入你的设计需求，可以是具体的设计内容、要传达的信息，或者是一些关键词。"]}, "tools": [{"name": "ckt_intelligent_design", "id": "7351293595066040356", "description": "输入设计需求，即刻生成精美设计图；涵盖手机海报、宣传图、电商图、店铺广告、日签、社交媒体配图等多样场景。", "parameters": [{"description": "用户输入主题", "name": "query", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.97697, "call_amount": 134042}}]}, {"name": "头条视频搜索", "id": "7345693026326446080", "description": "头条视频搜索，可以获得用户想要搜索的视频", "category": "新闻阅读", "is_official": true, "is_free": true, "heat": 20885, "entity_id": "", "favorite_count": 0, "readme": {"overview": "*", "features": ["功能描述", "头条视频搜索插件是一款强大的工具，用户可以通过该插件搜索想要观看的视频。插件提供了详细的入参和出参，以便用户获取准确的视频信息。", "插件的入参包括：", "- query: 用户想要搜索的关键词。"]}, "tools": [{"name": "ToutiaoVideoSearch", "id": "7345695274301407267", "description": "当你需要查找头条上的视频时，可以使用此插件", "parameters": [{"description": "响应中返回的搜索结果数量。默认值为5，最大值为20。实际返回结果的数量可能会少于请求的数量。", "name": "count", "required": false, "sub_params": [], "type": "string"}, {"description": "从结果中返回前要跳过的基于零的偏移量。默认为0。", "name": "offset", "required": false, "sub_params": [], "type": "string"}, {"description": "用户想要搜索视频的查询词。查询词不能为空。", "name": "query", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 589589}}]}, {"name": "板栗看板", "id": "7358789295274049572", "description": "任务拆解", "category": "实用工具", "is_official": true, "is_free": true, "heat": 20280, "entity_id": "", "favorite_count": 0, "readme": {"overview": "对任务进行拆解，并生成一个看板。", "features": ["*", "使用示例：", "*", "1.设计需求对接的看板，包括公告栏、设计需求池，设计评审、驳回、验收合格的板块。"]}, "tools": [{"name": "generateTasks", "id": "7358794730332848140", "description": "根据用户提问拆解任务，生成任务看板", "parameters": [{"description": "用户的完整输入内容，只支持中文", "name": "input", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99933, "call_amount": 25400}}]}, {"name": "飞常准", "id": "7328314169139380234", "description": "通过VariFlight覆盖的全球商业客运航班，您的终端用户可以轻松获得他们的航班状态、办理登机手续柜台、预计出发时间、登机口、登机状态、行李转盘等信息，并能在整个航程中随时掌握。", "category": "便利生活", "is_official": true, "is_free": false, "heat": 19898, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述", "features": ["\"飞常准\"插件是一款高效准确的航班信息查询插件，它能根据用户提供的出发城市名称（dep)、到达城市名称（arr)及出发日期（depTime)以获取相应的航班列表。针对用户输入的出发和到达城市名，该插件可以快速返回匹配的航班信息，无需用户手动输入城市代码，用户体验更加友好。", "出参中包含了各类详尽的航班信息，如航空公司名称（airlinesName)、航班号码（flightNumber)、航班出发/到达时间（departurePlanTime、arrivalPlanTime、departureActualTime、arrivalActualTime)、航班状态（flightStatus)、实际起飞/到达时间戳（departureActualTimestamp、arrivalActualTimestamp)等，为用户提供全方位的航班信息。", "返回的航班信息除包含出发和到达的时间、城市、航空公司、航班号等基本信息外，还特别提供了包括值机柜台（checkinTable)、登机口（boardingGate)、出发/到达航站楼（departureTerminal、arrivalTerminal)及相对应的英文城市名、国家名等详尽信息。此外，通过\"飞常准\"插件，用户还可以知道航班的飞行距离（distance)，以便对出行时间和消耗做出更合理的规划。", "使用说明"]}, "tools": [{"name": "getRoute", "id": "7379228260972036105", "description": "获取航班列表", "parameters": [{"description": "到达城市名称，仅接受名称，不接受代码", "name": "arr", "required": true, "sub_params": [], "type": "string"}, {"description": "出发城市名称，不接受代码。", "name": "dep", "required": true, "sub_params": [], "type": "string"}, {"description": "出发日期，默认为今天日期，例如：2023-12-19。注意格式需要是2023-01-03，不可以是2023-1-3等其他格式", "name": "depTime", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.94988, "call_amount": 38661}}]}, {"name": "天眼查", "id": "7407722060627247143", "description": "利用天眼查官方API接口,以关键词检索企业列表实现企业搜索功能，并支持查询企业基本信息、新闻舆情、经营异常及变更记录等信息。", "category": "金融与商业", "is_official": true, "is_free": false, "heat": 19766, "entity_id": "", "favorite_count": 0, "readme": {"overview": "利用天眼查官方API接口,以关键词检索企业列表实现企业搜索功能，并支持查询企业基本信息、新闻舆情、经营异常及变更记录等信息。", "features": []}, "tools": [{"name": "change_log", "id": "7407724292865179667", "description": "可以通过公司名称（需公司全称）或ID获取企业变更记录，变更记录包括工商变更事项、变更前后信息等字段的详细信息。", "parameters": [{"description": "当前页数（默认第1页）", "name": "pageNum", "required": false, "sub_params": [], "type": "integer"}, {"description": "每页条数（默认20条，最大20条）", "name": "pageSize", "required": false, "sub_params": [], "type": "integer"}, {"description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "name": "keyword", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99975, "call_amount": 29661}}, {"name": "company_detail", "id": "7407724292865163283", "description": "可以通过公司名称（需公司全称）或ID获取企业基本信息和企业联系方式，包括公司名称或ID、类型、成立日期、电话、邮箱、网址等字段的详细信息。", "parameters": [{"description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "name": "keyword", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99962, "call_amount": 296099}}, {"name": "search", "id": "7407724292865212435", "description": "可以通过关键词获取企业列表，企业列表包括公司名称或ID、类型、成立日期、经营状态、统一社会信用代码等字段的详细信息。", "parameters": [{"description": "关键词", "name": "word", "required": true, "sub_params": [], "type": "string"}, {"description": "每页条数（默认20条，最大20条）", "name": "pageSize", "required": false, "sub_params": [], "type": "integer"}, {"description": "当前页数（默认第1页）", "name": "pageNum", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.99808, "call_amount": 176009}}, {"name": "news", "id": "7407724292865196051", "description": "可以通过公司名称（需公司全称）或ID精确匹配获取新闻列表。", "parameters": [{"description": "结束时间", "name": "endTime", "required": false, "sub_params": [], "type": "string"}, {"description": "当前页数（默认第1页）", "name": "pageNum", "required": false, "sub_params": [], "type": "integer"}, {"description": "标签（多个逗号分隔，默认返回全部）", "name": "tags", "required": false, "sub_params": [], "type": "string"}, {"description": "公司名称（精确匹配、name和id其一为必填即可）", "name": "name", "required": false, "sub_params": [], "type": "string"}, {"description": "每页条数（默认20条，最大20条）", "name": "pageSize", "required": false, "sub_params": [], "type": "integer"}, {"description": "开始时间", "name": "startTime", "required": false, "sub_params": [], "type": "string"}, {"description": "公司id（name和id其一为必填即可）", "name": "id", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.9999, "call_amount": 35692}}, {"name": "abnormal_operation", "id": "7407724292865146899", "description": "可以通过公司名称（需公司全称）或ID获取企业经营异常信息，经营异常信息包括列入/移除原因、时间、做出决定机关等字段的详细信息。", "parameters": [{"description": "当前页数（默认第1页）", "name": "pageNum", "required": false, "sub_params": [], "type": "integer"}, {"description": "String\t搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "name": "keyword", "required": true, "sub_params": [], "type": "string"}, {"description": "每页条数（默认20条，最大20条）", "name": "pageSize", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.99952, "call_amount": 25451}}]}, {"name": "百度地图", "id": "7394300204087787570", "description": "利用百度地图帮助你搜索周边，路线规划等。", "category": "便利生活", "is_official": true, "is_free": true, "heat": 19432, "entity_id": "", "favorite_count": 0, "readme": {"overview": "利用百度地图帮助你搜索周边，路线规划等。", "features": ["*", "使用示例：", "*", "北京西站转换成经纬度"]}, "tools": [{"name": "ip_to_address", "id": "7394300614395674634", "description": "根据用户输入的IP地址，能够快速的帮用户定位IP的所在位置。", "parameters": [{"description": "用户输入的ip。", "name": "ip", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.97872, "call_amount": 16604}}, {"name": "search_around", "id": "7394300614395772938", "description": "提供指定地点额关键词搜索周边能力。", "parameters": [{"description": "圆形区域检索半径，单位为米。", "name": "radius", "required": false, "sub_params": [], "type": "integer"}, {"description": "检索关键字。如“美食”。", "name": "keyword", "required": false, "sub_params": [], "type": "string"}, {"description": "待搜索点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "name": "location", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.9522, "call_amount": 49555}}, {"name": "plan_driving", "id": "7394300614395707402", "description": "驾驶路线规划。", "parameters": [{"description": "目的地。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "name": "dest_location", "required": true, "sub_params": [], "type": "string"}, {"description": "出发点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "name": "origin_location", "required": true, "sub_params": [], "type": "string"}, {"description": "是否下发step详情 1：下发step详情；0：不下发step详情。", "name": "steps_info", "required": false, "sub_params": [], "type": "integer"}, {"description": "路线偏好。默认值：0。 可选值：0：常规路线(时间最短)；1：不走高速；2：躲避拥堵；3：最短距离；4：花费最少；5：大路优先；6：表示避开轮渡。", "name": "tactics", "required": false, "sub_params": [], "type": "integer"}, {"description": "途径点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "name": "way_points_location", "required": false, "sub_params": [{"description": "途径点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "name": "", "required": false, "sub_params": [], "type": "string"}], "type": "array"}], "performance": {"success_rate": 0.94491, "call_amount": 145136}}, {"name": "search_region", "id": "7394300614395789322", "description": "提供指定区域关键词搜索的能力。", "parameters": [{"description": "检索关键字。如“美食”。", "name": "keyword", "required": false, "sub_params": [], "type": "string"}, {"description": "检索行政区划区域，可输入行政区划名。", "name": "region", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 182578}}, {"name": "geocode_to_poi", "id": "7394300614395658250", "description": "逆地理编码，将经纬度转换为详细结构化的地址。", "parameters": [{"description": "经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121", "name": "location", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.94989, "call_amount": 38061}}, {"name": "poi_to_geocode", "id": "7394300614395756554", "description": "地理编码，将详细的结构化地址转换为百度经纬度坐标。", "parameters": [{"description": "待解析的地址。可以输入两种样式的值，分别是：标准的结构化地址信息，如北京市海淀区上地十街十号。与“*路与*路交叉口”描述方式，如北一环路和阜阳路的交叉路口。", "name": "address", "required": true, "sub_params": [], "type": "string"}, {"description": "地址所在的城市名。", "name": "city", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99128, "call_amount": 748006}}, {"name": "plan_bicycle", "id": "7394300614395691018", "description": "骑行路线规划。", "parameters": [{"description": "骑行类型。默认0，0：普通自行车；1：电动自行车。", "name": "riding_type", "required": false, "sub_params": [], "type": "string"}, {"description": "是否下发step详情 1：下发step详情；0：不下发step详情。", "name": "steps_info", "required": false, "sub_params": [], "type": "integer"}, {"description": "目的地。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "name": "dest_location", "required": true, "sub_params": [], "type": "string"}, {"description": "出发点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "name": "origin_location", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.98273, "call_amount": 123516}}, {"name": "plan_walking", "id": "7394300614395740170", "description": "步行路线规划。", "parameters": [{"description": "目的地。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "name": "dest_location", "required": true, "sub_params": [], "type": "string"}, {"description": "出发点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "name": "origin_location", "required": true, "sub_params": [], "type": "string"}, {"description": "是否下发step详情 1：下发step详情；0：不下发step详情。", "name": "steps_info", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.92808, "call_amount": 125977}}, {"name": "plan_integrated", "id": "7394300614395723786", "description": "公共交通路线规划。", "parameters": [{"description": "目的地。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "name": "dest_location", "required": true, "sub_params": [], "type": "string"}, {"description": "出发点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "name": "origin_location", "required": true, "sub_params": [], "type": "string"}, {"description": "是否下发step详情 1：下发step详情；0：不下发step详情。", "name": "steps_info", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.92402, "call_amount": 10572}}]}, {"name": "新浪财经", "id": "7356445481058189327", "description": "该插件提供汇率转换，股票行情查询，股票财务数据查询功能。由新浪财经提供相关能力", "category": "实用工具", "is_official": true, "is_free": true, "heat": 18911, "entity_id": "", "favorite_count": 0, "readme": {"overview": "该插件提供汇率转换，股票行情查询，股票财务数据查询功能。由新浪财经提供相关能力", "features": []}, "tools": [{"name": "SearchStockData", "id": "7356446812695461888", "description": "根据关键词或者股票代码搜索股票信息，包括财务状况以及市场信息。例子：贵州茅台、600519", "parameters": [{"description": "指定日期，默认今天。格式为YYYY-MM-DD", "name": "date", "required": false, "sub_params": [], "type": "string"}, {"description": "股票的关键词或者股票代码，例如：茅台、腾讯、600519", "name": "keyword", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.97189, "call_amount": 4612324}}, {"name": "GetExchangeRate", "id": "7356446812695445504", "description": "该工具提供汇率查询能力，入参中的from和to只能是货币代码", "parameters": [{"description": "指定日期，默认今日。格式为YYYY-MM-DD", "name": "date", "required": false, "sub_params": [], "type": "string"}, {"description": "起始货币代码，如USD、CNY", "name": "from", "required": true, "sub_params": [], "type": "string"}, {"description": "目标货币代码，如CNY、USD", "name": "to", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.97943, "call_amount": 47238}}]}, {"name": "文库搜索", "id": "7376228190244618278", "description": "根据文库文档标题关键字，搜索豆柴文库内容和网页 URL", "category": "科学与教育", "is_official": true, "is_free": true, "heat": 17602, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["文库搜索插件根据用户的关键词搜索豆柴网(", "https://www.douchai.cn/", ")文库内容和网页 URL。", "Tools："]}, "tools": [{"name": "document_search", "id": "7376233353852485659", "description": "根据关键词检索豆柴文档", "parameters": [{"description": "用以检索的关键词", "name": "keyword", "required": true, "sub_params": [], "type": "string"}, {"description": "获得的数据条数，默认10条，最大50条", "name": "number", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99637, "call_amount": 96327}}]}, {"name": "猎聘", "id": "7330155407547138089", "description": "帮助用户根据工作经验、教育经历、地理位置、薪水、职位名称、工作性质等条件搜索猎聘上提供的招聘信息", "category": "便利生活", "is_official": true, "is_free": true, "heat": 16868, "entity_id": "", "favorite_count": 0, "readme": {"overview": "帮助用户根据工作经验、教育经历、地理位置、薪水、职位名称、工作性质等条件搜索猎聘上提供的招聘信息。", "features": ["*", "使用示例：", "1.帮我找下北京市的工作", "2.我想在保定找一份工作，月薪6千元，我的工作经验是2年半，有没有好的推荐"]}, "tools": [{"name": "job_recommendation", "id": "7379228748476022819", "description": "帮助用户搜索工作招聘，基于用户的工作经验、教育经历、地理位置、薪水、职位名称、工作性质等", "parameters": [{"description": "公司性质，填国企、私企", "name": "compNature", "required": false, "sub_params": [], "type": "string"}, {"description": "公司名称", "name": "companyName", "required": false, "sub_params": [], "type": "string"}, {"description": "教育经历，如硕士，博士等。如果用户没提及受教育程度，此项勿填", "name": "eduLevel", "required": false, "sub_params": [], "type": "string"}, {"description": "薪水上限，必须使用数字来代表，比如：100000", "name": "salaryCap", "required": false, "sub_params": [], "type": "string"}, {"description": "薪水下限，必须使用数字来代表，比如：50000", "name": "salaryFloor", "required": false, "sub_params": [], "type": "string"}, {"description": "薪水类别，只能填“月薪”或者“年薪”。", "name": "salaryKind", "required": false, "sub_params": [], "type": "string"}, {"description": "公司位置，如北京", "name": "address", "required": false, "sub_params": [], "type": "string"}, {"description": "职位名称，如java开发", "name": "job<PERSON>ame", "required": false, "sub_params": [], "type": "string"}, {"description": "工作经历，填具体年限，如2年以上", "name": "workExperience", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 50106}}]}, {"name": "Notion", "id": "7328314363604025371", "description": "Notion", "category": "实用工具", "is_official": true, "is_free": true, "heat": 16518, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["Notion插件提供用户和", "https://www.notion.so/", "交互的能力，使用oauth鉴权后，用户可以读取notion页面中的内容，或者将与bot对话的内容存储到notion中。", "*"]}, "tools": [{"name": "get_database_info", "id": "7396964197051187227", "description": "使用notion中数据库的id或者数据库的url查找相应的数据库。", "parameters": [{"description": "输入数据库id或数据库url以搜索特定的notion数据库并检索其内容", "name": "id", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.98, "call_amount": 1406}}, {"name": "search", "id": "7396964197051138075", "description": "根据标题关键字搜索notion页面，支持模糊搜索。", "parameters": [{"description": "使用标题关键字搜索notion以找到相应的页面", "name": "keyword", "required": false, "sub_params": [], "type": "string"}, {"description": "通过输入\"page\" 或 \"database\"作为参数来选择页面类型", "name": "type", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.96, "call_amount": 2531}}, {"name": "search_database_item", "id": "7396964197051154459", "description": "查询数据库数据", "parameters": [{"description": "页面大小", "name": "page_size", "required": false, "sub_params": [], "type": "integer"}, {"description": "指定组织数据的排序标准", "name": "sorts", "required": false, "sub_params": [{"description": "排序的方向。可能的值包括\"ascending\" 和 \"descending\"", "name": "direction", "required": false, "sub_params": [], "type": "string"}, {"description": "列名", "name": "property", "required": false, "sub_params": [], "type": "string"}], "type": "array"}, {"description": "notion数据库的ID或URL", "name": "database_id", "required": true, "sub_params": [], "type": "string"}, {"description": "页面", "name": "page", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.95, "call_amount": 2180}}, {"name": "create_page", "id": "7396964197051170843", "description": "创建一个新页面，用户可以在其中输入他们想要的内容", "parameters": [{"description": "指定父页面的page_id或页面的url来控制新页面的位置", "name": "parent_id", "required": true, "sub_params": [], "type": "string"}, {"description": "用户想要输入到页面中的文本", "name": "text", "required": false, "sub_params": [], "type": "string"}, {"description": "指定页面的标题", "name": "title", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.97, "call_amount": 4677}}, {"name": "add_database_item", "id": "7396964197051088923", "description": "向Notion数据库添加新行数据", "parameters": [{"description": "数据库的列属性", "name": "properties", "required": true, "sub_params": [{"description": "数据库列的名称", "name": "name", "required": true, "sub_params": [], "type": "string"}, {"description": "数据库中支持的列类型，包括rich_text, checkbox, number, and date这些类型", "name": "type", "required": true, "sub_params": [], "type": "string"}, {"description": "您要存储的值", "name": "value", "required": true, "sub_params": [], "type": "string"}], "type": "array"}, {"description": "输入notion数据库ID或URL以向指定数据库添加新行", "name": "parent_id", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.95, "call_amount": 42414}}, {"name": "create_database", "id": "7396964197051105307", "description": "在Notion中创建新数据库", "parameters": [{"description": "指定父页面的page_id以控制新页面的位置。如果留空，新页面将在“coze space”下创建。", "name": "parent_id", "required": false, "sub_params": [], "type": "string"}, {"description": "数据库的列属性", "name": "properties", "required": true, "sub_params": [{"description": "数据库列的名称", "name": "name", "required": true, "sub_params": [], "type": "string"}, {"description": "数据库中支持的列类型包括：rich_text, checkbox, number, and date.", "name": "type", "required": true, "sub_params": [], "type": "string"}], "type": "array"}, {"description": "指定要创建的新数据库的标题, default value is default", "name": "title", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.955, "call_amount": 1145}}, {"name": "write_page", "id": "7396964197051072539", "description": "向特定页面添加内容", "parameters": [{"description": "指定父页面的page_id以控制新页面的位置", "name": "page_id", "required": true, "sub_params": [], "type": "string"}, {"description": "用户想要输入到页面中的文本", "name": "text", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.95, "call_amount": 11587}}, {"name": "read_page", "id": "7396964197051121691", "description": "使用notion中的page_id或者页面url查找相应的页面", "parameters": [{"description": "输入page_id或notion页面url以搜索特定的notion页面并检索其内容", "name": "id", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.96, "call_amount": 2673}}]}, {"name": "中文文本转语音", "id": "7361314113294712842", "description": "将输入的文字转为各种语色的语音，插件也支持英文，但是“英文文本转语音”读得更好哦，快来看看吧～", "category": "实用工具", "is_official": true, "is_free": true, "heat": 15463, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["中文文本转语音是一个将用户输入的中文文本转为mp3音频链接的插件。", "Tools：", "输入：text：用户想要转为音频的中文文本", "*"]}, "tools": [{"name": "roumei_nvsheng", "id": "7361316775017087014", "description": "输入一段文本，转化为柔美女生的声音", "parameters": [{"description": "想要转化为语音的文本", "name": "text", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99969, "call_amount": 362323}}, {"name": "gaoleng_yujie", "id": "7361316775017070630", "description": "将你的文本转化为高冷御姐的声音", "parameters": [{"description": "想要转化为语音的文本", "name": "text", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99894, "call_amount": 209673}}, {"name": "yangguang_qingnian", "id": "7361316857011240987", "description": "将你输入的文本转化为阳光青年的声音", "parameters": [{"description": "用户想要转化为声音的文本", "name": "text", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99762, "call_amount": 274569}}, {"name": "wennuan_ahu", "id": "7361316775017103398", "description": "将输入的文本转化为温柔的男声", "parameters": [{"description": "用户想要转化为声音的文本", "name": "text", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99978, "call_amount": 379154}}]}, {"name": "搜狐热闻", "id": "7343894357063385100", "description": "帮助用户获取搜狐网上的每日热闻", "category": "新闻阅读", "is_official": true, "is_free": true, "heat": 15194, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["搜狐新闻插件可以根据用户query帮助用户获取搜狐网上的每日热闻。", "Tools：", "*", "top_news"]}, "tools": [{"name": "top_news", "id": "7345778111100993586", "description": "帮助用户获取搜狐网上的每日热闻", "parameters": [{"description": "获取新闻条数", "name": "count", "required": true, "sub_params": [], "type": "integer"}, {"description": "搜索关键词", "name": "q", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99972, "call_amount": 2075652}}]}, {"name": "懂车帝", "id": "7329410795979325503", "description": "如果你想要查询汽车信息，包括二手车、新车、某些车型的信息时可以使用此插件进行查询", "category": "网页搜索", "is_official": true, "is_free": true, "heat": 14888, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["SecondHandCar是一款专为二手车市场设计的信息查询工具，它可以帮助用户对感兴趣的二手车进行深度的数据挖掘和分析。除了基础的品牌、城市、车型、价格和车系查询功能外，插件还会提供二手车的原始价格、当前价格、车况图片、车名全称、上牌年份和生产年份等详细信息，以便用户进行全面的评估和比较。该插件尤其适用于需要获取多源和准确的二手车信息的私人买卖者、二手车经销商和汽车研究者。", "*", "CarSeries可以查询新车信息或者查询某个特定车系（如宝马3系，奔驰e级）信息，可以获得新车价格，车辆结构，车辆生产年份，售卖链接等信息，以便用户进行全面的评估和比较。", "使用说明："]}, "tools": [{"name": "CarSeries", "id": "7353481774833401856", "description": "当你需要查询新车信息或者查询某个特定车系（如宝马3系，奔驰e级）信息的时候可以使用此工具，可以获得新车价格，车辆结构，车辆生产年份，售卖链接等信息", "parameters": [{"description": "期望查询的车系，如宝马3系、奔驰e级", "name": "series", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99868, "call_amount": 98511}}, {"name": "SecondHandCar", "id": "7353481774833418240", "description": "当你查询二手车的售卖信息时候可以使用此工具，可以获得二手车的价格、二手车车况图片等信息", "parameters": [{"description": "期望查询的车系，如宝马1系、奥迪a4等", "name": "series", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询的汽车品牌，如宝马、奥迪", "name": "brand", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询二手车的城市", "name": "city", "required": true, "sub_params": [], "type": "string"}, {"description": "期望查询的二手车类型、如中型suv、紧凑型轿车等", "name": "grade", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询二手车的价格范围", "name": "price_tag", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99926, "call_amount": 17900}}]}, {"name": "头条图片搜索", "id": "7345719541038579747", "description": "头条图片搜索，用户可以获得想要搜索的图片", "category": "新闻阅读", "is_official": true, "is_free": true, "heat": 13762, "entity_id": "", "favorite_count": 0, "readme": {"overview": "*", "features": ["插件功能描述", "头条图片搜索插件是一款功能强大的插件，能够帮助用户轻松搜索并获取他们想要的图片。它可以根据用户提供的搜索关键词，从各大图片库中抓取与关键词相关的图片，并返回给用户。通过该插件，用户可以方便快捷地找到所需的图片素材，满足他们的个人或商业需求。", "该插件支持以下核心功能：", "*"]}, "tools": [{"name": "ToutiaoPictureSearch", "id": "7345724749311426560", "description": "头条图像搜索API允许用户在头条站内内查找图片。", "parameters": [{"description": "响应中返回的搜索结果数量。默认为5，最大值为20。实际返回结果的数量可能会少于请求的数量。", "name": "count", "required": false, "sub_params": [], "type": "string"}, {"description": "从结果中返回前要跳过的基于零的偏移量。默认为0。", "name": "offset", "required": false, "sub_params": [], "type": "string"}, {"description": "用户搜索图片的查询词。查询词不能为空。", "name": "query", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 2564715}}]}, {"name": "语音合成", "id": "7426654728890777650", "description": "根据音色和文本合成音频", "category": "实用工具", "is_official": true, "is_free": true, "heat": 13573, "entity_id": "", "favorite_count": 0, "readme": {"overview": "根据音色和文本合成音频。支持的音色列表可以参考", "features": ["https://bytedance.larkoffice.com/docx/WdDOdiB1BoRyBNxlkXWcn0n3nLc"]}, "tools": [{"name": "speech_synthesis", "id": "7426655854067367946", "description": "根据音色和文本合成音频", "parameters": [{"description": "语音语种，非必填，参考 https://bytedance.larkoffice.com/docx/WdDOdiB1BoRyBNxlkXWcn0n3nLc", "name": "language", "required": false, "sub_params": [], "type": "string"}, {"description": "音色ID，默认为爽快思思/Skye。详细音色列表参考 https://bytedance.larkoffice.com/docx/WdDOdiB1BoRyBNxlkXWcn0n3nLc, default value is 爽快思思/Skye", "name": "speaker_id", "required": false, "sub_params": [], "type": "string"}, {"description": "语速，范围是[0.2,3]，默认为1，通常保留一位小数即可, default value is 1", "name": "speed_ratio", "required": false, "sub_params": [], "type": "number"}, {"description": "要合成音频的文本内容", "name": "text", "required": true, "sub_params": [], "type": "string"}, {"description": "voice id", "name": "voice_id", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99827, "call_amount": 19233573}}]}, {"name": "古诗词搜索", "id": "7376222239357747212", "description": "内容来源于权威的古诗文网。根据古诗词标题或内容或作者，检索古诗词。\n输入：标签、关键词、作者、返回诗词数量\n输出：标题、诗文内容、注释、作者、朝代、简介、作品赏析、创作背景、作者简介、作品评价", "category": "科学与教育", "is_official": true, "is_free": true, "heat": 12815, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["古诗词搜索是一个品鉴中国古代诗词的插件，能够根据作者、关键词等信息检索古诗词，并返回对古诗词内容、注释、作者、朝代、简介、作品赏析、创作背景、作者简介、作品评价等信息。", "Tools：", "poem_search，输入：keyword：输入关键词进行检索，检索内容包括作者、主题等信息。writer：全等匹配作者名称。 输出：古诗词品鉴集合", "使用示例："]}, "tools": [{"name": "poem_search", "id": "7376233100990464019", "description": "根据条件搜索古诗词，可以根据关键词、作者、标签等条件进行检索。", "parameters": [{"description": "全等匹配作者名称", "name": "writer", "required": false, "sub_params": [], "type": "string"}, {"description": "全等匹配标签名称，多个标签使用|划分，命中一个即返回，示例：\"春愁\"、\"赏花\"、\"松柏\"、\"征战、\"骆驼\"、\"垂柳\"、\"端午\"", "name": "tags", "required": false, "sub_params": [], "type": "string"}, {"description": "获得的数据条数，默认10条，最大50条", "name": "number", "required": false, "sub_params": [], "type": "string"}, {"description": "输入关键词进行检索，检索内容包括作者、主题等信息。此项为必填，用户的搜索将基于此关键词进行。", "name": "keyword", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99994, "call_amount": 244029}}]}, {"name": "iSlide", "id": "7404671110094258185", "description": "通过关键词、模版，生成大纲与PPT。", "category": "科学与教育", "is_official": true, "is_free": true, "heat": 12413, "entity_id": "", "favorite_count": 0, "readme": {"overview": "通过关键词、模版，生成大纲与PPT。", "features": []}, "tools": [{"name": "get_ppt", "id": "7404678015596380187", "description": "通过任务id生成ppt。", "parameters": [{"description": "任务id。", "name": "historyId", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.94299, "call_amount": 200000}}, {"name": "get_outline", "id": "7404678015596363803", "description": "根据关键字获取大纲。", "parameters": [{"description": "想要生成的大纲的主题。", "name": "topic", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.94777, "call_amount": 975247}}, {"name": "get_thumbnail_with_outline", "id": "7404678082176483354", "description": "通过大纲的详细内容和模版id生成ppt的缩略图，并输出生成ppt的任务id。", "parameters": [{"description": "大纲内容。", "name": "outline", "required": true, "sub_params": [{"description": "ppt标题。", "name": "title", "required": true, "sub_params": [], "type": "string"}, {"description": "ppt章节。", "name": "sections", "required": true, "sub_params": [{"description": "二级章节。", "name": "contents", "required": true, "sub_params": [{"description": "三级章节。", "name": "items", "required": true, "sub_params": [{"description": "三级章节内容。", "name": "items", "required": true, "sub_params": [{"description": "三级章节内容。", "name": "", "required": false, "sub_params": [], "type": "string"}], "type": "array"}, {"description": "三级章节标题。", "name": "title", "required": true, "sub_params": [], "type": "string"}], "type": "array"}, {"description": "二级章节副标题。", "name": "subtitle", "required": true, "sub_params": [], "type": "string"}, {"description": "二级章节标题。", "name": "title", "required": true, "sub_params": [], "type": "string"}], "type": "array"}, {"description": "章节副标题。", "name": "subtitle", "required": true, "sub_params": [], "type": "string"}, {"description": "章节标题。", "name": "title", "required": true, "sub_params": [], "type": "string"}], "type": "array"}, {"description": "ppt副标题。", "name": "subtitle", "required": true, "sub_params": [], "type": "string"}], "type": "object"}, {"description": "模版id", "name": "themeId", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.6557, "call_amount": 56498}}, {"name": "get_thumbnail", "id": "7404678015596412955", "description": "根据模版id和大纲id获取PPT的缩略图，并输出生成ppt的任务id。", "parameters": [{"description": "大纲id。", "name": "outLineId", "required": true, "sub_params": [], "type": "string"}, {"description": "模版id。", "name": "themeId", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.94747, "call_amount": 48234}}, {"name": "get_themes", "id": "7404678015596396571", "description": "根据关键字获取模版。", "parameters": [{"description": "获取模版的数量。", "name": "count", "required": false, "sub_params": [], "type": "integer"}, {"description": "想要获取模版的关键字。", "name": "keyword", "required": true, "sub_params": [], "type": "string"}, {"description": "获取模版的起始位置。", "name": "offset", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.94633, "call_amount": 978649}}]}, {"name": "国内快递查询", "id": "7329788013695860747", "description": "快递状态查询", "category": "便利生活", "is_official": true, "is_free": true, "heat": 10959, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["国内快递查询，顾名思义，根据公司名和快递单号查询快递状态。", "Tools：", "ExpressDeliveryPlugin：查询快递状态。", "*"]}, "tools": [{"name": "ExpressDeliveryPlugin", "id": "7362084343025532964", "description": "查询快递状态，返回值为空代表未查到快递信息，请检查快递单号是否正确", "parameters": [{"description": "快递公司名称", "name": "express_name", "required": false, "sub_params": [], "type": "string"}, {"description": "快递单号，只能包含数字和英文字母，如果不合法请提示“输入正确的快递单号”", "name": "express_number", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 55514}}]}, {"name": "水滴信用", "id": "7361779242348347442", "description": "水滴信用官方插件，提供一站式解锁企业大数据多维度洞察，精准查询市场主体数据、工商照面详情，深挖股东脉络与主要人员信息，跟踪企业变更记录。一键扫描企业风险，包括司法案件、被执行人、破产重组、严重违法、行政处罚、经营异常。挖掘企业营销线索，获取企业电话等联系方式。", "category": "金融与商业", "is_official": true, "is_free": true, "heat": 10840, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["水滴信用是一个与合作方共同开发的官方插件，本插件可为您一站式解锁企业大数据多维度洞察！精准查询市场主体数据、工商照面详情，深挖股东脉络与主要人员信息，跟踪企业变更记录。一键扫描企业风险，包括司法案件、被执行人、破产重组、严重违法、行政处罚、经营异常。挖掘企业营销线索，获取企业电话等联系方式。", "Tools：", "*", "search：根据企业名称获取工商照面详情信息，包括统一社会信用代码、成立日期、经营状态、企业类型、登记地址、注册资本等。"]}, "tools": [{"name": "search_changes", "id": "7376252580202135564", "description": "查询公司的变更记录", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.96522, "call_amount": 76546}}, {"name": "search", "id": "7361782659560849458", "description": "获取公司名称，搜索公司详细信息", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.85278, "call_amount": 27794770}}, {"name": "search_phone", "id": "7434832183438622760", "description": "根据公司名称查询企业手机号", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.98188, "call_amount": 644232}}, {"name": "search_employees", "id": "7376252580202151948", "description": "根据公司名搜索公司的主要成员", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.98547, "call_amount": 8340013}}, {"name": "search_xzcf", "id": "7376252580202102796", "description": "查询公司是否有行政处罚", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.95953, "call_amount": 74597}}, {"name": "search_illegals", "id": "7376252580202070028", "description": "查询公司是否有严重违法信息", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.96379, "call_amount": 74637}}, {"name": "search_operations", "id": "7376252580202086412", "description": "查询公司是否有经营异常", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.95782, "call_amount": 83140}}, {"name": "search_judicial_list", "id": "7434832183438606376", "description": "根据公司名称查询司法信息", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.95898, "call_amount": 75571}}, {"name": "search_clears", "id": "7376252580202037260", "description": "查询公司的清算信息", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.95187, "call_amount": 56421}}, {"name": "search_partners", "id": "7376252580202168332", "description": "根据公司名搜索公司对应的股东", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.96426, "call_amount": 108493}}, {"name": "search_executes", "id": "7376252580202053644", "description": "查询公司的被执行信息", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.96487, "call_amount": 80561}}, {"name": "search_bankruptcy_public_list", "id": "7376252580202119180", "description": "查询公司破产重组的信息", "parameters": [{"description": "公司名称", "name": "company_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.96133, "call_amount": 66254}}]}, {"name": "ChatPPT", "id": "7412468645747572774", "description": "ChatPPT，是一款在线Chat指令式生成PPT产品。通过简单的对话式输入，即可轻松完成PPT文档在线生成、编辑、美化、排版、导出、一键动效、协作等超400+类操作，同时支持基于主题词或其他文档（word、脑图、在线链接、大纲文本等）生成PPT，目前支持500字以内生成指令的解析。", "category": "实用工具", "is_official": true, "is_free": false, "heat": 10718, "entity_id": "", "favorite_count": 0, "readme": {"overview": "仅需一句话指令，即可自动生成结构完整、内容精炼、设计美观的PPT演示文稿。无论是商业汇报、学术分享还是日常演讲，都能轻松应对，助您瞬间提升专业形象，节省大量时间于内容创作与设计调整上。", "features": ["【示例指令】", "生成一份奶茶消费者洞察ppt绿色风格", "生成一份人工智能PPT", "插入一个大数据市场ppt，深蓝色主题"]}, "tools": [{"name": "ChatPPT", "id": "7431078573601128475", "description": "仅需一句话指令，即可自动生成结构完整、内容精炼、设计美观的PPT演示文稿。无论是商业汇报、学术分享还是日常演讲，都能轻松应对，助您瞬间提升专业形象，节省大量时间于内容创作与设计调整上。", "parameters": [{"description": "文档主题, default value is 介绍ChatPPT", "name": "text", "required": true, "sub_params": [], "type": "string"}, {"description": "主题颜色, default value is 蓝色", "name": "color", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.96408, "call_amount": 33606}}]}, {"name": "字幕获取", "id": "7403655462140264486", "description": "根据视频的语音来识别字幕", "category": "实用工具", "is_official": true, "is_free": true, "heat": 10076, "entity_id": "", "favorite_count": 0, "readme": {"overview": "根据视频的语音来识别字幕", "features": []}, "tools": [{"name": "generate_video_captions_sync", "id": "7403656762315948070", "description": "根据视频的语音来生成字幕\n", "parameters": [{"description": "视频语言，如汉语、英语等, default value is 汉语", "name": "lang", "required": false, "sub_params": [], "type": "string"}, {"description": "视频链接", "name": "url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.49866, "call_amount": 7422120}}, {"name": "generate_video_captions", "id": "7403656762315931686", "description": "根据视频的语音来生成字幕", "parameters": [{"description": "视频语言，如汉语、英语等, default value is 汉语", "name": "lang", "required": false, "sub_params": [], "type": "string"}, {"description": "视频链接", "name": "url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 28320}}]}, {"name": "提示词优化", "id": "7439196919706075136", "description": "智能优化图像提示词", "category": "图像", "is_official": true, "is_free": false, "heat": 9888, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["本插件名为“提示词优化”，其主要功能是智能优化图像提示词。通过先进的算法和自然语言处理技术，能够对用户提供的提示词进行分析和处理，使其更加准确、生动、富有表现力，从而帮助用户在图像生成过程中获得更符合预期的高质量图像。", "工具介绍：", "*", "工具名称：sd_better_prompt"]}, "tools": [{"name": "sd_better_prompt", "id": "7439197952104726528", "description": "智能优化图像提示词", "parameters": [{"description": "prompt", "name": "prompt", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99928, "call_amount": 1047026}}]}, {"name": "国内银行利率", "id": "7329411028809334825", "description": "银行利率查询，可查询指定银行的贷款利率、存款利率、公积金利率", "category": "金融与商业", "is_official": true, "is_free": true, "heat": 9497, "entity_id": "", "favorite_count": 0, "readme": {"overview": "*", "features": ["插件名称：", "*", "国内银行利率", "*"]}, "tools": [{"name": "BankInterestRate", "id": "7379228576949927976", "description": "银行利率查询，可查询指定银行的贷款利率、存款利率、公积金利率", "parameters": [{"description": "银行名。比如农业银行、工商银行、招商银行、中国银行", "name": "bank", "required": true, "sub_params": [], "type": "string"}, {"description": "查询日期。比如：2023/01/02", "name": "time", "required": false, "sub_params": [], "type": "string"}, {"description": "利率类型。枚举值：存款利率、商业贷款利率、公积金贷款利率", "name": "type", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 9007}}]}, {"name": "追书", "id": "7371340340680245286", "description": "根据描述返回符合要求的小说", "category": "便利生活", "is_official": true, "is_free": true, "heat": 9361, "entity_id": "", "favorite_count": 0, "readme": {"overview": "据描述返回符合要求的小说。", "features": ["*", "使用示例：", "*", "1.好评率最高的关于男生爱看的武侠类小说，要已完结的并且免费的，最好字数不要太多。"]}, "tools": [{"name": "getBooks", "id": "7371344578504605747", "description": "获取符合要求的小说信息。", "parameters": [{"description": "起始条数 从0开始", "name": "start", "required": true, "sub_params": [], "type": "integer"}, {"description": "标签，多个则英文逗号隔开，不传则表示全部", "name": "tag", "required": false, "sub_params": [], "type": "string"}, {"description": "书籍名称（模糊搜索）", "name": "bookName", "required": false, "sub_params": [], "type": "string"}, {"description": "每页条数  最大 100", "name": "limit", "required": true, "sub_params": [], "type": "integer"}, {"description": "书籍男女频  1：male  2：female", "name": "gender", "required": false, "sub_params": [], "type": "integer"}, {"description": "是否付费 1：免费(免费看), 3:付费", "name": "price", "required": false, "sub_params": [], "type": "integer"}, {"description": "排序方式 1：人气排序（默认），2：留存排序，3：评分排序，4：字数排序，默认为1", "name": "sort", "required": false, "sub_params": [], "type": "integer"}, {"description": "书籍状态， 1: 完结, 2: 连载", "name": "status", "required": false, "sub_params": [], "type": "integer"}, {"description": "最近更新时间， 3： 三天内，7：7天内，15: 15天内，30： 30天内", "name": "updated", "required": false, "sub_params": [], "type": "integer"}, {"description": "字数，是一个数字，字数范围  1: 0~20w; 2: 20w~50w; 3: 50w~100w; 4: 100w~200w; 5: 200w以上  6: 0~100万; 7: 100万~300万; 8: 300万以上; 9: 0~50万; 10: 50万~200万", "name": "wordCount", "required": false, "sub_params": [], "type": "integer"}, {"description": "作者名（精确搜索）", "name": "author", "required": false, "sub_params": [], "type": "string"}, {"description": "书籍id（精确搜索）", "name": "bookId", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99823, "call_amount": 15728}}]}, {"name": "掌上高考", "id": "7366928721615372329", "description": "提供掌上高考官方数据查询，例如【高考日程】、【大学信息】、【专业信息】、【志愿填报】。", "category": "科学与教育", "is_official": true, "is_free": true, "heat": 9273, "entity_id": "", "favorite_count": 0, "readme": {"overview": "提供掌上高考官方数据查询，例如【高考日程】、【大学信息】、【专业信息】、【志愿填报】。", "features": ["*", "对于school_choose插件的elective参数，需要区分新老高考，对于老高考的省份（山西,河南,陕西,内蒙古,四川,云南,宁夏,青海,西藏,新疆）需要输入文科、理科，对于新高考的省份，需要输入三科如（物理,化学,生物）。", "*", "使用示例："]}, "tools": [{"name": "school_major", "id": "7394744595206406163", "description": "用来查询某所大学开设了哪些专业的工具", "parameters": [{"description": "query中要查询的专业层次，例如：【本科】、【专科】、【专科（高职）】", "name": "education_level", "required": false, "sub_params": [], "type": "string"}, {"description": "query中要查询的专业大类，例如：【哲学类】", "name": "major_category", "required": false, "sub_params": [], "type": "string"}, {"description": "query中要查询的的某个专业的code码", "name": "major_code", "required": false, "sub_params": [], "type": "string"}, {"description": "query中要查询的专业名称", "name": "major_name", "required": false, "sub_params": [], "type": "string"}, {"description": "query中要查询的专业门类，例如：【哲学】", "name": "major_subcategory", "required": false, "sub_params": [], "type": "string"}, {"description": "query中要查询的学校名称", "name": "school_name", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 14831}}, {"name": "school_list", "id": "7394744595206389779", "description": "提供学校列表的查询，查询条件包括： - 院校所属： 全部省级行政区 - 院校类型：综合、理工、农林、医药、师范、语言、财经、政法、体育、艺术、民族、军事、其他 - 办学类型：普通本科、专科（高职）、公办、民办、中外合作办学、内地与港澳台地区合作办学 - 院校特色：985、211、双一流、教育部直属、中央部委、强基计划、双高计划", "parameters": [{"description": "用户查询教育部直属院校时，固定值为：1", "name": "department", "required": false, "sub_params": [], "type": "string"}, {"description": "用户查询双高计划时，固定值为：1", "name": "is_doublehigh", "required": false, "sub_params": [], "type": "string"}, {"description": "用户查询办学性质时，可用的办学性质如下：公办、民办、中外合作办学、内地与港澳台地区合作办学", "name": "nature", "required": false, "sub_params": [], "type": "string"}, {"description": "用户查询双一流院校时，固定值为：1", "name": "is_dual_class", "required": false, "sub_params": [], "type": "string"}, {"description": "用户查询中的的省份名称，使用省份简写，例如：山东，新疆", "name": "province", "required": false, "sub_params": [], "type": "string"}, {"description": "用户查询办学类型时，可用的办学类型如下：综合类、理工类、农林类、医药类、师范类、语言类、财经类、政法类、体育类、艺术类、民族类", "name": "school_type", "required": false, "sub_params": [], "type": "string"}, {"description": "用户查询强基计划院校时，固定值为：1", "name": "admissions", "required": false, "sub_params": [], "type": "string"}, {"description": "用户查询中央部委院校时，固定值为：1", "name": "central", "required": false, "sub_params": [], "type": "string"}, {"description": "用户查询中的地级市名称，转换成国家行政地区标准名称，例如：济南市", "name": "city", "required": false, "sub_params": [], "type": "string"}, {"description": "用户查询211院校时，固定值为：211", "name": "f211", "required": false, "sub_params": [], "type": "string"}, {"description": "用户查询985院校时，固定值为：985", "name": "f985", "required": false, "sub_params": [], "type": "string"}, {"description": "用户查询院校类型时，可用的院校类型如下：普通本科、专科（高职）", "name": "type", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 11258}}, {"name": "school_choose", "id": "7394744595206373395", "description": "根据用户的省份、分数、选科信息，智能的为用户推荐适合的学校", "parameters": [{"description": "用户query中的分数", "name": "score", "required": true, "sub_params": [], "type": "string"}, {"description": "用户query中的文理科或者选科组成的由英文逗号分隔的数组，根据不同的省份可以是：【理科】、【文科】、【专科】、【本科】、【物理】、【历史】、【化学】、【生物】、【政治】、【地理】、【技术】。例如：\"物理,化学,生物\"。山西,河南,陕西,内蒙古,四川,云南,宁夏,青海,西藏,新疆使用老高考需要输入【理科】或【文科】，其他省份需要输入除【理科】、【文科】之外的三科。", "name": "elective", "required": true, "sub_params": [], "type": "string"}, {"description": "用户query中想要查询的院校特色，可以是：【985】、【211】、【双一流】、【教育部直属】、【中央部委】、【强基计划】", "name": "feature", "required": false, "sub_params": [], "type": "string"}, {"description": "用户query中的省份", "name": "province_name", "required": true, "sub_params": [], "type": "string"}, {"description": "用户query中的录取概率，可以是：【概率小】、【概率中】、【概率大】", "name": "recom", "required": false, "sub_params": [], "type": "string"}, {"description": "用户query中想要查询的学校所属的省份", "name": "school_province_name", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.91119, "call_amount": 35117}}, {"name": "major", "id": "7394744595206357011", "description": "用来查询大学所学的某个专业的基本信息", "parameters": [{"description": "query中用户要求的排序方式，例如：【人气排序】、【薪酬排序】", "name": "sort", "required": false, "sub_params": [], "type": "string"}, {"description": "query中要查询的专业层次，例如：【本科】、【专科】、【专科（高职）】", "name": "education_level", "required": false, "sub_params": [], "type": "string"}, {"description": "query中要查询的专业大类，例如：【哲学类】", "name": "major_category", "required": false, "sub_params": [], "type": "string"}, {"description": "query中要查询的某个专业的code码", "name": "major_code", "required": false, "sub_params": [], "type": "string"}, {"description": "query中要查询的专业名称", "name": "major_name", "required": false, "sub_params": [], "type": "string"}, {"description": "query中要查询的专业门类，例如：【哲学】", "name": "major_subcategory", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 11743}}]}, {"name": "画质提升", "id": "7438834453352529946", "description": "4倍提升画面质量", "category": "图像", "is_official": true, "is_free": false, "heat": 8271, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["本插件主要用于提升图片的画质，能够将图片的清晰度提高 4 倍，让图片呈现出更加清晰、细腻的效果。", "插件说明", "工具介绍：", "*"]}, "tools": [{"name": "image_quality_improve", "id": "7438835880728543282", "description": "提升图像清晰度", "parameters": [{"description": "上传图片", "name": "image_url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.88054, "call_amount": 284576}}]}, {"name": "智能换脸", "id": "7438916871795228722", "description": "为图片替换参考图的人脸", "category": "图像", "is_official": true, "is_free": false, "heat": 8021, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["智能换脸是一款强大的图片处理插件，它能够将图片中的人脸替换为其他参考图中的人脸。通过这个插件，用户可以轻松实现人脸的替换和融合，创造出各种有趣和创意的图片效果。无论是想要给自己的照片添加新的表情和风格，还是进行影视特效制作、广告设计等专业应用，智能换脸都能满足你的需求。", "工具介绍：", "*", "工具名称：swap_face"]}, "tools": [{"name": "swap_face", "id": "7438917954420637723", "description": "为图片替换参考图的人脸", "parameters": [{"description": "参考图", "name": "reference_picture_url", "required": true, "sub_params": [], "type": "string"}, {"description": "模板图", "name": "template_picture_url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.97748, "call_amount": 1228370}}]}, {"name": "什么值得买", "id": "7355060468744044595", "description": "帮助用户查询商品的优惠信息，根据用户输入的商品相关提问，返回商品概况、价格、购买渠道、性价比推荐等信息，并给出优惠商品的链接地址。", "category": "便利生活", "is_official": true, "is_free": true, "heat": 7414, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["什么值得买是一个购物插件，可以帮助用户查询商品的优惠信息，根据用户输入的商品相关提问，返回商品概况、价格、购买渠道、性价比推荐等信息，并给出优惠商品的链接地址。", "Tools：", "smzdm_haojia_articles", "输入："]}, "tools": [{"name": "smzdm_haojia_articles", "id": "7355068167946960905", "description": "帮助用户查询商品的优惠信息，根据用户输入的商品相关提问，返回商品概况、价格、购买渠道、性价比推荐等信息，并给出优惠商品的链接地址", "parameters": [{"description": "最低价，如“1000”，“1500”", "name": "min_price", "required": false, "sub_params": [], "type": "string"}, {"description": "最高价，如“5000”，“5500”", "name": "max_price", "required": false, "sub_params": [], "type": "string"}, {"description": "搜索词，商品的品类词、品牌词、型号词等，如手机、iPhone、iphone15", "name": "Query", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99925, "call_amount": 120130}}]}, {"name": "智联招聘", "id": "7356152229616730149", "description": "根据您的信息推荐招聘信息", "category": "便利生活", "is_official": true, "is_free": true, "heat": 7347, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["智联招聘是Coze与合作方开发的官方插件，这个插件可以帮助用户根据职位名称、薪水、工作地点、学历、工作经验、公司名称、公司性质等条件搜索智联招聘上提供的招聘信息。", "Tools：", "get_job：根据职位名称、薪水、工作地点、学历、工作经验、公司名称、公司性质等条件搜索智联招聘上提供的招聘信息", "*"]}, "tools": [{"name": "get_job", "id": "7356153301232255002", "description": "帮助用户根据职位名称、薪水、工作地点、学历、工作经验、公司名称、公司性质等条件搜索智联招聘上提供的招聘信息", "parameters": [{"description": "城市，如北京，上海", "name": "city", "required": false, "sub_params": [], "type": "string"}, {"description": "请输入数字代表公司类型，当用户没有提供这个信息时，这个字段不填写。例如：1代表国企。可供选择的类型包括：1. 国企、2. 外商独资、3. 代表处、4. 合资、5. 民营、6. 国家机关、7. 其他、8. 股份制企业、9. 上市公司、10. 事业单位、11. 银行、12. 医院、13. 学校/下级学院、14. 律师事务所、15. 社会团体、16. 港澳台公司。", "name": "companyType", "required": false, "sub_params": [], "type": "integer"}, {"description": "必须是职位的名称，如园艺师", "name": "job<PERSON>ame", "required": true, "sub_params": [], "type": "string"}, {"description": "页数，默认值为1，当用户请求更多时输入2，3依次增加, default value is 1", "name": "page", "required": false, "sub_params": [], "type": "integer"}, {"description": "请输入数字代表工作经验，例如：1（代表无经验）。枚举值：1-无经验，2-1年以下，3-1-3年，4-3-5年，5-5-10年，6-10年以上。", "name": "workExperience", "required": false, "sub_params": [], "type": "integer"}, {"description": "公司名称", "name": "companyName", "required": false, "sub_params": [], "type": "string"}, {"description": "请输入代表学历的整型值。例如，“1”代表不限。枚举值包括：1-不限、2-初中及以下、3-中技、4-高中、5-中专/中技、6-大专、7-本科、8-硕士、9-MBA/EMBA、10-EMBA、11-博士、12-其他。", "name": "education", "required": false, "sub_params": [], "type": "integer"}, {"description": "每页返回多少个，默认值为5, default value is 5", "name": "page_size", "required": false, "sub_params": [], "type": "integer"}, {"description": "输入你期望的薪水下限，用数字表示，例如:3000,30000,300000，单位为人民币元。", "name": "salaryMin", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.99195, "call_amount": 92170}}]}, {"name": "智能抠图", "id": "7438917083918024738", "description": "自动去背景，自定义抠图对象", "category": "图像", "is_official": true, "is_free": false, "heat": 7336, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["智能抠图插件是一款功能强大的工具，它能够自动去除图片的背景，并且支持自定义抠图对象。用户只需提供待抠图的图片，插件即可快速准确地完成抠图任务，为用户提供高效便捷的图片处理体验。", "工具介绍：", "*", "工具名称：cutout"]}, "tools": [{"name": "cutout", "id": "7438919188246429731", "description": "", "parameters": [{"description": "结果图尺寸，支持返回抠图结果尺寸(去除透明长宽)和原图尺寸, enum list is [0,3], default value is 0", "name": "only_mask", "required": false, "sub_params": [], "type": "integer"}, {"description": "输出图模式，可选透明背景图/蒙版矢量图, enum list is [0,1], default value is 0", "name": "output_mode", "required": false, "sub_params": [], "type": "integer"}, {"description": "自定义抠图内容的提示词，不填时默认保留主体抠图", "name": "prompt", "required": false, "sub_params": [], "type": "string"}, {"description": "待抠图的图片", "name": "url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.97931, "call_amount": 5476781}}]}, {"name": "幸福里", "id": "7329411028809187369", "description": "提供二手房、新房、租房信息的插件，想要查询某个城市、区域、户型的房产信息时，可以使用此插件", "category": "便利生活", "is_official": true, "is_free": true, "heat": 7155, "entity_id": "", "favorite_count": 0, "readme": {"overview": "幸福里是一款提供二手房、新房、租房信息的插件。如果你想查询某个城市、区域、户型的房产信息，可以使用此插件。", "features": ["它的主要功能包括：", "*", "二手房查询", "：输入二手房的城市、户型、小区名称等，可以获取二手房所在的区域、房子图片、二手房总价、每平米价格等信息。"]}, "tools": [{"name": "HouseRenting", "id": "7379228483471409202", "description": "通过此工具获取租房的信息，包括房子所在的区域，小区名称，房子图片，租房的价格", "parameters": [{"description": "期望查询的区域、如梅溪湖、三里屯", "name": "region", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询的城市", "name": "city", "required": true, "sub_params": [], "type": "string"}, {"description": "期望查询的行政区、如海淀、昌平", "name": "district", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询的房间布局，如1居、2居、3居等", "name": "house_layout", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询的小区名称", "name": "house_name", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 5156}}, {"name": "SecondHandHouse", "id": "7379228483471441970", "description": "如果想要购买二手房或者查询二手房信息的时候可以使用此工具，输入二手房的城市、户型、小区名称等，可以获取二手房所在的区域，房子图片，二手房总价，每平米价格等信息", "parameters": [{"description": "期望查询的城市", "name": "city", "required": true, "sub_params": [], "type": "string"}, {"description": "期望查询的行政区，如海淀、朝阳", "name": "district", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询的房子结构，如1居、2居等", "name": "house_layout", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询的小区名称", "name": "house_name", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询的区域，如三里屯、十里河", "name": "region", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 7476}}, {"name": "NewHouse", "id": "7379228483471425586", "description": "如果你想买新房，可以先用这个工具获取新房的信息，输入想要的户型、区域、城市、小区名称等，可以获取新房的具体信息，包括房子图片，价格，每平米价格等", "parameters": [{"description": "期望查询的城市", "name": "city", "required": true, "sub_params": [], "type": "string"}, {"description": "期望查询的行政区，如海淀、朝阳", "name": "district", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询的房间布局", "name": "house_layout", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询的小区名称", "name": "house_name", "required": false, "sub_params": [], "type": "string"}, {"description": "期望查询的区域，如三里屯，十里河", "name": "region", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 3356}}]}, {"name": "OCR火山版", "id": "7454455001315033122", "description": "【OCR火山版】是一个强大的光学字符识别（OCR）插件，提供两种主要工具：常规OCR工具和多语言OCR工具。该插件能够从图像或PDF文档中提取文本信息，支持多种语言，适用于各种应用场景。", "category": "实用工具", "is_official": true, "is_free": true, "heat": 6443, "entity_id": "", "favorite_count": 0, "readme": {"overview": "【OCR火山版】是一个强大的光学字符识别（OCR）插件，提供两种主要工具：常规OCR工具和多语言OCR工具。该插件能够从图像或PDF文档中提取文本信息，支持多种语言，适用于各种应用场景。", "features": []}, "tools": [{"name": "multilingual_ocr", "id": "7454457295544795170", "description": "多语种OCR服务支持中英文、日语、法语、德语、俄语、西班牙语等50+语种的文字识别能力。向客户提供文字识别结果、语种等多种关键信息。为多语种场景下的图片文字识别、提取提供完整解决方案。", "parameters": [{"description": "image或pdf链接", "name": "url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.9972, "call_amount": 62974}}, {"name": "general_ocr", "id": "7454457295544778786", "description": "通用文字识别服务提供「从图片到文字」的完整解决方案，可识别中英文内容，不仅在高并发环境下以毫秒级的速度进行识别，还可精准识别截图、扫描、拍照、视频抽帧等多类型图片。", "parameters": [{"description": "image或pdf链接", "name": "url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99987, "call_amount": 552379}}]}, {"name": "淘票票", "id": "7340117002398285843", "description": "使用这个插件可以去淘票票网站搜索正在上映的/即将上映的影片、演出，注意：无法搜索过去的已下映的影片，不支持电影名称关键字搜索。", "category": "便利生活", "is_official": true, "is_free": true, "heat": 6345, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["*", "影片检索：用户通过输入页码和页大小这两个参数，可以访问淘票票的影片信息库，获取正在上映或即将上映的影片。", "详细影片信息获取：无需手动筛选，直接返回影片名称、剧情简介、主演、导演、影片类型等一系列详细信息。", "评价系统：返回影片的评分、评分人数及想看人数等评价信息，帮助用户快速评估影片口碑。"]}, "tools": [{"name": "GetMovieAndShow", "id": "7376239842050490418", "description": "获取正在上映/即将上映的影片/演出，注意：无法搜索过去的、已下映的影片。", "parameters": [{"description": "页码，默认从0开始", "name": "pageIndex", "required": true, "sub_params": [], "type": "integer"}, {"description": "页大小，只支持1~30，默认输入10", "name": "pageSize", "required": true, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 1, "call_amount": 207143}}]}, {"name": "音乐生成", "id": "7449355471418359845", "description": "音乐生成插件，可以根据用户输入或歌词生成歌曲", "category": "实用工具", "is_official": true, "is_free": false, "heat": 5921, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["豆包音乐大模型是一款可以根据用户输入生成音乐的插件。它能够根据用户提供的歌词提示词或歌词、演唱者性别、音乐曲风以及歌曲的情感风格等信息，创作出符合要求的音乐作品。", "工具介绍：", "*", "gen_song"]}, "tools": [{"name": "gen_song", "id": "7449356525468106762", "description": "音乐生成", "parameters": [{"description": "歌曲的情感风格，影响旋律和编曲的情绪表达。可选值Happy、Dynamic/Energetic、Sentimental/Melancholic/Lonely、Inspirational/Hopeful、Nostalgic/Memory、Excited、Sorrow/Sad、Chill、Romantic", "name": "<PERSON><PERSON>", "required": false, "sub_params": [], "type": "string"}, {"description": "歌词提示词内容，仅支持中文，小于500个字符。, default value is 关于星空的歌", "name": "Prompt", "required": true, "sub_params": [], "type": "string"}, {"description": "演唱者性别，影响生成歌曲的声音特征。可选值Male（男声）、Female（女声）, default value is Male", "name": "Gender", "required": false, "sub_params": [], "type": "string"}, {"description": "音乐曲风，决定生成歌曲的音乐风格。可选值Folk、Pop、Rock、Chinese Style、Hip Hop/Rap、R&B/Soul、Punk、Electronic、Jazz、Reggae、DJ, default value is R&B/Soul", "name": "Genre", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99952, "call_amount": 41997}}, {"name": "lyrics_gen_song", "id": "7459737514975428658", "description": "根据歌词生成音乐", "parameters": [{"description": "歌曲的情感风格，影响旋律和编曲的情绪表达。可选值Happy、Dynamic/Energetic、Sentimental/Melancholic/Lonely、Inspirational/Hopeful、Nostalgic/Memory、Excited、Sorrow/Sad、Chill、Romantic", "name": "<PERSON><PERSON>", "required": false, "sub_params": [], "type": "string"}, {"description": "生成歌曲的音色，可选值Warm、<PERSON>、<PERSON><PERSON>、Electrified voice、Sweet_AUDIO_TIMBRE、Cute_AUDIO_TIMBRE、Loud and sonorous、Powerful、Sexy/Lazy", "name": "Timbre", "required": false, "sub_params": [], "type": "string"}, {"description": "生成指定音频时长，单位：s，生效范围[30，240]", "name": "Duration", "required": false, "sub_params": [], "type": "integer"}, {"description": "演唱者性别，影响生成歌曲的声音特征。可选值Male（男声）、Female（女声）", "name": "Gender", "required": false, "sub_params": [], "type": "string"}, {"description": "音乐曲风，决定生成歌曲的音乐风格。可选值Folk、Pop、Rock、Chinese Style、Hip Hop/Rap、R&B/Soul、Punk、Electronic、Jazz、Reggae、DJ", "name": "Genre", "required": false, "sub_params": [], "type": "string"}, {"description": "歌词内容，仅支持中文，5到700个字符，支持通过歌词精准生成。", "name": "Lyrics", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99968, "call_amount": 21998}}]}, {"name": "背景替换", "id": "7438917173386428450", "description": "为图片替换背景图", "category": "图像", "is_official": true, "is_free": false, "heat": 5892, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["这款名为“背景替换”的插件，能够为图片替换背景图。它可以将主体图放置在新的背景图之上，实现图片背景的转换，为您带来全新的视觉效果。", "工具介绍：", "*", "工具名称：background_change"]}, "tools": [{"name": "background_change", "id": "7438917859713155107", "description": "为图片替换背景图", "parameters": [{"description": "背景图", "name": "background_image", "required": true, "sub_params": [], "type": "string"}, {"description": "主体图", "name": "front_image", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.9025, "call_amount": 139425}}]}, {"name": "英文文本转语音", "id": "7361307169926709300", "description": "将输入的英文转为各种语色的语音，插件也支持中文，但是“中文文本转语音”读得更好哦，快来看看吧～", "category": "实用工具", "is_official": true, "is_free": true, "heat": 5585, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["英文文本转语音是一个将用户输入的英文文本转为mp3音频链接的插件。", "Tools：", "输入：text：用户想要转为音频的英文文本", "*"]}, "tools": [{"name": "charlie_voice", "id": "7361316979153879055", "description": "将你输入的文本转化为磁性青年的声音", "parameters": [{"description": "用户想要转化为声音的文本", "name": "text", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99946, "call_amount": 133875}}, {"name": "magis_sophie", "id": "7361316980961312803", "description": "将英文文本转化为充满魅力的sophie声音", "parameters": [{"description": "想要转化为语音的文本", "name": "text", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99989, "call_amount": 193027}}, {"name": "skye_voice", "id": "7361316980961329187", "description": "输入一段文本，转化为女生skye的声音", "parameters": [{"description": "想要转化为语音的文本", "name": "text", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99701, "call_amount": 154260}}, {"name": "wennuan_gavin", "id": "7361316980961345571", "description": "将输入的文本转化为温柔的男声", "parameters": [{"description": "用户想要转化为声音的文本", "name": "text", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99981, "call_amount": 56913}}]}, {"name": "时间工具", "id": "7404671383978131506", "description": "可以进行获取指定时区的时间、转换时间至指定时区、时间格式转换的操作。", "category": "实用工具", "is_official": true, "is_free": true, "heat": 5412, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件介绍", "features": ["Time Tools 是一款功能强大的插件，可让您以各种方式处理时间。您可以获取指定时区的时间，将时间转换为不同的时区，甚至转换时间格式。使用 Time Tools，管理与时间相关的任务变得轻而易举。", "工具介绍", "convert_format：此工具可让您轻松转换时间格式。您可以指定原始格式、要转换的时间和目标格式。当您需要更改时间格式以用于显示或处理目的时，这很有用。", "convert_timezone：使用此工具，您可以将给定的时间转换为不同时区的时间。指定原始时区、要转换的时间（或使用指定时区的默认当前时间）和目标时区。当您需要处理不同位置的时区时，此工具非常有用。"]}, "tools": [{"name": "get_current_time", "id": "7404677538204893221", "description": "获取指定时区的时间。", "parameters": [{"description": "时区，格式为UTC、UTC+1、UTC-1等，不填则使用当前环境的默认时区。", "name": "time_zone", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99998, "call_amount": 26677805}}, {"name": "convert_format", "id": "7404677538204860453", "description": "时间格式转换。", "parameters": [{"description": "原始格式，不填则默认使用yyyy-mm-dd hh:MM:ss格式。", "name": "from_format", "required": false, "sub_params": [], "type": "string"}, {"description": "需要转换的时间，不填则默认使用当前环境时区的时间，格式采用from_format的格式。", "name": "time", "required": false, "sub_params": [], "type": "string"}, {"description": "目标格式，不填则默认使用yyyy-mm-dd hh:MM:ss格式。", "name": "to_format", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.98817, "call_amount": 71303}}, {"name": "convert_timezone", "id": "7404677538204876837", "description": "将给定的时间转换为相应时区下的时间。", "parameters": [{"description": "原始时区，格式为UTC、UTC+1、UTC-1等，不填则使用当前环境的默认时区。", "name": "from_timezone", "required": false, "sub_params": [], "type": "string"}, {"description": "需要转换的时间，不填则默认使用from_timezone代表时区的当前时间，只支持yyyy-mm-dd hh:MM:ss。", "name": "time", "required": false, "sub_params": [], "type": "string"}, {"description": "目标时区，格式为UTC、UTC+1、UTC-1等，不填则使用当前环境的默认时区。", "name": "to_timezone", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.97368, "call_amount": 7921}}]}, {"name": "豆包图像生成大模型个人版", "id": "7460421184786743306", "description": "豆包图像大模型是一款强大的AI图像助手,提供两大核心功能：1) 图片编辑 - 上传图片并输入修改指令,AI就能按照您的要求智能编辑图片；2) 图片生成 - 输入文字描述,AI就能为您创作出独特的图像作品。无论是修改已有图片还是从零创作,豆包都能帮您轻松实现图像创意。", "category": "图像", "is_official": true, "is_free": true, "heat": 4979, "entity_id": "", "favorite_count": 0, "readme": {"overview": "豆包图像大模型是一款强大的AI图像助手,提供两大核心功能：1) 图片编辑 - 上传图片并输入修改指令,AI就能按照您的要求智能编辑图片；2) 图片生成 - 输入文字描述,AI就能为您创作出独特的图像作品。无论是修改已有图片还是从零创作,豆包都能帮您轻松实现图像创意。", "features": ["*", "该插件支持传入个人火山账号ak、sk"]}, "tools": [{"name": "gen_image", "id": "7460422449386225702", "description": "根据用户提示生成图片", "parameters": [{"description": "用户个人火山账号的ak, default value is coze****ak", "name": "private_ak", "required": true, "sub_params": [], "type": "string"}, {"description": "用户个人火山账号的sk, default value is coze****sk", "name": "private_sk", "required": true, "sub_params": [], "type": "string"}, {"description": "用于生成图像的提示词 ，中英文均可输入 Prompt 中用引号将希望书写的文字引起来，文字准确率会更高（例如：一张圣诞节海报，上面写着“Merry Christmas”）", "name": "prompt", "required": true, "sub_params": [], "type": "string"}, {"description": "可枚举，包括[general_v20_9B_pe, general_v20_9B_rephraser, general_v20_9B_pe], default value is general_v20_9B_pe", "name": "req_schedule_conf", "required": true, "sub_params": [], "type": "string"}, {"description": "生成图像的宽 默认值：512 取值范围：[256, 768], default value is 512", "name": "width", "required": false, "sub_params": [], "type": "integer"}, {"description": "生成图像的高 默认值：512 取值范围：[256, 768], default value is 512", "name": "height", "required": false, "sub_params": [], "type": "integer"}, {"description": "开启文本扩写，会针对输入prompt进行扩写优化，如果输入prompt较短建议开启，如果输入prompt较长建议关闭 默认值：true, default value is true", "name": "use_pre_llm", "required": false, "sub_params": [], "type": "boolean"}, {"description": "true：文生图+AIGC超分 false：文生图 默认值：true, default value is true", "name": "use_sr", "required": false, "sub_params": [], "type": "boolean"}], "performance": {"success_rate": 0.9986, "call_amount": 14513}}, {"name": "seed_edit", "id": "7460422449386242086", "description": "指令编辑（SeedEdit）是一种能够使用任何文本提示修改给定图像的扩散模型，也是专为图像编辑任务设计的模型。在通用性、可控性、高质量等方面取得了新的突破，凭借创新、多尺度且多规则的数据获取和过滤方案，输入任意指令，即可实现精准编辑。", "parameters": [{"description": "用户个人火山账号的sk, default value is coze****sk", "name": "private_sk", "required": true, "sub_params": [], "type": "string"}, {"description": "用于编辑图像的提示词，建议：编辑指令使用自然语言即可 每次编辑使用单指令会更好 局部编辑时指令描述尽量精准，尤其是画面有多个实体的时候，描述清楚对谁做什么，能获取更精准的编辑效果 发现编辑效果不明显的时候，可以调整一下编辑强度scale，数值越大越贴近指令执行 尽量使用清晰的，分辨率高的底图，动漫及豆包模型生成的图片编辑效果会更好。", "name": "prompt", "required": true, "sub_params": [], "type": "string"}, {"description": "图片文件URL，此功能需输入1张图片", "name": "image_url", "required": true, "sub_params": [], "type": "string"}, {"description": "用户个人火山账号的ak, default value is coze****ak", "name": "private_ak", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.91667, "call_amount": 4845}}]}, {"name": "简历阅读器", "id": "7391829126862356492", "description": "帮你解析简历", "category": "实用工具", "is_official": true, "is_free": true, "heat": 4874, "entity_id": "", "favorite_count": 0, "readme": {"overview": "帮您解析简历。", "features": ["使用示例：", "1. 看一下这份简历的内容", "https://tosv.byted.org/obj/bot-studio-platform-plugin-sg/%E9%99%88%E5%9C%86%E5%9C%86.pdf", "2. 您对这份简历有什么建议吗"]}, "tools": [{"name": "ResumeParse", "id": "7391838163297173540", "description": "上传简历并进行解析", "parameters": [{"description": "文件名，包括文件后缀（后缀包括.pdf、.doc等）。示例：HR专员_北京王连连2年.pdf。 ", "name": "file_name", "required": true, "sub_params": [], "type": "string"}, {"description": "文件下载地址", "name": "file_url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.9976, "call_amount": 101706}}]}, {"name": "指令编辑", "id": "7436679907834626084", "description": "自然语言修改图片内容", "category": "图像", "is_official": true, "is_free": false, "heat": 4852, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["指令编辑是一款能够通过自然语言修改图片内容的插件。用户只需提供原图和对画面内容调整的建议，该插件即可自动对图片进行修改，无需用户具备专业的图片编辑技能。", "工具介绍：", "*", "工具名称：image_change"]}, "tools": [{"name": "image_change", "id": "7436681307989590028", "description": "自然语言修改图片内容", "parameters": [{"description": "原图", "name": "image_url", "required": true, "sub_params": [], "type": "string"}, {"description": "对于画面内容调整的建议", "name": "prompt", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.94452, "call_amount": 885660}}]}, {"name": "外汇走势", "id": "7329410795966742567", "description": "当你需要查询外汇走势的时候可以使用此插件查询", "category": "实用工具", "is_official": true, "is_free": true, "heat": 4651, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["提供外汇走势查询服务。", "工具介绍：", "*", "工具名称"]}, "tools": [{"name": "ForexTrendPlugin", "id": "7379227644304343066", "description": "外汇走势", "parameters": [{"description": "想要查询的起始货币", "name": "from", "required": true, "sub_params": [], "type": "string"}, {"description": "日期，格式为2024-07-15", "name": "time", "required": false, "sub_params": [], "type": "string"}, {"description": "目标货币", "name": "to", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 79308}}]}, {"name": "36氪", "id": "7380175430994690102", "description": "根据用户的描述搜索相应的文章", "category": "便利生活", "is_official": true, "is_free": true, "heat": 4229, "entity_id": "", "favorite_count": 0, "readme": {"overview": "根据用户的描述搜索相应的文章。", "features": ["*", "使用示例：", "*", "1.搜索关于历史事件的文章，按文章发布时间排序。"]}, "tools": [{"name": "search_36kr", "id": "7380180552768356393", "description": "根据用户的描述搜索相应的文章", "parameters": [{"description": "页面大小最大，不超过100。", "name": "pageSize", "required": false, "sub_params": [], "type": "integer"}, {"description": "希望搜索内容的关键字，例如：ai、机器学习、数据库等。", "name": "searchWord", "required": true, "sub_params": [], "type": "string"}, {"description": "排序方式，0:按时间排序，1:按相关度排序，默认为0", "name": "sort", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.99921, "call_amount": 115187}}, {"name": "get_detail", "id": "7380180552768340009", "description": "获取文章的详细信息", "parameters": [{"description": "内容id。", "name": "itemId", "required": true, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.98844, "call_amount": 87457}}]}, {"name": "星座乐合集", "id": "7349090832584245274", "description": "1.当需要查询星座运势的时候，用户必须输入对应星座和年月日等信息查询星座运势，当用户给的信息不完善的时候，给用户返回固定的话术：如果想知道具体星座运势，需要给我信息的是：xx座xxxx年xx月xx日运势或者xx座xxxx年运势\n2.当需要查询星座配对的时候，用户必须输入男女星座查询星座配对，当用户给的信息不完善的时候，给用户返回固定的话术：如果想知道具体星座配对结果，需要给我信息的是：xx座男和xx座女\n3.当需要查询生肖运势的时候，用户必须输入对应生肖和年月日等信息查询生肖运势，当用户给的信息不完善的时候，给用户返回固定的话术：如果想知道具体生肖运势，需要给我信息的是：属xx的xxxx年xx月xx日运势或者属xx的xxxx年运势\n4.当需要查询生肖配对的时候，用户必须输入男女生肖查询生肖配对，当用户给的信息不完善的时候，给用户返回固定的话术：如果想知道具体生肖配对结果，需要给我信息的是：属x男和属x女\n5.当需要查询周公解梦的时候，用户必须输入梦境描述（梦见xxxxx）去查询周公解梦，当用户给的信息不完善的时候，给用户返回固定的话术：如果想知道梦境解析，需要给我的信息是：梦见xxxx", "category": "游戏与娱乐", "is_official": true, "is_free": true, "heat": 4002, "entity_id": "", "favorite_count": 0, "readme": {"overview": "功能描述：", "features": ["星座乐合集是一款娱乐插件，用户可以输入星座、生肖来推测运势，还可以周公解梦。", "Tools：", "*", "1. 查询星座运势，用户必须输入对应星座和年月日等信息查询星座运势，当用户给的信息不完善的时候，给用户返回固定的话术：如果想知道具体星座运势，需要给我信息的是：xx座xxxx年xx月xx日运势或者xx座xxxx年运势"]}, "tools": [{"name": "xz_sxys", "id": "7349092802531229746", "description": "根据输入的生肖名称和查询年月日给出相关生肖对应的运势内容，如果返回的内容为空，给用户返回固定的话术：如果想获得自己生肖相关运势，需要给我信息的是： 属x的xx年xx月xx日生肖运势   属x的xxx年生肖运势  属x的xx月生肖运势", "parameters": [{"description": "查询的月，格式为mm，取值范围为[1,12]，默认值为1", "name": "month", "required": false, "sub_params": [], "type": "string"}, {"description": "查询的日，格式为dd，取值范围为[1,31]，默认值为31", "name": "day", "required": false, "sub_params": [], "type": "string"}, {"description": "生肖名称，例如鼠，取值范围为[鼠，牛，虎，兔，龙，蛇，马，羊，猴，鸡，狗，猪]", "name": "sxname", "required": true, "sub_params": [], "type": "string"}, {"description": "查询的年，格式为yyyy，例如1998，默认值为2024", "name": "year", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 27469}}, {"name": "xz_sxpair", "id": "7349092802531213362", "description": "当需要查询星座配对的时候，根据输入对应生肖男和生肖女即可调用生肖配对插件查询配对指数，当用户给的信息不完善的时候，给用户返回固定的话术：需要给我信息是：生肖x男和生肖x女的生肖配对", "parameters": [{"description": "女生生肖，例如鼠，取值范围为[鼠，牛，虎，兔，龙，蛇，马，羊，猴，鸡，狗，猪]", "name": "nv", "required": true, "sub_params": [], "type": "string"}, {"description": "男生生肖，例如牛，取值范围为[鼠，牛，虎，兔，龙，蛇，马，羊，猴，鸡，狗，猪]", "name": "nan", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 10989}}, {"name": "xz_pair", "id": "7349092802531196978", "description": "当需要查询星座配对的时候，根据输入对应星座男和星座女即可调用星座配对插件查询配对指数，当用户给的信息不完善的时候，给用户返回固定的话术：需要给我信息是：xx座男和xx座女的星座配对", "parameters": [{"description": "匹配男生星座，例如双子男，取值范围为[白羊男，金牛男，双子男，巨蟹男，狮子男，处女男，天秤男，天蝎男，射手男，摩羯男，水瓶男，双鱼男]", "name": "man", "required": true, "sub_params": [], "type": "string"}, {"description": "匹配女生星座，例如天蝎女，取值范围为[白羊女，金牛女，双子女，巨蟹女，狮子女，处女女，天秤女，天蝎女，射手女，摩羯女，水瓶女，双鱼女]", "name": "woman", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.96296, "call_amount": 11554}}, {"name": "xz_luck", "id": "7349092802531180594", "description": "当需要查询星座运势的时候，根据输入对应星座即可调用星座运势插件查询运势，用户必须输入对应星座和年月日等信息查询运势，当用户给的信息不完善的时候，给用户返回固定的话术：如果想知道具体星座运势，需要给我信息的是：xx座xxxx年xx月xx日星座运势  xx座xxxx年xx月星座运势  xx座xxxx年星座运势", "parameters": [{"description": "查询的年，格式为yyyy，例如1998，默认值为2024", "name": "year", "required": true, "sub_params": [], "type": "string"}, {"description": "查询的月，格式为mm，例如01，取值范围为[1,12]，默认值为1", "name": "month", "required": false, "sub_params": [], "type": "string"}, {"description": "查询的日，格式为dd，例如01，取值范围为[1,31]，默认值为31", "name": "day", "required": false, "sub_params": [], "type": "string"}, {"description": "星座名称，例如天蝎座，取值范围为[白羊座，金牛座，双子座，巨蟹座，狮子座，处女座，天秤座，天蝎座，射手座，摩羯座，水瓶座，双鱼座]", "name": "xzname", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 30963}}, {"name": "xz_zgjm", "id": "7349092802531246130", "description": "根据输入的解梦标题给出相关对应的解梦内容，如果返回的内容为空，给用户返回固定的话术：如果想了解自己梦境的详细解析，需要给我详细的梦见信息，例如： 梦见XXX", "parameters": [{"description": "查询解梦标题，例如：梦见蛇", "name": "title", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 12607}}]}, {"name": "Microsoft Outlook Calendar", "id": "7391846517373550619", "description": "您可以使用此插件设置Microsoft日历的日程安排，并获取信息以快速阅读Microsoft日历中的日程安排。", "category": "便利生活", "is_official": true, "is_free": true, "heat": 3467, "entity_id": "", "favorite_count": 0, "readme": {"overview": "Microsoft Outlook Calendar 是一款功能强大的插件，它可以帮助您轻松设置 Microsoft 日历的日程安排，并快速获取日程安排信息。您可以使用此插件创建、更新和删除事件，还可以根据关键词、时间等条件筛选日程信息。此外，该插件还支持添加参与者，让您更方便地与他人共享日程安排。", "features": []}, "tools": [{"name": "delete_event", "id": "7391848610280882213", "description": "delete event\n", "parameters": [{"description": "event id", "name": "eventId", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 66}}, {"name": "update_event", "id": "7391848610280914981", "description": "Update an event in Microsoft Calendar that allows you to add participants. You must get from other Microsoft Outlook Calendar tools, if no, a message is displayed indicating that the tool is unavailable.", "parameters": [{"description": "The event ID. For example, \"AQMkADAwATMwMAItMTdiYy03ZGEzLTAwAi0wMAoARgAAA1TM81CARAtAid7xmDEW2LoHACMpL-5a1SJPoSi2HilGIegAAAIBDQAAACMpL-5a1SJPoSi2HilGIegABN0wtNgAAAA=\"", "name": "id", "required": true, "sub_params": [], "type": "string"}, {"description": "Attendees in the event.", "name": "attendees", "required": false, "sub_params": [], "type": "array"}, {"description": "The event end time. At a minimum, users need to specify the time to hour. Format as \"2022-12-12 12:12:12\".", "name": "endTime", "required": false, "sub_params": [], "type": "string"}, {"description": "The event start time. At a minimum, users need to specify the time to hour. Format as \"2022-12-12 12:12:12\".", "name": "startTime", "required": false, "sub_params": [], "type": "string"}, {"description": "The event subject.", "name": "subject", "required": false, "sub_params": [], "type": "string"}, {"description": "Time zone used in the response. For example, \"Asia/Shanghai\".", "name": "timezone", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 48}}, {"name": "list_events", "id": "7391848610280898597", "description": "Get information about the schedule in Microsoft Calendar, which can be filtered by schedule title and event time.", "parameters": [{"description": "Filter by events end time. Format as \"2022-12-12 12:12:12\".", "name": "end_time", "required": false, "sub_params": [], "type": "string"}, {"description": "Time zone used in the response. For example, \"Asia/Shanghai\".", "name": "timezone", "required": true, "sub_params": [], "type": "string"}, {"description": "Filter based on keywords that appear in the subject.", "name": "keyword", "required": false, "sub_params": [], "type": "string"}, {"description": "Filter by events start tim. Format as \"2022-12-12 12:12:12\".", "name": "start_time", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.85246, "call_amount": 408}}, {"name": "create_event", "id": "7391848610280865829", "description": "Create an event in Microsoft Calendar that allows you to add participants.", "parameters": [{"description": "The type of event detail description. Support html and text, default is text.", "name": "contentType", "required": false, "sub_params": [], "type": "string"}, {"description": "The event end time. At a minimum, users need to specify the time to hour. Format as \"2022-12-12 12:12:12\".", "name": "endTime", "required": true, "sub_params": [], "type": "string"}, {"description": "The event start time. At a minimum, users need to specify the time to hour. Format as \"2022-12-12 12:12:12\".", "name": "startTime", "required": true, "sub_params": [], "type": "string"}, {"description": "The event subject.", "name": "subject", "required": true, "sub_params": [], "type": "string"}, {"description": "Time zone used in the response. For example, \"Asia/Shanghai\".", "name": "timezone", "required": true, "sub_params": [], "type": "string"}, {"description": "Attendees in the event.", "name": "attendees", "required": false, "sub_params": [], "type": "array"}, {"description": "Detailed description of the event.", "name": "content", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 907}}]}, {"name": "二维码生成器", "id": "7371733871576219648", "description": "可以将文本转化成二维码", "category": "实用工具", "is_official": true, "is_free": true, "heat": 3036, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["*", "可以将文本转化成二维码。", "工具介绍：", "*"]}, "tools": [{"name": "generate", "id": "7371735783230881826", "description": "可以将文本转化成二维码", "parameters": [{"description": "二维码背景颜色，默认为白色(#FFFFFF)", "name": "bg_color", "required": false, "sub_params": [], "type": "string"}, {"description": "二维码的内容", "name": "content", "required": true, "sub_params": [], "type": "string"}, {"description": "二维码前景色，默认为黑色（#000000）", "name": "fg_color", "required": false, "sub_params": [], "type": "string"}, {"description": "QR码纠错级别，例如：低、中、高、最高。默认为中等", "name": "level", "required": false, "sub_params": [], "type": "string"}, {"description": "二维码logo url 链接", "name": "logo_url", "required": false, "sub_params": [], "type": "string"}, {"description": "生成的图片大小，默认512", "name": "size", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.98564, "call_amount": 393651}}]}, {"name": "语音合成火山版", "id": "7434029840719839284", "description": "根据音色和文本合成音频", "category": "实用工具", "is_official": true, "is_free": true, "heat": 3012, "entity_id": "", "favorite_count": 0, "readme": {"overview": "根据音色和文本合成音频。支持的音色列表可以参考", "features": ["https://bytedance.larkoffice.com/docx/WdDOdiB1BoRyBNxlkXWcn0n3nLc"]}, "tools": [{"name": "voice_copy", "id": "7434034777440911395", "description": "提供一个音色ID和录音文件完成音色复刻", "parameters": [{"description": "音色ID", "name": "speaker_id", "required": true, "sub_params": [], "type": "string"}, {"description": "火山控制台个人app id", "name": "app_id", "required": true, "sub_params": [], "type": "string"}, {"description": "火山控制台个人app token", "name": "app_token", "required": true, "sub_params": [], "type": "string"}, {"description": "待复刻音色的录音文件", "name": "audio_file_name", "required": true, "sub_params": [], "type": "string"}, {"description": "录音文件格式，pcm、m4a必填，其余格式选填", "name": "audio_format", "required": false, "sub_params": [], "type": "string"}, {"description": "音色语言", "name": "language", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.66432, "call_amount": 3046}}, {"name": "speech_synthesis", "id": "7434034777440895011", "description": "根据音色和文本合成音频", "parameters": [{"description": "火山控制台个人app id", "name": "app_id", "required": true, "sub_params": [], "type": "string"}, {"description": "火山控制台个人app token", "name": "app_token", "required": true, "sub_params": [], "type": "string"}, {"description": "火山服务集群", "name": "cluster", "required": true, "sub_params": [], "type": "string"}, {"description": "语音语种，非必填，参考 https://bytedance.larkoffice.com/docx/WdDOdiB1BoRyBNxlkXWcn0n3nLc", "name": "language", "required": false, "sub_params": [], "type": "string"}, {"description": "音色ID，默认为爽快思思/Skye。详细音色列表参考 https://bytedance.larkoffice.com/docx/WdDOdiB1BoRyBNxlkXWcn0n3nLc, default value is 爽快思思/Skye", "name": "speaker_id", "required": true, "sub_params": [], "type": "string"}, {"description": "语速，范围是[0.2,3]，默认为1，通常保留一位小数即可, default value is 1", "name": "speed_ratio", "required": false, "sub_params": [], "type": "number"}, {"description": "要合成音频的文本内容", "name": "text", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99975, "call_amount": 193082}}]}, {"name": "灯塔", "id": "7375087417503694848", "description": "根据输入查找电影信息", "category": "便利生活", "is_official": true, "is_free": true, "heat": 2850, "entity_id": "", "favorite_count": 0, "readme": {"overview": "根据您的要求，查找电影票房信息。", "features": ["*", "使用示例：", "*", "1.找一些上映日期2023-01-01至2024-12-31的悬疑电影"]}, "tools": [{"name": "search", "id": "7375091686361530402", "description": "根据用户的输入查找电影信息", "parameters": [{"description": "获取数据条数。", "name": "num", "required": false, "sub_params": [], "type": "integer"}, {"description": "获取数据的起始位置。", "name": "offset", "required": false, "sub_params": [], "type": "integer"}, {"description": "电影名。", "name": "show_name", "required": false, "sub_params": [], "type": "string"}, {"description": "上映日期查询的起始时间，格式为\"yyyy-mm-dd\"。", "name": "start_day", "required": false, "sub_params": [], "type": "string"}, {"description": "影片类型（剧情、科幻、动作、爱情、喜剧、犯罪、惊悚、悬疑、冒险、战争、历史、动画、奇幻、家庭、古装、儿童、灾难等），不同类型之间用“,”连接。", "name": "type", "required": false, "sub_params": [], "type": "string"}, {"description": "产地（国产/进口/合拍/港澳台），不同产地之间用“,”连接。", "name": "district_type", "required": false, "sub_params": [], "type": "string"}, {"description": "上映日期查询的终止时间，格式为\"yyyy-mm-dd\"。", "name": "end_day", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99441, "call_amount": 16110}}]}, {"name": "飞书知识库", "id": "7408195262340104230", "description": "飞书知识库搜索Wiki、获取Wiki全部子节点列表", "category": "便利生活", "is_official": true, "is_free": true, "heat": 2787, "entity_id": "", "favorite_count": 0, "readme": {"overview": "飞书知识库搜索Wiki、获取Wiki全部子节点列表", "features": []}, "tools": [{"name": "search_wiki", "id": "7408204496645505075", "description": "搜索 Wiki 文档列表", "parameters": [{"description": "下一页的分页 token，首页不需要填写，根据返回的 token 获取下一页数据", "name": "page_token", "required": false, "sub_params": [], "type": "string"}, {"description": "本页返回数量的最大值，范围0-50, default value is 20", "name": "page_size", "required": false, "sub_params": [], "type": "integer"}, {"description": "不为空搜索该节点及其所有子节点，为空搜索所有 wiki（使用 node_id 过滤必须传入 space_id）", "name": "node_id", "required": false, "sub_params": [], "type": "string"}, {"description": "搜索关键词", "name": "query", "required": true, "sub_params": [], "type": "string"}, {"description": "文档所属的知识空间ID，为空搜索全部知识空间，支持输入空间url，如：https://svi136aogf123.feishu.cn/wiki/settings/7166950623940706332", "name": "space_id", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 9001}}]}, {"name": "Microsoft Outlook Email", "id": "7391830237610491944", "description": "插件帮助用户管理Outlook邮箱", "category": "便利生活", "is_official": true, "is_free": true, "heat": 2685, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件名称：Microsoft Outlook Email 管理插件", "features": ["功能描述：", "该插件提供了一系列的功能，帮助用户管理 Outlook 邮箱。用户可以使用插件的各个工具来发送草稿邮件、创建草稿、列出所有的 Outlook 类别、创建 Outlook 邮箱的目录、列出用户邮箱中的消息、发送邮件、回复消息以及修改草稿邮件。", "使用说明：", "1. 发送草稿邮件（send_draft_mail）：用户可以使用该工具来发送 Outlook 邮箱中的草稿邮件。调用该功能时，需要提供草稿邮件的标识（ID）作为输入参数。"]}, "tools": [{"name": "get_mail_list", "id": "7391837740175802420", "description": "列出用户邮箱中的消息。", "parameters": [{"description": "通过关键字搜索邮件。", "name": "keyword", "required": false, "sub_params": [], "type": "string"}, {"description": "指定消息的标签来搜索消息。只支持枚举INBOX，SEND，DRAFTS，ALL", "name": "labelIDs", "required": true, "sub_params": [], "type": "array"}, {"description": "跳过前N封电子邮件。可以与Top一起用于执行分页查询", "name": "skip", "required": false, "sub_params": [], "type": "number"}, {"description": "对于返回的前N封电子邮件，该字段的默认值是3，该字段允许的最大值是10。", "name": "top", "required": false, "sub_params": [], "type": "number"}, {"description": "获取特定时间段后的消息。日期和时间信息采用 ISO 8601 格式，并且始终采用 UTC 时间。例如，2014 年 1 月 1 日午夜 UTC 为 2014-01-01T00:00:00Z。约束，将用户描述的时间和用户所在时区转换为零时区时间。", "name": "after", "required": false, "sub_params": [], "type": "string"}, {"description": "获取某一时间段之前的消息。日期和时间信息采用ISO 8601格式，始终为UTC时间。例如，2014年1月1日的协调世界时午夜为2014-01-01T00:00:00Z。约束条件：将用户描述的时间和用户的时区转换为零时区时间。", "name": "before", "required": false, "sub_params": [], "type": "string"}, {"description": "将消息的发送者指定为某人。", "name": "from", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.97333, "call_amount": 1611}}, {"name": "send_mail", "id": "7391837740175867956", "description": "发送Outlook邮件", "parameters": [{"description": "邮件抄送人地址", "name": "cc", "required": false, "sub_params": [], "type": "array"}, {"description": "邮件内容", "name": "content", "required": true, "sub_params": [], "type": "string"}, {"description": "邮件主题", "name": "subject", "required": true, "sub_params": [], "type": "string"}, {"description": "邮件接收人地址", "name": "to", "required": true, "sub_params": [], "type": "array"}], "performance": {"success_rate": 0.96386, "call_amount": 3019}}, {"name": "reply_mail", "id": "7391837740175835188", "description": "回复用户邮箱中的一条消息", "parameters": [{"description": "回复电子邮件内容", "name": "comment", "required": true, "sub_params": [], "type": "string"}, {"description": "消息的唯一标识符。被回复的内容是从get_mail_list()接口中的id获取的。限制：不要在webLink中使用ItemID。", "name": "messageID", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 40}}, {"name": "update_draft_email", "id": "7391837740175884340", "description": "修改Outlook草稿", "parameters": [{"description": "消息的唯一标识符。你可以通过get_mail_list()接口获取消息id。从草稿列表中提取最合适的邮件，严格使用其id字段。", "name": "id", "required": true, "sub_params": [], "type": "string"}, {"description": "邮件的主题", "name": "subject", "required": false, "sub_params": [], "type": "string"}, {"description": "邮件的接收人地址", "name": "to", "required": false, "sub_params": [], "type": "array"}, {"description": "草稿邮件的抄送地址", "name": "cc", "required": false, "sub_params": [], "type": "array"}, {"description": "邮件的内容", "name": "content", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0, "call_amount": 2}}, {"name": "send_draft_mail", "id": "7391837740175851572", "description": "发送草稿邮件", "parameters": [{"description": "消息的唯一标识符。你可以通过`get_mail_list()`接口获取消息id。从草稿列表中提取最合适的邮件，严格使用其`id`字段。", "name": "id", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 23}}, {"name": "create_draft", "id": "7391837740175769652", "description": "创建一个邮箱草稿", "parameters": [{"description": "邮件抄送地址列表，不要编造地址", "name": "cc", "required": false, "sub_params": [], "type": "array"}, {"description": "邮件内容", "name": "content", "required": true, "sub_params": [], "type": "string"}, {"description": "邮件主题", "name": "subject", "required": true, "sub_params": [], "type": "string"}, {"description": "邮件的接受人列表，不要编造地址", "name": "to", "required": true, "sub_params": [], "type": "array"}], "performance": {"success_rate": 1, "call_amount": 70}}, {"name": "list_category", "id": "7391837740175818804", "description": "列出所有的Outlook类别", "parameters": [], "performance": {"success_rate": 0, "call_amount": 33}}, {"name": "create_category", "id": "7391837740175753268", "description": "创建Outlook邮箱的目录", "parameters": [{"description": "Outlook邮箱目录的名称", "name": "displayName", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 1, "call_amount": 12}}]}, {"name": "HTTP请求器", "id": "7413209912572035108", "description": "发送HTTP请求", "category": "实用工具", "is_official": true, "is_free": true, "heat": 2561, "entity_id": "", "favorite_count": 0, "readme": {"overview": "HTTP请求器插件是一款解决接口调用需求的插件，该插件支持用户通过配置HTTP参数实现对外部API的调用与响应结果处理，适用于开发和集成场景。", "features": []}, "tools": [{"name": "postman", "id": "7413213283358113811", "description": "使用header、params、method、url、body发送HTTP请求。", "parameters": [{"description": "请求方法", "name": "method", "required": true, "sub_params": [], "type": "string"}, {"description": "请求参数，json字符串，示例：{\"param1\":\"c\", \"param2\": \"xxx\"}", "name": "params", "required": false, "sub_params": [], "type": "string"}, {"description": "请求链接", "name": "url", "required": true, "sub_params": [], "type": "string"}, {"description": "请求内容，json字符串，示例：{\"body1\": \"xxxxxx\"}", "name": "body", "required": false, "sub_params": [], "type": "string"}, {"description": "请求头，json字符串，示例：{\"header1\": \"xxxxxx\"}", "name": "header", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99307, "call_amount": 11330423}}, {"name": "curl", "id": "7413213283358097427", "description": "使用curl发送HTTP请求。", "parameters": [{"description": "CURL", "name": "curl_string", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99932, "call_amount": 3064135}}]}, {"name": "风格滤镜", "id": "7438921896516763698", "description": "为你的照片创建风格化的滤镜，支持毛毡、粘土、积木、美漫、玉石、搞笑涂鸦、工笔、水墨、3D僵尸风格", "category": "图像", "is_official": true, "is_free": false, "heat": 2488, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["风格滤镜是一款强大的工具，能够为你的照片添加各种独特的风格滤镜效果。它支持多种风格类型，包括毛毡、粘土、积木、美漫、玉石、搞笑涂鸦、工笔、水墨和 3D 僵尸风格等。通过应用这些风格滤镜，你可以将普通照片转换为具有艺术感或创意风格的作品，为你的照片增添独特的魅力。", "工具介绍：", "*", "工具名称：style_transfer"]}, "tools": [{"name": "style_transfer", "id": "7438923452805070857", "description": "为你的照片创建风格化的滤镜，支持毛毡、粘土、积木、美漫、玉石、搞笑涂鸦、工笔、水墨、3D僵尸风格", "parameters": [{"description": "上传图片", "name": "image_url", "required": true, "sub_params": [], "type": "string"}, {"description": "风格滤镜, enum list is [\"0\",\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\"], default value is 0", "name": "style", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.9478, "call_amount": 105106}}]}, {"name": "智能扩图", "id": "7438917721858916367", "description": "为图像扩充相应范围的内容", "category": "图像", "is_official": true, "is_free": false, "heat": 2416, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["智能扩图这款插件的主要功能是为图像扩充相应范围的内容。通过该插件，用户可以根据给定的原图链接以及自定义的扩展比例等参数，对图像进行灵活的扩展，从而得到一个更大尺寸且内容更丰富的图像，满足用户在图像编辑、创作等方面的多样化需求。", "工具介绍：", "*", "工具名称：intelligentImageExpansion"]}, "tools": [{"name": "intelligentImageExpansion", "id": "7438919529398435875", "description": "为图像扩充相应范围。", "parameters": [{"description": "向右扩展比例，范围为(0,1]", "name": "right", "required": false, "sub_params": [], "type": "number"}, {"description": "向上扩展比例，范围为(0,1]", "name": "top", "required": false, "sub_params": [], "type": "number"}, {"description": "原图链接", "name": "url", "required": true, "sub_params": [], "type": "string"}, {"description": "向下扩展比例，范围为(0,1]", "name": "bottom", "required": false, "sub_params": [], "type": "number"}, {"description": "生成内容的提示词", "name": "custom_prompt", "required": false, "sub_params": [], "type": "string"}, {"description": "向左扩展比例，范围为(0,1]", "name": "left", "required": false, "sub_params": [], "type": "number"}], "performance": {"success_rate": 0.97134, "call_amount": 350404}}]}, {"name": "添加文字", "id": "7439197585790844978", "description": "上传底图，在底图上添加图片", "category": "图像", "is_official": true, "is_free": false, "heat": 2335, "entity_id": "", "favorite_count": 0, "readme": {"overview": "这是一款强大的插件，它可以帮助用户在上传的底图上添加文字。具体功能包括：", "features": ["*", "添加文字", "：用户可以在底图上添加各种文字内容。", "*"]}, "tools": [{"name": "add_text_to_image", "id": "7439198651441741862", "description": "将图片增加水印", "parameters": [{"description": "文字的透明度。取值范围：[0,100] 默认值：100， 表示透明度 100%（不透明）", "name": "transparency", "required": false, "sub_params": [], "type": "string"}, {"description": "文字的中线垂直偏移。当文字位置在左中、中部、右中时，可以指定文字位置根据中线往上或者往下偏移。取值范围：[-1000,1000] 默认值：0 单位：px", "name": "voffset", "required": false, "sub_params": [], "type": "string"}, {"description": "文字的颜色。十六进制颜色码，取值为 000000 到 FFFFFF，默认值为000000（黑色）。", "name": "color", "required": false, "sub_params": [], "type": "string"}, {"description": "是否将文字平铺满原图。1：表示将文字水印铺满原图。0（默认值）：表示不将文字水印铺满全图。", "name": "fill", "required": false, "sub_params": [], "type": "string"}, {"description": "文字的初始位置。nw:左上, north:中上, ne:右上, west:左中, center:中部, east:右中, sw:左下, south:中下, se(默认值):右下", "name": "location", "required": false, "sub_params": [], "type": "string"}, {"description": "文字按顺时针方向旋转的角度。[0,360] 默认值：0，表示不旋转。", "name": "rotate", "required": false, "sub_params": [], "type": "string"}, {"description": "文字内容，中文字符串的最大字节长度为 64 个字符。", "name": "text", "required": true, "sub_params": [], "type": "string"}, {"description": "文字的垂直边距，即距离图片边缘的垂直距离， 这个参数只有当文字位置是左上、中上、右上、左下、中下、右下才有意义。取值范围：[0,4096]默认值：10 单位：像素（px）", "name": "vertical_margin", "required": false, "sub_params": [], "type": "string"}, {"description": "文字的水平边距， 即距离图片边缘的水平距离。这个参数只有当文字位置是左上、左中、左下、右上、右中、右下才有意义。取值范围：[0,4096]默认值：10 单位：像素（px）", "name": "horizontal_margin", "required": false, "sub_params": [], "type": "string"}, {"description": "原始图片链接", "name": "origin_url", "required": true, "sub_params": [], "type": "string"}, {"description": "文字的阴影透明度。[0,100] 默认值：0，表示没有阴影。", "name": "shadow", "required": false, "sub_params": [], "type": "string"}, {"description": "文字的文字大小,(0,1000],默认值：40,单位：px", "name": "size", "required": false, "sub_params": [], "type": "string"}, {"description": "文字字体。可选字体为文泉驿正黑（默认）、文泉微米黑、方正书宋、方正楷体、方正黑体、方正仿宋", "name": "type", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.97176, "call_amount": 121310}}]}, {"name": "美颜", "id": "7439160626078859303", "description": "一键智能美颜", "category": "图像", "is_official": true, "is_free": false, "heat": 2143, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["该插件为“美颜”，旨在提供一键智能美颜的功能。用户只需输入待美颜的图片，即可快速获得经过美化处理后的图片，让照片更加美观动人。", "工具介绍：", "*", "工具名称：FacePretty"]}, "tools": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "id": "7439161814753165353", "description": "美颜", "parameters": [{"description": "美颜程度，范围[0,1], default value is 1", "name": "beauty_level", "required": false, "sub_params": [], "type": "number"}, {"description": "待美颜的图片", "name": "image_url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.9438, "call_amount": 55202}}]}, {"name": "ByteArtist Pets", "id": "7359145197827145738", "description": "ByteArtist下的宠物类别的图像处理插件。上传宠物照片，来为你的宠物换装吧。", "category": "图像", "is_official": true, "is_free": false, "heat": 2121, "entity_id": "", "favorite_count": 0, "readme": {"overview": "ByteArtist下的宠物类别的图像处理插件。上传宠物照片，来为你的宠物换装吧。", "features": ["*", "使用示例：", "*", "1.我要和我们的猫猫过新年，"]}, "tools": [{"name": "spring_pets_image", "id": "7359149802313138214", "description": "为用户提供的宠物图片进行春天主题的风格化处理。", "parameters": [{"description": "随机种子，需要输入正整数。其中-1代表随机生成，固定其他任意正整数，则生成相似的图片。默认值为-1。", "name": "seed", "required": false, "sub_params": [], "type": "integer"}, {"description": "风格化强度。0:低；1:中；2:高。默认值为1。", "name": "strength", "required": false, "sub_params": [], "type": "integer"}, {"description": "用户上传的图片链接。", "name": "image_url", "required": true, "sub_params": [], "type": "string"}, {"description": "期望生成的图片的风格，是一个数字，按照如下规则传入：1=春游记；2=花房；3=复活节彩蛋；4=打工人。", "name": "model", "required": true, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.57658, "call_amount": 13615}}]}, {"name": "喜马拉雅FM", "id": "7399961811942113280", "description": "根据关键词搜索喜马拉雅FM音频，包括小说故事、相声评书、京剧戏曲、新闻广播等", "category": "游戏与娱乐", "is_official": true, "is_free": true, "heat": 1812, "entity_id": "", "favorite_count": 0, "readme": {"overview": "根据关键词搜索喜马拉雅FM音频，包括小说故事、相声评书、京剧戏曲、新闻广播等", "features": []}, "tools": [{"name": "search_tracks", "id": "7399965386126475264", "description": "根据关键词搜索单个音频，包括小说故事、相声评书、京剧戏曲、新闻广播等", "parameters": [{"description": "用户想要返回多少条数据, default value is 10", "name": "count", "required": false, "sub_params": [], "type": "integer"}, {"description": "搜索关键词", "name": "keyword", "required": true, "sub_params": [], "type": "string"}, {"description": "用户想要跳过的搜索数量，比如想从第六个搜索，offset = 5, default value is 0", "name": "offset", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 1, "call_amount": 9094}}, {"name": "search_albums", "id": "7399965386126458880", "description": "根据关键词搜索喜马拉雅音频专辑", "parameters": [{"description": "搜索关键词", "name": "keyword", "required": true, "sub_params": [], "type": "string"}, {"description": "用户想要跳过的搜索数量，比如想从第六个搜索，offset = 5", "name": "offset", "required": true, "sub_params": [], "type": "integer"}, {"description": "用户想要返回多少条数据", "name": "count", "required": true, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 1, "call_amount": 8091}}]}, {"name": "调整", "id": "7438921446090637312", "description": "调整图片的亮度、对比度、饱和度", "category": "图像", "is_official": true, "is_free": false, "heat": 1643, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["这款名为“调整”的插件，主要用于对图片的亮度、对比度和饱和度进行调整。通过该插件，用户可以轻松地改变图片的视觉效果，以满足不同的需求和创作意图。无论是想要让图片更加明亮鲜艳，还是增加对比度突出细节，或者调整饱和度营造特定氛围，都能通过该插件实现。", "工具介绍：", "*", "工具名称：change"]}, "tools": [{"name": "change", "id": "7438923315269500943", "description": "调整图片的亮度、对比度、饱和度，默认值为1时画面不调整", "parameters": [{"description": "亮度, default value is 1", "name": "brightness", "required": false, "sub_params": [], "type": "number"}, {"description": "对比度, default value is 1", "name": "contrast", "required": false, "sub_params": [], "type": "number"}, {"description": "上传图片", "name": "image_url", "required": true, "sub_params": [], "type": "string"}, {"description": "饱和度, default value is 1", "name": "saturation", "required": false, "sub_params": [], "type": "number"}], "performance": {"success_rate": 0.98477, "call_amount": 295701}}]}, {"name": "图片格式转换", "id": "7367214572647890956", "description": "一个图片格式转换工具。", "category": "实用工具", "is_official": true, "is_free": true, "heat": 1546, "entity_id": "", "favorite_count": 0, "readme": {"overview": "一个图片格式转换工具，可以进行图片格式转换。", "features": ["*", "使用示例：", "*", "1.把图片"]}, "tools": [{"name": "svgConverter", "id": "7367216661943975990", "description": "一个图片格式转换工具，可以把jpg格式、png格式的图片转换为svg格式。", "parameters": [{"description": "图片链接", "name": "image_url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.91843, "call_amount": 21359}}]}, {"name": "光影融合", "id": "7438920651794284559", "description": "让画面融合光影图的光影", "category": "图像", "is_official": true, "is_free": false, "heat": 1492, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["光影融合插件能够让画面融合光影图的光影，通过巧妙地结合主体图和光影图，为图像增添丰富的层次感和真实感。无论是想要营造出浪漫的日落氛围，还是突出物体的立体感，该插件都能满足你的需求。", "工具介绍：", "*", "工具名称：light"]}, "tools": [{"name": "light", "id": "7438922908631597108", "description": "让画面融合光影图的光影", "parameters": [{"description": "亮度，默认为10, default value is 10", "name": "brightness", "required": false, "sub_params": [], "type": "number"}, {"description": "对比度，默认为10, default value is 10", "name": "contrast", "required": false, "sub_params": [], "type": "number"}, {"description": "逆光时的补光强度，范围(0,-100)，越小暗面越亮，默认为-50, default value is -50", "name": "fill_light_strength", "required": false, "sub_params": [], "type": "number"}, {"description": "光影图", "name": "light_image", "required": true, "sub_params": [], "type": "string"}, {"description": "主体图", "name": "main_image", "required": true, "sub_params": [], "type": "string"}, {"description": "是否背光，顺光为1，逆光为2，默认为1, enum list is [\"1\",\"2\"], default value is 1", "name": "mode", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99254, "call_amount": 20728}}]}, {"name": "博查搜索", "id": "7496002227321143350", "description": "从全网搜索任何网页信息和网页链接，结果准确、摘要完整，更适合AI使用。", "category": "网页搜索", "is_official": true, "is_free": false, "heat": 1200, "entity_id": "", "favorite_count": 0, "readme": {"overview": "博查 AI 搜索，从全网搜索任何网页信息和网页链接，结果准确、摘要完整，更适合AI使用。", "features": []}, "tools": [{"name": "web_search", "id": "7496002598705872937", "description": "从全网搜索任何网页信息和网页链接，结果准确、摘要完整，更适合AI使用。\n\n搜索结果:\n包括网页、图片、视频，Response格式兼容Bing Search API。\n- 网页包括name、url、snippet、summary、siteName、siteIcon等信息\n[图片]\n- 图片包括 contentUrl、hostPageUrl、width、height等信息", "parameters": [{"description": "返回结果的条数（实际返回结果数量可能会小于count指定的数量）。 - 可填范围：1-50，最大单次搜索返回50条 - 默认为100, default value is 10", "name": "count", "required": false, "sub_params": [], "type": "integer"}, {"description": "搜索指定时间范围内的网页。 可填值： - oneDay，一天内 - oneWeek，一周内 - oneMonth，一个月内 - oneYear，一年内 - noLimit，不限（默认） - YYYY-MM-DD..YYYY-MM-DD，搜索日期范围，例如：\"2025-01-01..2025-04-06\" - YYYY-MM-DD，搜索指定日期，例如：\"2025-04-06\" 推荐使用“noLimit”。搜索算法会自动进行时间范围的改写，效果更佳。", "name": "freshness", "required": false, "sub_params": [], "type": "string"}, {"description": "页码，默认值为 1, default value is 1", "name": "page", "required": false, "sub_params": [], "type": "integer"}, {"description": "用户的搜索词", "name": "query", "required": true, "sub_params": [], "type": "string"}, {"description": "是否显示文本摘要。 可填值： - true，显示 - false，不显示（默认）, default value is false", "name": "summary", "required": false, "sub_params": [], "type": "boolean"}], "performance": {"success_rate": 0.99345, "call_amount": 3830873}}]}, {"name": "裁剪", "id": "7438917857070743588", "description": "自定义裁剪", "category": "图像", "is_official": true, "is_free": false, "heat": 1189, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["该插件名为“裁剪”，具有强大的自定义裁剪图片功能。通过此插件，用户可以根据自己的需求对图片进行灵活裁剪，获取特定区域的图像内容。无论是需要突出图片中的某个主体、调整图片尺寸，还是进行一些创意裁剪，都能通过该插件轻松实现。", "工具介绍：", "*", "工具名称：cut_image"]}, "tools": [{"name": "cut_image", "id": "7438919975403896844", "description": "裁剪图片", "parameters": [{"description": "[0,图片宽度] 默认为最大值。", "name": "width", "required": false, "sub_params": [], "type": "string"}, {"description": "裁剪起始点横坐标偏移（相对于指定的坐标原点）。横坐标向右偏移为正，向左偏移为负。", "name": "x", "required": false, "sub_params": [], "type": "string"}, {"description": "裁剪起始点纵坐标偏移（相对于指定的坐标原点）。 纵坐标向下偏移为正，向上偏移为负。", "name": "y", "required": false, "sub_params": [], "type": "string"}, {"description": "[0,图片高度] 默认为最大值。", "name": "height", "required": false, "sub_params": [], "type": "string"}, {"description": "裁剪起始点，nw（默认值）：左上,north：中上,ne：右上,west：左中,center：中部,east：右中,sw：左下,south：中下,se：右下", "name": "location", "required": false, "sub_params": [], "type": "string"}, {"description": "原始图片链接", "name": "origin_url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.96765, "call_amount": 226197}}]}, {"name": "叠图", "id": "7438921536473612288", "description": "为图片添加图片", "category": "图像", "is_official": true, "is_free": false, "heat": 906, "entity_id": "", "favorite_count": 0, "readme": {"overview": "为图片添加图片。", "features": []}, "tools": [{"name": "add_image_to_image", "id": "7438922391696343080", "description": "叠图", "parameters": [{"description": "底部图的图片链接", "name": "origin_url", "required": true, "sub_params": [], "type": "string"}, {"description": "取值范围：[0,100] 默认值：100， 表示透明度 100%（不透明）", "name": "transparency", "required": false, "sub_params": [], "type": "string"}, {"description": "上层图离底部图片边缘的垂直距离。仅当上层图位置是是左上、中上、右上、左下、中下、右下才生效。取值范围：[0,4096]，默认值:10，单位:像素(px)", "name": "vertical_margin", "required": false, "sub_params": [], "type": "string"}, {"description": "上层图的中线垂直偏移。仅当上层图位置是左中、中部、右中时，可以指定上层图根据中线往上或者往下偏移。取值范围：[-1000,1000]，默认值:10，单位:像素(px)", "name": "voffset", "required": false, "sub_params": [], "type": "string"}, {"description": "对上层图进行百分比缩放的数值", "name": "zoom", "required": true, "sub_params": [], "type": "string"}, {"description": "上层图距离底部图片边缘的水平距离。仅当上层图位置是左上、左中、左下、右上、右中、右下才生效。取值范围：[0,4096]，默认值:10，单位:像素(px)", "name": "horizontal_margin", "required": false, "sub_params": [], "type": "string"}, {"description": "上层图的图片链接", "name": "image_url", "required": true, "sub_params": [], "type": "string"}, {"description": "上层图叠放的空间位置。nw：左上,north：中上,ne：右上,west：左中,center：中部,east：右中,sw：左下,south：中下,se（默认值）：右下", "name": "location", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.95805, "call_amount": 135391}}]}, {"name": "缩放", "id": "7438917029320491008", "description": "按设定的长边/短边的尺寸缩放图片", "category": "图像", "is_official": true, "is_free": false, "heat": 900, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["该插件名为“缩放”，主要功能是按设定的长边或短边的尺寸来缩放图片。用户可以通过调整参数，灵活地控制图片缩放后的大小，实现对图片尺寸的定制化处理。", "工具介绍：", "*", "工具名称：resize"]}, "tools": [{"name": "resize", "id": "7438918747382906895", "description": "缩放图片", "parameters": [{"description": "缩放模式，按长边缩放为1，按短边缩放为2，默认为1", "name": "mode", "required": false, "sub_params": [], "type": "string"}, {"description": "原图", "name": "image_url", "required": true, "sub_params": [], "type": "string"}, {"description": "图片缩放的最大尺寸，支持按长边/短边缩放，单位（像素）", "name": "max_length", "required": true, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.97417, "call_amount": 208351}}]}, {"name": "视频工具", "id": "7491321428051132479", "description": "视频工具", "category": "实用工具", "is_official": true, "is_free": true, "heat": 880, "entity_id": "", "favorite_count": 0, "readme": {"overview": "视频抽帧工具，支持上传视频url后进行视频抽帧切片以及导出音频等能力。功能列表如下", "features": ["*", "支持基于固定间隔时间进行抽帧（上限100张，超过则不再输出）", "*", "支持基于固定帧数进行抽帧"]}, "tools": [{"name": "frame_extractor_by_key_frame", "id": "7491322158728085540", "description": "抽取关键帧", "parameters": [{"description": "视频 URL", "name": "url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99183, "call_amount": 2432}}, {"name": "frame_extractor_by_interval", "id": "7491322136238538788", "description": "等时抽帧，从 start_time_ms 开始，每隔 interval_ms 时间抽帧一次。", "parameters": [{"description": "间隔抽帧时间, 单位: ms", "name": "interval_ms", "required": true, "sub_params": [], "type": "number"}, {"description": "视频 URL", "name": "url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99331, "call_amount": 988}}, {"name": "frame_extractor_by_count", "id": "7491322136238522404", "description": "定数抽帧，根据视频时长/抽取帧数计算间隔动态抽帧。", "parameters": [{"description": "视频 URL", "name": "url", "required": true, "sub_params": [], "type": "string"}, {"description": "抽取多少个帧", "name": "count", "required": true, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.99702, "call_amount": 10165}}, {"name": "audio_extractor", "id": "7493396054575104011", "description": "从视频提取音频", "parameters": [{"description": "音频类型, 默认 mp3, 支持 mp3, wav, aac, default value is mp3", "name": "audio_type", "required": false, "sub_params": [], "type": "string"}, {"description": "视频 url", "name": "url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99834, "call_amount": 197296}}]}, {"name": "悠船", "id": "7496179617884028968", "description": "悠船生图模型", "category": "图像", "is_official": true, "is_free": false, "heat": 728, "entity_id": "", "favorite_count": 0, "readme": {"overview": "悠船", "features": ["是一款由Midjourney官方推出的国内中文版AI图像生成工具", "，它允许用户通过文字描述来生成图像。"]}, "tools": [{"name": "variation", "id": "7496182091709972495", "description": "变化：该工具可以根据自然语言对图片进行变化操作，请注意该工具需要文生图返回的任务id\n", "parameters": [{"description": "图片编号(0/1/2/3)", "name": "image_no", "required": true, "sub_params": [], "type": "integer"}, {"description": "任务ID", "name": "job_id", "required": true, "sub_params": [], "type": "string"}, {"description": "新提示词, 长度[1-8192]", "name": "remix_prompt", "required": false, "sub_params": [], "type": "string"}, {"description": "0: 轻微变换  1: 强烈变换", "name": "type", "required": true, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.9726, "call_amount": 104}}, {"name": "upscale", "id": "7496182091709956111", "description": "高清：该工具可以根据自然语言对图片进行高清优化，请注意该工具需要文生图返回的任务id\n", "parameters": [{"description": "图片编号(0/1/2/3)", "name": "image_no", "required": true, "sub_params": [], "type": "integer"}, {"description": "任务ID", "name": "job_id", "required": true, "sub_params": [], "type": "string"}, {"description": "0: v6/niji6/v6.1 subtle高清  1: v6/niji6/v6.1 creative高清", "name": "type", "required": true, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 1, "call_amount": 83}}, {"name": "text2image", "id": "7496182091709939727", "description": "文生图工具：该工具可以根据自然语言描述生成4张图片", "parameters": [{"description": "文本信息", "name": "text", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.9947, "call_amount": 3410}}, {"name": "pan", "id": "7496182091709923343", "description": "单方向延展：该工具可以根据自然语言对图片进行单方向延展，请注意该工具需要文生图返回的任务id\n", "parameters": [{"description": "延展区域提示词, 长度[1-8192]", "name": "remix_prompt", "required": true, "sub_params": [], "type": "string"}, {"description": "延展目标比例,即在延展方向上 视图中新图所占区域相较原图区域的倍数（非像素倍数，是占比比例）, 取值范围[1.1-3.0]；例如 一个 1:1的图片，向右扩13%，则scale=1.13", "name": "scale", "required": true, "sub_params": [], "type": "number"}, {"description": "方向, 0: 下 1: 右 2: 上 3: 左", "name": "direction", "required": true, "sub_params": [], "type": "integer"}, {"description": "图片编号(0/1/2/3)", "name": "image_no", "required": true, "sub_params": [], "type": "integer"}, {"description": "任务ID", "name": "job_id", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.96296, "call_amount": 30}}, {"name": "out_paint", "id": "7496182091709906959", "description": "扩图：该工具可以根据自然语言对图片进行扩展，请注意该工具需要文生图返回的任务id", "parameters": [{"description": "图片编号(0/1/2/3)", "name": "image_no", "required": true, "sub_params": [], "type": "integer"}, {"description": "任务ID", "name": "job_id", "required": true, "sub_params": [], "type": "string"}, {"description": "新提示词, 长度[1-8192]", "name": "remix_prompt", "required": false, "sub_params": [], "type": "string"}, {"description": "扩图目标比例, 即在视图中新图所占区域相较原图区域的倍数，取值范围[1.1-2.0]；例如 一个1:1图片扩图后，外扩20%(scale=1.2)", "name": "scale", "required": true, "sub_params": [], "type": "number"}], "performance": {"success_rate": 1, "call_amount": 395}}]}, {"name": "宠物风格化", "id": "7438922705132421120", "description": "宠物风格化", "category": "实用工具", "is_official": true, "is_free": true, "heat": 712, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["本插件“宠物风格化”旨在为用户提供一种将普通宠物图片进行风格化处理的能力。通过该插件，用户可以轻松地将宠物图片转换为具有独特风格的艺术作品，为宠物照片增添创意和趣味性。", "工具介绍：", "*", "工具名称：spring_pets_image"]}, "tools": [{"name": "spring_pets_image", "id": "7438923179067867151", "description": "宠物风格化", "parameters": [{"description": "上传图片", "name": "image_url", "required": true, "sub_params": [], "type": "string"}, {"description": "期望生成的图片的风格, enum list is [1,2,3,4], default value is 1", "name": "model", "required": false, "sub_params": [], "type": "integer"}, {"description": "风格化强度, enum list is [0,1,2], default value is 1", "name": "strength", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.86167, "call_amount": 4968}}]}, {"name": "火山云搜索服务", "id": "7495590356151582772", "description": "火山云搜索服务插件基于火山引擎的云搜索服务ES和OpenSearch引擎，兼容开源的ES和OpenSearch语法，提供文本加向量的混合搜索功能，实现精准查询和语义查询的完美结合，并且可以实现超大规模的向量存储（适配十万到五百亿之间的规模），能让 Coze 平台的聊天机器人直接调用云搜索能力。\n准备工作\n在使用插件前，用户需完成以下步骤：\n1. 开通火山引擎云搜索服务\n  - 注册火山引擎账号并完成实名认证。\n  - 在火山引擎控制台开通云搜索服务https://www.volcengine.com/product/es。\n2. 创建引擎\n  - 创建ES或者OpenSearch实例。\n  - 导入数据，在引擎页面调试召回效果，确保配置正确。\n配置与使用\n1. 配置参数\n  - 输入连接地址、用户名、索引等必要参数。\n2. 测试与应用\n  - 在对话中输入查询，Bot 将自动检索知识库切片并生成准确回答。", "category": "实用工具", "is_official": true, "is_free": true, "heat": 652, "entity_id": "", "favorite_count": 0, "readme": {"overview": "火山云搜索服务插件基于火山引擎的云搜索服务ES和OpenSearch引擎，兼容开源的ES和OpenSearch语法，提供文本加向量的混合搜索功能，实现精准查询和语义查询的完美结合，并且可以实现超大规模的向量存储（适配十万到五百亿之间的规模），能让 Coze 平台的聊天机器人直接调用云搜索能力。", "features": ["*", "准备工作", "在使用插件前，用户需完成以下步骤：", "*"]}, "tools": [{"name": "search_template", "id": "7495590551685922856", "description": "通过调用 OpenSearch 的 search_template 接口，支持预存或动态模板的搜索请求，返回标准化的文档结果列表。", "parameters": [{"description": "认证密", "name": "secret", "required": true, "sub_params": [], "type": "string"}, {"description": "预存搜索模板的 ID", "name": "template_id", "required": false, "sub_params": [], "type": "string"}, {"description": "分页起始偏移量（对应模板中的 {{from}}。若提供则必须≥0", "name": "template_param_from", "required": false, "sub_params": [], "type": "integer"}, {"description": "认证用户名", "name": "username", "required": true, "sub_params": [], "type": "string"}, {"description": "直接传入的搜索模板内容（JSON 格式）", "name": "template_source", "required": false, "sub_params": [], "type": "string"}, {"description": "OpenSearch 公网访问地址。需为完整 URL（如 https://host:port）", "name": "endpoint", "required": true, "sub_params": [], "type": "string"}, {"description": "索引名称", "name": "index", "required": true, "sub_params": [], "type": "string"}, {"description": "query_params", "name": "params", "required": false, "sub_params": [], "type": "string"}, {"description": "搜索关键词（对应模板中的 {{keyword}}），长度 <8000 字符", "name": "template_param_keyword", "required": false, "sub_params": [], "type": "string"}, {"description": "每页结果数量（对应模板中的 {{size}}，1 ≤ value ≤ 200", "name": "template_param_size", "required": false, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0, "call_amount": 296}}]}, {"name": "旋转", "id": "7438921878921560102", "description": "旋转图片", "category": "图像", "is_official": true, "is_free": false, "heat": 586, "entity_id": "", "favorite_count": 0, "readme": {"overview": "插件功能：", "features": ["该插件名为“旋转”，主要用于对图片进行旋转操作。它能够根据用户指定的旋转角度，对输入的原始图片进行顺时针方向的旋转处理，让用户可以轻松调整图片的方向，适用于多种场景。", "工具介绍：", "*", "工具名称：image_rotate"]}, "tools": [{"name": "image_rotate", "id": "7438922485959262218", "description": "图片旋转", "parameters": [{"description": "原始图片链接", "name": "origin_url", "required": true, "sub_params": [], "type": "string"}, {"description": "范围：[0,360] 图片将会按顺时针方向进行旋转。", "name": "rotate", "required": false, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.99037, "call_amount": 34884}}]}, {"name": "图片压缩", "id": "7461926057290153993", "description": "上传图片链接，返回压缩后的base64", "category": "实用工具", "is_official": true, "is_free": true, "heat": 474, "entity_id": "", "favorite_count": 0, "readme": {"overview": "'''", "features": ["插件功能：", "上传图片链接，对图片进行压缩并返回压缩后的 base64 数据。", "工具介绍：", "*"]}, "tools": [{"name": "img2base64", "id": "7461927492098523187", "description": "压缩图片后返回base64", "parameters": [{"description": "压缩后图片宽度", "name": "output_width", "required": true, "sub_params": [], "type": "integer"}, {"description": "压缩质量。范围1-100，数字越大，压缩后图片质量越高", "name": "quality", "required": true, "sub_params": [], "type": "integer"}, {"description": "图片链接", "name": "url", "required": true, "sub_params": [], "type": "string"}, {"description": "压缩后图片高度", "name": "output_height", "required": true, "sub_params": [], "type": "integer"}], "performance": {"success_rate": 0.28375, "call_amount": 13005}}]}, {"name": "大模型语音识别", "id": "7506777203359629350", "description": "相比传统模型有更高的准确率；对多语种多方言/口音/噪声和背景人声更准确；能根据上下文、用户输入、背景信息输入有更多理解", "category": "实用工具", "is_official": true, "is_free": true, "heat": 17, "entity_id": "", "favorite_count": 0, "readme": {"overview": "大模型语音识别", "features": []}, "tools": [{"name": "asr_llm", "id": "7506817938062835739", "description": "相比传统模型有更高的准确率；对多语种多方言/口音/噪声和背景人声更准确；能根据上下文、用户输入、背景信息输入有更多理解", "parameters": [{"description": "音频文件地址，插件会尝试识别文件格式，对于无后缀的 URL 可能识别不到。文件类型支持 raw / wav / mp3 / ogg", "name": "audio_url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.93564, "call_amount": 428}}]}, {"name": "语音识别", "id": "7506776373675966516", "description": "准确地将语音内容转写成文字。适用于录音质检、会议总结、音频内容分析、课堂内容分析等场景。", "category": "实用工具", "is_official": true, "is_free": true, "heat": 11, "entity_id": "", "favorite_count": 0, "readme": {"overview": "语音识别", "features": []}, "tools": [{"name": "asr_lite", "id": "7506818401994752000", "description": "准确地将语音内容转写成文字。适用于录音质检、会议总结、音频内容分析、课堂内容分析等场景。", "parameters": [{"description": "音频语言，当前支持：普通话，粤语，四川话，上海话，英文，日语，法语，韩语，西班牙语，葡萄牙语，印尼语", "name": "language", "required": true, "sub_params": [], "type": "string"}, {"description": "音频文件地址，插件会尝试识别文件格式，对于无后缀的 URL 可能识别不到。文件类型支持 raw / wav / mp3 / ogg ", "name": "audio_url", "required": true, "sub_params": [], "type": "string"}], "performance": {"success_rate": 0.94231, "call_amount": 232}}]}]