# Coze 工作流 API 和配置参考

## 🔗 API 集成参考

### 核心 API 文档

#### 工作流执行 API
- **官方文档**: [通过 API 运行应用工作流](https://www.coze.cn/open/docs/guides/run_app_as_api)
- **功能**: 通过 REST API 调用和执行工作流
- **适用场景**: 外部系统集成、自动化触发、批量处理

#### 开发者 API 总览
- **官方文档**: [API 和 SDK](https://www.coze.cn/open/docs/developer_guides)
- **功能**: 完整的开发者 API 文档
- **包含内容**: 认证、调用方式、参数说明、响应格式

## ⚙️ 节点配置参考

### 基础节点配置

#### 1. 大模型节点 (LLM Node)
**官方文档**: [大模型节点](https://www.coze.cn/open/docs/guides/llm_node)

**核心配置参数**:
```json
{
  "modleName": "模型名称",
  "temperature": 0.8,
  "maxTokens": 16384,
  "responseFormat": 2,
  "systemPrompt": "系统提示词",
  "prompt": "用户提示词模板",
  "enableChatHistory": false,
  "chatHistoryRound": 3
}
```

**参数说明**:
- `temperature`: 创造性控制 (0.0-2.0)
- `maxTokens`: 最大输出长度
- `responseFormat`: 响应格式类型
- `enableChatHistory`: 是否启用对话历史

#### 2. 插件节点 (Plugin Node)
**官方文档**: [插件节点](https://www.coze.cn/open/docs/guides/plugin_node)

**配置结构**:
```json
{
  "apiParam": [
    {
      "name": "apiID",
      "input": {"value": {"content": "插件API ID"}}
    },
    {
      "name": "pluginID", 
      "input": {"value": {"content": "插件ID"}}
    }
  ],
  "inputParameters": [
    {
      "name": "参数名",
      "input": {
        "type": "string",
        "value": {"type": "ref", "content": {...}}
      }
    }
  ]
}
```

#### 3. 代码节点 (Code Node)
**官方文档**: [代码节点](https://www.coze.cn/open/docs/guides/code_node)

**支持语言**: Python, JavaScript
**配置示例**:
```json
{
  "language": "python",
  "code": "# Python代码\ndef main(input_data):\n    return {'result': input_data}",
  "inputParameters": [...],
  "outputParameters": [...]
}
```

#### 4. HTTP 请求节点 (HTTP Node)
**官方文档**: [HTTP 请求节点](https://www.coze.cn/open/docs/guides/http_node)

**配置参数**:
```json
{
  "method": "POST",
  "url": "https://api.example.com/endpoint",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer {{token}}"
  },
  "body": {
    "data": "{{input}}"
  },
  "timeout": 30000
}
```

### 控制流节点配置

#### 1. 选择器节点 (Condition Node)
**官方文档**: [选择器节点](https://www.coze.cn/open/docs/guides/condition_node)

**条件配置**:
```json
{
  "conditions": [
    {
      "expression": "{{variable}} > 10",
      "operator": "greater_than",
      "value": 10,
      "targetNode": "node_id_1"
    },
    {
      "expression": "{{variable}} <= 10",
      "operator": "less_equal",
      "value": 10,
      "targetNode": "node_id_2"
    }
  ],
  "defaultNode": "default_node_id"
}
```

#### 2. 循环节点 (Loop Node)
**官方文档**: [循环节点](https://www.coze.cn/open/docs/guides/loop_node)

**循环配置**:
```json
{
  "loopType": "for_each",
  "iterableVariable": "{{list_data}}",
  "maxIterations": 100,
  "breakCondition": "{{item.status}} == 'complete'",
  "outputAggregation": "array"
}
```

### 数据操作节点配置

#### 1. 数据库节点配置
**相关文档**:
- [SQL 自定义节点](https://www.coze.cn/open/docs/guides/database_sql_node)
- [新增数据节点](https://www.coze.cn/open/docs/guides/database_insert_node)
- [查询数据节点](https://www.coze.cn/open/docs/guides/database_select_node)
- [更新数据节点](https://www.coze.cn/open/docs/guides/database_update_node)
- [删除数据节点](https://www.coze.cn/open/docs/guides/database_delete_node)

**SQL 节点配置**:
```json
{
  "sqlQuery": "SELECT * FROM users WHERE id = {{user_id}}",
  "parameters": [
    {
      "name": "user_id",
      "type": "integer",
      "value": "{{input.userId}}"
    }
  ],
  "outputFormat": "json"
}
```

#### 2. 变量操作节点
**官方文档**: [变量赋值节点](https://www.coze.cn/open/docs/guides/variable_assign_node)

**变量赋值配置**:
```json
{
  "assignments": [
    {
      "variableName": "result",
      "expression": "{{input.value}} * 2",
      "type": "number"
    },
    {
      "variableName": "status",
      "expression": "completed",
      "type": "string"
    }
  ]
}
```

### 知识库和记忆节点配置

#### 1. 知识库节点
**相关文档**:
- [知识库检索节点](https://www.coze.cn/open/docs/guides/knowledge_node)
- [知识库写入节点](https://www.coze.cn/open/docs/guides/knowledge_base_writing_node)

**检索配置**:
```json
{
  "knowledgeBaseId": "kb_12345",
  "query": "{{user_question}}",
  "topK": 5,
  "scoreThreshold": 0.7,
  "retrievalMode": "semantic"
}
```

#### 2. 长期记忆节点
**官方文档**: [长期记忆节点](https://www.coze.cn/open/docs/guides/ltm_node)

**记忆配置**:
```json
{
  "memoryType": "user_preference",
  "userId": "{{user.id}}",
  "operation": "store",
  "content": "{{conversation_summary}}",
  "tags": ["preference", "behavior"]
}
```

## 🔄 变量引用系统

### 变量类型和引用方式

#### 1. 字面量引用
```json
{
  "type": "literal",
  "content": "固定文本内容"
}
```

#### 2. 节点输出引用
```json
{
  "type": "ref",
  "content": {
    "source": "block-output",
    "blockID": "节点ID",
    "name": "输出变量名"
  }
}
```

#### 3. 工作流输入引用
```json
{
  "type": "ref",
  "content": {
    "source": "block-output",
    "blockID": "100001",
    "name": "input"
  }
}
```

#### 4. 变量表达式
```json
{
  "type": "expression",
  "content": "{{variable1}} + {{variable2}}"
}
```

### 数据类型映射

| 类型编码 | 数据类型 | 说明 | 示例 |
|----------|----------|------|------|
| 1 | string | 字符串 | "Hello World" |
| 2 | integer | 整数 | 42 |
| 3 | boolean | 布尔值 | true/false |
| 4 | float | 浮点数 | 3.14 |
| 5 | array | 数组 | [1, 2, 3] |
| 6 | object | 对象 | {"key": "value"} |

## ⚡ 错误处理配置

### 节点级错误处理

```json
{
  "settingOnError": {
    "switch": true,
    "processType": 1,
    "timeoutMs": 300000,
    "retryTimes": 2,
    "retryInterval": 5000
  }
}
```

**参数说明**:
- `switch`: 是否启用错误处理
- `processType`: 错误处理类型 (1: 继续执行, 2: 停止执行)
- `timeoutMs`: 超时时间 (毫秒)
- `retryTimes`: 重试次数
- `retryInterval`: 重试间隔 (毫秒)

### 工作流级错误处理

```json
{
  "globalErrorHandling": {
    "enableGlobalCatch": true,
    "errorHandlerNode": "error_handler_node_id",
    "logLevel": "error",
    "notificationSettings": {
      "email": "<EMAIL>",
      "webhook": "https://webhook.example.com/error"
    }
  }
}
```

## 🔧 性能优化配置

### 并行执行配置

```json
{
  "parallelExecution": {
    "enabled": true,
    "maxConcurrency": 5,
    "batchSize": 10
  }
}
```

### 缓存配置

```json
{
  "cacheSettings": {
    "enabled": true,
    "ttl": 3600,
    "cacheKey": "{{input.hash}}",
    "cacheScope": "workflow"
  }
}
```

## 📊 监控和日志配置

### 日志配置

```json
{
  "logging": {
    "level": "info",
    "includeInputOutput": true,
    "includeExecutionTime": true,
    "customFields": [
      "user_id",
      "session_id"
    ]
  }
}
```

### 监控配置

```json
{
  "monitoring": {
    "enableMetrics": true,
    "metricsEndpoint": "https://metrics.example.com",
    "alertThresholds": {
      "executionTime": 30000,
      "errorRate": 0.05,
      "queueLength": 100
    }
  }
}
```

## 🚀 部署和版本配置

### 版本管理
**官方文档**: [管理工作流版本](https://www.coze.cn/open/docs/guides/workflow_version)

```json
{
  "version": {
    "major": 1,
    "minor": 2,
    "patch": 3,
    "tag": "stable",
    "description": "版本更新说明"
  }
}
```

### 环境配置

```json
{
  "environment": {
    "name": "production",
    "variables": {
      "API_BASE_URL": "https://api.prod.example.com",
      "DEBUG_MODE": false,
      "MAX_RETRIES": 3
    }
  }
}
```

## 📚 相关资源

### 官方文档链接
- [工作流介绍](https://www.coze.cn/open/docs/guides/workflow)
- [使用工作流](https://www.coze.cn/open/docs/guides/use_workflow)
- [工作流使用限制](https://www.coze.cn/open/docs/guides/workflow_limits)
- [工作流常见问题](https://www.coze.cn/open/docs/guides/workflow_faq)

### 开发者资源
- [开发指南](https://www.coze.cn/open/docs/dev_how_to_guides)
- [API 和 SDK](https://www.coze.cn/open/docs/developer_guides)
- [实践教程](https://www.coze.cn/open/docs/tutorial)

---

*本参考文档基于 Coze 官方文档链接整理，提供工作流 API 和配置的详细参考信息。具体参数和配置请以官方最新文档为准。*
