# Coze 工作流官方指南汇总

## 📚 文档索引

基于 Coze 官方文档链接分析，以下是工作流相关的完整官方指南索引：

### 🎯 核心工作流文档

#### 1. 工作流基础
| 文档标题 | 官方链接 | 功能描述 |
|----------|----------|----------|
| 工作流介绍 | [链接](https://www.coze.cn/open/docs/guides/workflow) | 工作流基本概念和功能概述 |
| 工作流与对话流 | [链接](https://www.coze.cn/open/docs/guides/workflow_and_chatflow) | 两种流程模式的区别和选择 |
| 使用工作流 | [链接](https://www.coze.cn/open/docs/guides/use_workflow) | 工作流的基本使用方法 |
| 工作流使用限制 | [链接](https://www.coze.cn/open/docs/guides/workflow_limits) | 使用限制和注意事项 |

#### 2. 节点类型详解
| 节点类型 | 官方链接 | 功能说明 |
|----------|----------|----------|
| 开始和结束节点 | [链接](https://www.coze.cn/open/docs/guides/start_end_node) | 工作流的入口和出口节点 |
| 大模型节点 | [链接](https://www.coze.cn/open/docs/guides/llm_node) | LLM调用和配置 |
| 插件节点 | [链接](https://www.coze.cn/open/docs/guides/plugin_node) | 外部插件集成 |
| 工作流节点 | [链接](https://www.coze.cn/open/docs/guides/workflow_node) | 嵌套工作流调用 |
| 代码节点 | [链接](https://www.coze.cn/open/docs/guides/code_node) | 自定义代码执行 |
| 选择器节点 | [链接](https://www.coze.cn/open/docs/guides/condition_node) | 条件分支控制 |
| 意图识别节点 | [链接](https://www.coze.cn/open/docs/guides/intent_recognition_node) | 用户意图分析 |
| 循环节点 | [链接](https://www.coze.cn/open/docs/guides/loop_node) | 循环处理逻辑 |
| 批处理节点 | [链接](https://www.coze.cn/open/docs/guides/batch_node) | 批量数据处理 |
| 变量聚合节点 | [链接](https://www.coze.cn/open/docs/guides/variable_merge_node) | 变量合并处理 |
| 输入节点 | [链接](https://www.coze.cn/open/docs/guides/input_node) | 用户输入处理 |
| 输出节点 | [链接](https://www.coze.cn/open/docs/guides/message_node) | 结果输出控制 |

#### 3. 数据库操作节点
| 节点类型 | 官方链接 | 功能说明 |
|----------|----------|----------|
| SQL 自定义节点 | [链接](https://www.coze.cn/open/docs/guides/database_sql_node) | 自定义SQL查询 |
| 新增数据节点 | [链接](https://www.coze.cn/open/docs/guides/database_insert_node) | 数据库插入操作 |
| 查询数据节点 | [链接](https://www.coze.cn/open/docs/guides/database_select_node) | 数据库查询操作 |
| 更新数据节点 | [链接](https://www.coze.cn/open/docs/guides/database_update_node) | 数据库更新操作 |
| 删除数据节点 | [链接](https://www.coze.cn/open/docs/guides/database_delete_node) | 数据库删除操作 |

#### 4. 高级功能节点
| 节点类型 | 官方链接 | 功能说明 |
|----------|----------|----------|
| 变量赋值节点 | [链接](https://www.coze.cn/open/docs/guides/variable_assign_node) | 变量操作和赋值 |
| 知识库写入节点 | [链接](https://www.coze.cn/open/docs/guides/knowledge_base_writing_node) | 知识库数据写入 |
| 知识库检索节点 | [链接](https://www.coze.cn/open/docs/guides/knowledge_node) | 知识库内容检索 |
| 长期记忆节点 | [链接](https://www.coze.cn/open/docs/guides/ltm_node) | 长期记忆管理 |
| 图像生成节点 | [链接](https://www.coze.cn/open/docs/guides/image_generation_node) | AI图像生成 |
| 画板节点 | [链接](https://www.coze.cn/open/docs/guides/canvas_node) | 图像编辑画板 |
| 图像处理插件节点 | [链接](https://www.coze.cn/open/docs/guides/Image_plugin_node) | 图像处理功能 |
| HTTP 请求节点 | [链接](https://www.coze.cn/open/docs/guides/http_node) | HTTP API调用 |
| 问答节点 | [链接](https://www.coze.cn/open/docs/guides/question_node) | 问答处理逻辑 |
| 文本处理节点 | [链接](https://www.coze.cn/open/docs/guides/text_processing_node) | 文本处理功能 |
| JSON 序列化节点 | [链接](https://www.coze.cn/open/docs/guides/json_serialization_node) | JSON数据序列化 |
| JSON 反序列化节点 | [链接](https://www.coze.cn/open/docs/guides/json_deserialization_node) | JSON数据解析 |

#### 5. 触发器和定时任务
| 节点类型 | 官方链接 | 功能说明 |
|----------|----------|----------|
| 设置定时触发器 | [链接](https://www.coze.cn/open/docs/guides/set_timed_trigger) | 定时任务设置 |
| 查询定时触发器节点 | [链接](https://www.coze.cn/open/docs/guides/query_timed_trigger) | 定时任务查询 |
| 删除定时触发器节点 | [链接](https://www.coze.cn/open/docs/guides/delete_timed_trigger) | 定时任务删除 |

#### 6. 会话管理节点
| 节点类型 | 官方链接 | 功能说明 |
|----------|----------|----------|
| 创建会话节点 | [链接](https://www.coze.cn/open/docs/guides/create_conversation) | 会话创建管理 |
| 修改会话节点 | [链接](https://www.coze.cn/open/docs/guides/edit_conversation) | 会话信息修改 |
| 删除会话节点 | [链接](https://www.coze.cn/open/docs/guides/delete_conversation) | 会话删除操作 |
| 查看会话列表节点 | [链接](https://www.coze.cn/open/docs/guides/query_conversation_list) | 会话列表查询 |
| 查询会话历史节点 | [链接](https://www.coze.cn/open/docs/guides/query_conversation_history) | 会话历史查询 |
| 清空会话历史节点 | [链接](https://www.coze.cn/open/docs/guides/clear_conversation_history) | 会话历史清理 |

#### 7. 消息管理节点
| 节点类型 | 官方链接 | 功能说明 |
|----------|----------|----------|
| 创建消息节点 | [链接](https://www.coze.cn/open/docs/guides/create_message) | 消息创建发送 |
| 修改消息节点 | [链接](https://www.coze.cn/open/docs/guides/edit_message) | 消息内容修改 |
| 删除消息节点 | [链接](https://www.coze.cn/open/docs/guides/delete_message) | 消息删除操作 |
| 查询消息列表节点 | [链接](https://www.coze.cn/open/docs/guides/query_message_list) | 消息列表查询 |

### 🔧 工作流管理

#### 版本管理
| 功能 | 官方链接 | 说明 |
|------|----------|------|
| 管理工作流版本 | [链接](https://www.coze.cn/open/docs/guides/workflow_version) | 版本控制和发布管理 |
| 封装与解散工作流 | [链接](https://www.coze.cn/open/docs/guides/group_workflow) | 工作流模块化管理 |

#### 协作开发
| 功能 | 官方链接 | 说明 |
|------|----------|------|
| 协同管理工作流 | [链接](https://www.coze.cn/open/docs/guides/collaborate_workflow) | 多人协作开发 |

#### 问题解决
| 资源 | 官方链接 | 说明 |
|------|----------|------|
| 工作流常见问题 | [链接](https://www.coze.cn/open/docs/guides/workflow_faq) | 常见问题和解决方案 |

### 🚀 应用集成

#### API 集成
| 功能 | 官方链接 | 说明 |
|------|----------|------|
| 通过 API 运行应用工作流 | [链接](https://www.coze.cn/open/docs/guides/run_app_as_api) | API方式调用工作流 |

#### 智能体集成
| 功能 | 官方链接 | 说明 |
|------|----------|------|
| 工作流 (智能体中) | [链接](https://www.coze.cn/open/docs/guides/agent_workflow) | 在智能体中使用工作流 |

## 📖 文档使用指南

### 学习路径建议

#### 初学者路径
1. **工作流介绍** - 了解基本概念
2. **工作流与对话流** - 理解不同模式
3. **使用工作流** - 掌握基本操作
4. **开始和结束节点** - 学习基础节点
5. **大模型节点** - 掌握核心功能

#### 进阶开发者路径
1. **插件节点** - 扩展功能集成
2. **代码节点** - 自定义逻辑开发
3. **选择器节点** - 条件控制逻辑
4. **循环节点** - 复杂流程控制
5. **HTTP 请求节点** - 外部API集成

#### 高级应用路径
1. **数据库操作节点** - 数据持久化
2. **知识库节点** - 知识管理
3. **图像处理节点** - 多媒体处理
4. **工作流版本管理** - 生产环境部署
5. **协同管理工作流** - 团队协作

### 快速查找指南

#### 按功能分类
- **数据处理**: SQL节点、变量节点、JSON节点
- **AI功能**: 大模型节点、图像生成节点、意图识别节点
- **外部集成**: 插件节点、HTTP节点
- **流程控制**: 选择器节点、循环节点、批处理节点
- **用户交互**: 输入节点、输出节点、问答节点

#### 按应用场景
- **内容生成**: 大模型节点 + 文本处理节点
- **数据分析**: 数据库节点 + 代码节点
- **图像处理**: 图像生成节点 + 图像处理插件节点
- **API服务**: HTTP节点 + JSON节点
- **知识问答**: 知识库节点 + 问答节点

### 注意事项

1. **访问限制**: 部分官方文档可能有地区访问限制
2. **版本更新**: 官方文档会定期更新，建议关注最新版本
3. **实践结合**: 建议结合实际项目进行学习
4. **社区资源**: 可以参考社区案例和最佳实践

---

*本指南基于 Coze 官方文档链接整理，提供工作流相关文档的完整索引和学习路径建议。由于网络限制，具体内容请访问官方链接获取最新信息。*
