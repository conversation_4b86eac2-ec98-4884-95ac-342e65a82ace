# Coze 工作流快速参考卡片

## 🚀 快速开始

### 核心概念速查
| 概念 | 定义 | 用途 |
|------|------|------|
| 工作流 | 节点和连接组成的自动化流程 | 复杂业务逻辑编排 |
| 节点 | 执行特定功能的处理单元 | 数据处理和转换 |
| 连接 | 节点间的数据传递路径 | 控制执行顺序 |
| 变量 | 存储和传递数据的容器 | 节点间数据共享 |

### 常用节点类型
| 节点类型 | 图标 | 主要功能 | 使用频率 |
|----------|------|----------|----------|
| 大模型节点 | 🤖 | LLM内容生成 | ⭐⭐⭐⭐⭐ |
| 插件节点 | 🔌 | 外部服务集成 | ⭐⭐⭐⭐ |
| 输出节点 | 📤 | 结果输出控制 | ⭐⭐⭐⭐⭐ |
| 选择器节点 | 🔀 | 条件分支控制 | ⭐⭐⭐ |
| 循环节点 | 🔄 | 循环处理逻辑 | ⭐⭐ |
| 代码节点 | 💻 | 自定义代码执行 | ⭐⭐⭐ |
| HTTP节点 | 🌐 | API调用 | ⭐⭐⭐ |

## ⚙️ 配置模板

### 大模型节点模板

#### 基础配置
```json
{
  "modleName": "DeepSeek-V3-0324",
  "temperature": 0.8,
  "maxTokens": 4096,
  "systemPrompt": "你是一个专业的AI助手",
  "prompt": "{{input}}"
}
```

#### 创意内容生成
```json
{
  "modleName": "GPT-4",
  "temperature": 1.0,
  "maxTokens": 8192,
  "systemPrompt": "你是一个富有创意的内容创作者",
  "prompt": "基于以下主题创作内容：{{topic}}"
}
```

#### 事实性问答
```json
{
  "modleName": "Claude-3",
  "temperature": 0.3,
  "maxTokens": 2048,
  "systemPrompt": "你是一个准确可靠的知识助手",
  "prompt": "请准确回答：{{question}}"
}
```

### 插件节点模板

#### 基础插件配置
```json
{
  "apiParam": [
    {
      "name": "pluginID",
      "input": {"value": {"content": "插件ID"}}
    },
    {
      "name": "apiName", 
      "input": {"value": {"content": "API名称"}}
    }
  ],
  "inputParameters": [
    {
      "name": "参数名",
      "input": {
        "type": "string",
        "value": {"type": "ref", "content": {...}}
      }
    }
  ]
}
```

### HTTP请求节点模板

#### GET请求
```json
{
  "method": "GET",
  "url": "https://api.example.com/data",
  "headers": {
    "Authorization": "Bearer {{token}}"
  },
  "timeout": 30000
}
```

#### POST请求
```json
{
  "method": "POST",
  "url": "https://api.example.com/submit",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "data": "{{input_data}}"
  },
  "timeout": 60000
}
```

### 选择器节点模板

#### 数值比较
```json
{
  "conditions": [
    {
      "expression": "{{score}} >= 80",
      "targetNode": "high_score_node"
    },
    {
      "expression": "{{score}} >= 60",
      "targetNode": "medium_score_node"
    }
  ],
  "defaultNode": "low_score_node"
}
```

#### 文本匹配
```json
{
  "conditions": [
    {
      "expression": "{{category}} == 'urgent'",
      "targetNode": "urgent_handler"
    },
    {
      "expression": "{{category}} == 'normal'",
      "targetNode": "normal_handler"
    }
  ],
  "defaultNode": "default_handler"
}
```

## 🔧 常用配置片段

### 错误处理配置
```json
{
  "settingOnError": {
    "switch": true,
    "processType": 1,
    "timeoutMs": 300000,
    "retryTimes": 2
  }
}
```

### 变量引用配置
```json
{
  "type": "ref",
  "content": {
    "source": "block-output",
    "blockID": "节点ID",
    "name": "输出变量名"
  }
}
```

### 输出节点配置
```json
{
  "content": {
    "type": "string",
    "value": {
      "type": "literal",
      "content": "处理完成：{{result}}"
    }
  },
  "streamingOutput": false
}
```

## 📊 性能优化速查

### 并行处理优化
```
串行模式: A → B → C → D (时间: tA + tB + tC + tD)
并行模式: A → (B || C) → D (时间: tA + max(tB, tC) + tD)
```

### 缓存策略
| 缓存类型 | 适用场景 | 配置方式 |
|----------|----------|----------|
| 节点缓存 | 重复计算 | 节点级配置 |
| 变量缓存 | 数据复用 | 变量作用域 |
| API缓存 | 外部调用 | HTTP节点配置 |

### 超时时间建议
| 节点类型 | 建议超时 | 最大超时 |
|----------|----------|----------|
| 大模型节点 | 60-300秒 | 600秒 |
| 插件节点 | 30-180秒 | 300秒 |
| HTTP节点 | 10-60秒 | 120秒 |
| 代码节点 | 5-30秒 | 60秒 |

## 🐛 调试技巧速查

### 调试步骤
1. **单节点测试** - 逐个验证节点功能
2. **数据流检查** - 确认变量传递正确
3. **错误日志分析** - 查看详细错误信息
4. **性能分析** - 识别性能瓶颈

### 常见错误快速修复
| 错误类型 | 快速检查 | 修复方法 |
|----------|----------|----------|
| 变量未找到 | 检查变量名拼写 | 修正变量引用 |
| 类型不匹配 | 检查数据类型 | 添加类型转换 |
| 超时错误 | 检查网络连接 | 增加超时时间 |
| 权限错误 | 检查API密钥 | 更新认证信息 |

## 📋 最佳实践清单

### 设计阶段 ✅
- [ ] 明确工作流目标和范围
- [ ] 设计清晰的数据流向
- [ ] 识别并行处理机会
- [ ] 规划错误处理策略
- [ ] 考虑性能优化点

### 开发阶段 ✅
- [ ] 使用描述性的节点名称
- [ ] 添加必要的注释说明
- [ ] 实现完整的错误处理
- [ ] 设置合理的超时时间
- [ ] 添加调试输出节点

### 测试阶段 ✅
- [ ] 单节点功能测试
- [ ] 端到端流程测试
- [ ] 边界条件测试
- [ ] 性能压力测试
- [ ] 错误场景测试

### 部署阶段 ✅
- [ ] 确认所有依赖可用
- [ ] 验证权限配置正确
- [ ] 设置监控和告警
- [ ] 建立备份和回滚机制
- [ ] 编写操作文档

## 🔗 快速链接

### 官方文档
- [工作流介绍](https://www.coze.cn/open/docs/guides/workflow)
- [使用工作流](https://www.coze.cn/open/docs/guides/use_workflow)
- [工作流常见问题](https://www.coze.cn/open/docs/guides/workflow_faq)

### 节点文档
- [大模型节点](https://www.coze.cn/open/docs/guides/llm_node)
- [插件节点](https://www.coze.cn/open/docs/guides/plugin_node)
- [选择器节点](https://www.coze.cn/open/docs/guides/condition_node)

### 高级功能
- [工作流版本管理](https://www.coze.cn/open/docs/guides/workflow_version)
- [协同管理工作流](https://www.coze.cn/open/docs/guides/collaborate_workflow)

## 🎯 常用代码片段

### Python代码节点示例
```python
def main(input_data):
    # 数据处理逻辑
    result = process_data(input_data)
    return {
        "success": True,
        "result": result,
        "timestamp": datetime.now().isoformat()
    }

def process_data(data):
    # 具体处理逻辑
    return data.upper()
```

### JavaScript代码节点示例
```javascript
function main(inputData) {
    // 数据处理逻辑
    const result = processData(inputData);
    return {
        success: true,
        result: result,
        timestamp: new Date().toISOString()
    };
}

function processData(data) {
    // 具体处理逻辑
    return data.toLowerCase();
}
```

### JSON处理示例
```json
{
  "parseJSON": "JSON.parse({{json_string}})",
  "stringifyJSON": "JSON.stringify({{object}})",
  "extractField": "{{object}}.field_name",
  "arrayLength": "{{array}}.length"
}
```

## 📱 移动端适配

### 响应式设计
- 使用相对单位而非固定像素
- 考虑不同屏幕尺寸的布局
- 优化触摸交互体验

### 性能优化
- 减少不必要的网络请求
- 压缩图片和资源文件
- 实现懒加载机制

---

*本快速参考卡片提供 Coze 工作流开发的核心信息和常用配置，便于开发者快速查找和使用。*
