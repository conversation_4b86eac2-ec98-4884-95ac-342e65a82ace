{"code": 0, "data": {"has_more": true, "next_cursor": "10", "products": [{"commercial_setting": {"commercial_type": 0}, "meta_info": {"category": {"active_icon_url": "", "count": 0, "icon_url": "", "id": "7327137275714748453", "index": 0, "name": "图像"}, "description": "剪映草稿，视频合成方案。", "entity_id": "7457837925833801768", "entity_type": 2, "entity_version": "0", "favorite_count": 6737, "heat": 17163, "icon_url": "https://p9-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/61eceb6aeeda49c5957953dfe3b99731~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1751202342&x-signature=vXIj1ZZqD13sI3E5thJeyEOaNu8%3D", "id": "7457837529027493922", "is_favorited": false, "is_free": true, "is_official": false, "is_professional": false, "is_template": false, "labels": [], "listed_at": "1748586929", "medium_icon_url": "", "name": "视频合成_剪映小助手", "origin_icon_url": "", "readme": "{\"0\":{\"ops\":[{\"attributes\":{\"bold\":\"true\"},\"insert\":\"剪映小助手插件功能简介\"},{\"insert\":\"\\n\"},{\"insert\":\"——AI驱动的高效视频合成工具\\n\"},{\"attributes\":{\"lmkr\":\"1\"},\"insert\":\"*\"},{\"insert\":\"配合token获取：\"},{\"insert\":\"https://www.51aigc.cc/#/\",\"attributes\":{\"clientside-auto-url\":\"https://www.51aigc.cc/#/\"}},{\"insert\":\"\\n\"},{\"attributes\":{\"lmkr\":\"1\"},\"insert\":\"*\"},{\"insert\":\"✅ \"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"高效批处理\"},{\"insert\":\"：一键操作批量素材，节省90%重复劳动。\\n\"},{\"insert\":\"✅ \"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"AI友好\"},{\"insert\":\"：无缝衔接AIGC生成内容（如图文/音频），加速创作流程。\\n\"},{\"insert\":\"✅ \"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"云端协同\"},{\"insert\":\"：草稿多端同步+云渲染，打破设备限制。\\n\"},{\"insert\":\"✅ \"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"灵活扩展\"},{\"insert\":\"：开放插件生态，支持自定义动画、特效等高级功能。\\n\"},{\"attributes\":{\"lmkr\":\"1\"},\"insert\":\"*\"},{\"insert\":\"\\n\"}],\"zoneId\":\"0\",\"zoneType\":\"Z\"}}", "seller": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "id": "0", "name": "速推AIGC"}, "status": 1, "user_info": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "name": "速推AIGC", "user_id": "0", "user_name": "sutui"}}, "plugin_extra": {"associated_bots_use_count": 0, "avg_exec_time": 351.42024470588234, "bots_use_count": 10427, "call_amount": 16073228, "connectors": [], "is_default_icon": false, "is_official": false, "is_premium": false, "plugin_type": 0, "space_id": "0", "success_rate": 0.9380717647058825, "tools": [{"avg_exec_time": 320.79566, "bots_use_count": 1355, "call_amount": 783980, "description": "插件入口-创建草稿", "id": "7457837955684515874", "name": "create_draft", "parameters": [{"description": "高", "name": "height", "required": false, "sub_params": [], "type": "integer"}, {"description": "关联创作者，用来获取推广分成", "name": "user_id", "required": false, "sub_params": [], "type": "integer"}, {"description": "宽", "name": "width", "required": false, "sub_params": [], "type": "integer"}], "run_mode": 0, "success_rate": 0.99864}, {"avg_exec_time": 337.4154, "bots_use_count": 2610, "call_amount": 135606, "description": "云渲染视频，接收create_draft输出的链接", "id": "7475240499365019682", "name": "gen_video", "parameters": [{"description": "获取：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "create_draft输出的draft_url", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99961}, {"avg_exec_time": 144.79718, "bots_use_count": 3324, "call_amount": 2494, "description": "获取图片出入场动画", "id": "7464464264288567334", "name": "get_image_animations", "parameters": [{"description": "默认0 全部 1 vip 2 免费", "name": "mode", "required": false, "sub_params": [], "type": "integer"}, {"description": "三个值，默认in：in 入场 out  出场 loop  组合", "name": "type", "required": false, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 1}, {"avg_exec_time": 360.16384, "bots_use_count": 1429, "call_amount": 1818264, "description": "添加关键帧", "id": "7465608338500452404", "name": "add_keyframes", "parameters": [{"description": "草稿地址，使用create_draft输出的draft_url即可", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}, {"description": "关键帧数据，格式： [     {       \"offset\": 5000000,       \"property\": \"KFTypePositionX\",       \"segment_id\": \"acc5b516-046b-4eae-a179-f686f35e70a8\",       \"value\": 0     }   ]", "name": "keyframes", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99472}, {"avg_exec_time": 182.9486, "bots_use_count": 227, "call_amount": 9987, "description": "查询视频状态。", "id": "7505185824548569103", "name": "gen_video_status", "parameters": [{"description": "草稿链接", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.9998}, {"avg_exec_time": 997.06308, "bots_use_count": 1274, "call_amount": 133271, "description": "快速上手，简易创建素材轨道\ncreate_draft+easy_create_material即可", "id": "7469369511599276051", "name": "easy_create_material", "parameters": [{"description": "字幕y轴的位置", "name": "text_transform_y", "required": false, "sub_params": [], "type": "number"}, {"description": "音频，轨道的开始结束以音频为主。", "name": "audio_url", "required": true, "sub_params": [], "type": "string"}, {"description": "草稿地址", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}, {"description": "字幕字体大小", "name": "font_size", "required": false, "sub_params": [], "type": "integer"}, {"description": "图片", "name": "img_url", "required": false, "sub_params": [], "type": "string"}, {"description": "字幕", "name": "text", "required": false, "sub_params": [], "type": "string"}, {"description": "字幕颜色", "name": "text_color", "required": false, "sub_params": [], "type": "string"}, {"description": "视频", "name": "video_url", "required": false, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99257}, {"avg_exec_time": 341.46143, "bots_use_count": 1892, "call_amount": 2440063, "description": "批量添加图片", "id": "7457837925833883688", "name": "add_images", "parameters": [{"description": "移动transform_x", "name": "transform_x", "required": false, "sub_params": [], "type": "number"}, {"description": "移动transform_y", "name": "transform_y", "required": false, "sub_params": [], "type": "number"}, {"description": "图片透明度，值0-1", "name": "alpha", "required": false, "sub_params": [], "type": "number"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/Pgm9wXA4EipKhYkeEQJcLBlJnWb?from=from_copylink", "name": "image_infos", "required": true, "sub_params": [], "type": "string"}, {"description": "x缩放", "name": "scale_x", "required": false, "sub_params": [], "type": "number"}, {"description": "y缩放", "name": "scale_y", "required": false, "sub_params": [], "type": "number"}], "run_mode": 0, "success_rate": 0.99685}, {"avg_exec_time": 601.19087, "bots_use_count": 1212, "call_amount": 561585, "description": "保存草稿\n", "id": "7457837955684548642", "name": "save_draft", "parameters": [{"description": "草稿地址，使用create_draft输出的draft_url即可", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}, {"description": "用户ID。如果填写了这个ID，新用户产生的月费就会按照比例归属到这个账号下。", "name": "user_id", "required": false, "sub_params": [], "type": "integer"}], "run_mode": 0, "success_rate": 0.99686}, {"avg_exec_time": 394.87627, "bots_use_count": 1833, "call_amount": 284354, "description": "添加特效", "id": "7466630948491231268", "name": "add_effects", "parameters": [{"description": "特效数组内容，eg:[{\"effect_title\":\"金粉闪闪\",\"end\":5000000,\"start\":0}]", "name": "effect_infos", "required": true, "sub_params": [], "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.994}, {"avg_exec_time": 318.269, "bots_use_count": 2736, "call_amount": 206223, "description": "批量添加视频", "id": "7457837955684499490", "name": "add_videos", "parameters": [{"description": "剪映scale_x 缩放", "name": "scale_x", "required": false, "sub_params": [], "type": "number"}, {"description": "剪映scale_y 缩放", "name": "scale_y", "required": false, "sub_params": [], "type": "number"}, {"description": "剪映x位置", "name": "transform_x", "required": false, "sub_params": [], "type": "number"}, {"description": "剪映y位置", "name": "transform_y", "required": false, "sub_params": [], "type": "number"}, {"description": "[     {         \"video_url\": \"https://example.com/video1.mp4\",         \"duration\": 12000000,  // 视频时长，以秒*1000000为单位         \"width\": 1920,    // 视频宽度         \"height\": 1080,   // 视频高度         \"start\": 0,       // 视频在时间轴上的开始时间         \"end\": 12000000        // 视频在时间轴上的结束时间     } ]", "name": "video_infos", "required": true, "sub_params": [], "type": "string"}, {"description": "透明度：0-1 ，默认1", "name": "alpha", "required": false, "sub_params": [], "type": "number"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.9914}, {"avg_exec_time": 676.29434, "bots_use_count": 3165, "call_amount": 1975617, "description": "批量添加字幕", "id": "7457837925833850920", "name": "add_captions", "parameters": [{"description": "字幕的对齐方式，0左对齐，1 居中对齐，2右对齐，3竖排居顶，4竖排居中，5竖排居底", "name": "alignment", "required": false, "sub_params": [], "type": "integer"}, {"description": "字体透明度0-1,默认1 ", "name": "alpha", "required": false, "sub_params": [], "type": "number"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/HhQrw3BFhi1XGOkkJCBcfkqGnwf?from=from_copylink", "name": "captions", "required": true, "sub_params": [], "type": "string"}, {"description": "字间距，默认0", "name": "letter_spacing", "required": false, "sub_params": [], "type": "number"}, {"description": "文字颜色：#ff1837", "name": "text_color", "required": false, "sub_params": [], "type": "string"}, {"description": "transform_y位置", "name": "transform_y", "required": false, "sub_params": [], "type": "number"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}, {"description": "默认：15", "name": "font_size", "required": false, "sub_params": [], "type": "integer"}, {"description": "scale_x缩放", "name": "scale_x", "required": false, "sub_params": [], "type": "number"}, {"description": "边框颜色，eg：#fe8a80", "name": "border_color", "required": false, "sub_params": [], "type": "string"}, {"description": "0 默认。1富文本样式", "name": "style_text", "required": false, "sub_params": [], "type": "integer"}, {"description": "transform_x位置", "name": "transform_x", "required": false, "sub_params": [], "type": "number"}, {"description": "字体列表：https://krxc4izye0.feishu.cn/wiki/SmnrwabXriG7JckEzyGcChk4nDd", "name": "font", "required": false, "sub_params": [], "type": "string"}, {"description": "行间距，默认0", "name": "line_spacing", "required": false, "sub_params": [], "type": "number"}, {"description": "scale_y缩放", "name": "scale_y", "required": false, "sub_params": [], "type": "number"}], "run_mode": 0, "success_rate": 0.99736}, {"avg_exec_time": 98.37629, "bots_use_count": 1528, "call_amount": 6884, "description": "创建文本富文本样式", "id": "7464220249576112128", "name": "add_text_style", "parameters": [{"description": "字体大小，默认15", "name": "font_size", "required": false, "sub_params": [], "type": "integer"}, {"description": "关键词", "name": "keyword", "required": true, "sub_params": [], "type": "string"}, {"description": "关键词颜色，默认：#ff7100", "name": "keyword_color", "required": false, "sub_params": [], "type": "string"}, {"description": "关键词字体大小，默认15", "name": "keyword_font_size", "required": false, "sub_params": [], "type": "integer"}, {"description": "文本内容", "name": "text", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.98969}, {"avg_exec_time": 243.10618, "bots_use_count": 1099, "call_amount": 16588, "description": "添加贴纸，批处理时，并发要设置成1 ", "id": "7479633158624018432", "name": "add_sticker", "parameters": [{"description": "结束时间", "name": "end", "required": true, "sub_params": [], "type": "integer"}, {"description": "缩放，0-5", "name": "scale", "required": false, "sub_params": [], "type": "number"}, {"description": "开始时间", "name": "start", "required": true, "sub_params": [], "type": "integer"}, {"description": "贴纸ID，从数据生成器search_sticker里面获取", "name": "sticker_id", "required": true, "sub_params": [], "type": "string"}, {"description": "剪映的x位置", "name": "transform_x", "required": false, "sub_params": [], "type": "integer"}, {"description": "剪映的y位置", "name": "transform_y", "required": false, "sub_params": [], "type": "integer"}, {"description": "草稿地址", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99845}, {"avg_exec_time": 342.41794, "bots_use_count": 2223, "call_amount": 1592034, "description": "批量添加音频", "id": "7457837925833834536", "name": "add_audios", "parameters": [{"description": "[{\"audio_url\": \"http://example.com/audio1.mp3\",\"duration\":120,\"start\":0,\"end\":12000000,\"audio_effect\":\"教堂\"}]", "name": "audio_infos", "required": true, "sub_params": [], "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99731}, {"avg_exec_time": 441.63273, "bots_use_count": 1019, "call_amount": 6104042, "description": "获取音频时长", "id": "7474863657353117750", "name": "get_audio_duration", "parameters": [{"description": "音频链接", "name": "mp3_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99996}, {"avg_exec_time": 0, "bots_use_count": 5, "call_amount": 1, "description": "增加蒙版", "id": "7510122630821953575", "name": "add_masks", "parameters": [{"description": "对应剪映的X位置", "name": "X", "required": false, "sub_params": [], "type": "integer"}, {"description": "对应剪映的Y位置", "name": "Y", "required": false, "sub_params": [], "type": "integer"}, {"description": "羽化0-100", "name": "feather", "required": false, "sub_params": [], "type": "integer"}, {"description": "旋转角度0-360", "name": "rotation", "required": false, "sub_params": [], "type": "integer"}, {"description": "add_videos或者add_images的返回值", "name": "segment_ids", "required": true, "sub_params": [{"description": "列表ids", "name": "", "required": false, "sub_params": [], "type": "string"}], "type": "array"}, {"description": "对应剪映的宽高", "name": "width", "required": false, "sub_params": [], "type": "integer"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "name": "draft_url", "required": true, "sub_params": [], "type": "string"}, {"description": "对应剪映的宽高", "name": "height", "required": false, "sub_params": [], "type": "integer"}, {"description": "是否反转", "name": "invert", "required": false, "sub_params": [], "type": "boolean"}, {"description": "蒙板类型：线性/镜面/圆形/矩形/爱心/星形，文档：https://krxc4izye0.feishu.cn/wiki/XVGLw75wIii389k6pCKcQvarnMb", "name": "name", "required": true, "sub_params": [], "type": "string"}, {"description": "矩形的圆角", "name": "round<PERSON><PERSON><PERSON>", "required": false, "sub_params": [], "type": "integer"}], "run_mode": 0, "success_rate": 0}, {"avg_exec_time": 173.33535, "bots_use_count": 2654, "call_amount": 2235, "description": "获取文字出入场动画", "id": "7464464264288583718", "name": "get_text_animations", "parameters": [{"description": "0 默认所有。1 VIP。 2 免费", "name": "mode", "required": false, "sub_params": [], "type": "integer"}, {"description": "in 入场。out 出场。loop 循环", "name": "type", "required": false, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 1}], "total_api_count": 17}, "user_behavior": {"used_at": "0", "viewed_at": "0"}}, {"commercial_setting": {"commercial_type": 0}, "meta_info": {"category": {"active_icon_url": "", "count": 0, "icon_url": "", "id": "7327137275714748453", "index": 0, "name": "图像"}, "description": "视频合成工具箱", "entity_id": "7418409962344333350", "entity_type": 2, "entity_version": "0", "favorite_count": 850, "heat": 13318, "icon_url": "https://p9-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/3636cc593cf9481e8d751434928fab2d~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1751201034&x-signature=wR82BZlVVkvkTSgm6zSYFSuGDWw%3D", "id": "7418405865834709002", "is_favorited": false, "is_free": true, "is_official": false, "is_professional": false, "is_template": false, "labels": [], "listed_at": "1748560321", "medium_icon_url": "", "name": "视频合成工具箱", "origin_icon_url": "", "readme": "{\"0\":{\"ops\":[{\"attributes\":{\"lmkr\":\"1\",\"list\":\"bullet1\",\"start\":\"1\",\"origin-start\":\"1\"},\"insert\":\"*\"},{\"insert\":\"img_to_video\\n\"},{\"insert\":\"输入图片和时长直接变成视频。\\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"bullet1\",\"start\":\"1\",\"origin-start\":\"1\"},\"insert\":\"*\"},{\"insert\":\"img_audio_video\\n\"},{\"insert\":\"把图片和音频合并成视频 输入图片和音频 生成视频链接\\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"bullet1\",\"start\":\"1\",\"origin-start\":\"1\"},\"insert\":\"*\"},{\"insert\":\"video_merging\\n\"},{\"insert\":\"视频合并，输入多个视频拼接成一个完整视频。不要输入太多，节点可能会超时。如果很多视频，请用视频合成-剪映小助手的方案。\\n\"}],\"zoneId\":\"0\",\"zoneType\":\"Z\"}}", "seller": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "id": "0", "name": "速推AIGC"}, "status": 1, "user_info": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "name": "速推AIGC", "user_id": "0", "user_name": "sutui"}}, "plugin_extra": {"associated_bots_use_count": 0, "avg_exec_time": 5129.853538, "bots_use_count": 12468, "call_amount": 213968, "connectors": [], "is_default_icon": false, "is_official": false, "is_premium": false, "plugin_type": 0, "space_id": "0", "success_rate": 0.901878, "tools": [{"avg_exec_time": 7098.83036, "bots_use_count": 3799, "call_amount": 21595, "description": "视频合并，输入多个视频拼接成一个完整视频，请注意节点超时时间，如果视频很多，请求使用视频合成剪映小助手的方式。", "id": "7424414550021210112", "name": "video_merging", "parameters": [{"description": "点击获取：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "转场，默认没有，填写random随机转场", "name": "transition", "required": false, "sub_params": [], "type": "string"}, {"description": "视频列表", "name": "video_urls", "required": true, "sub_params": [{"description": "视频内容", "name": "", "required": false, "sub_params": [], "type": "string"}], "type": "array"}], "run_mode": 0, "success_rate": 0.96676}, {"avg_exec_time": 1421.20773, "bots_use_count": 1144, "call_amount": 24063, "description": "平台视频下载", "id": "7446747632359292928", "name": "video_download", "parameters": [{"description": "提取地址：https://www.51aigc.cc/#/userInfo", "name": "api_token", "required": false, "sub_params": [], "type": "string"}, {"description": "平台分享链接", "name": "share_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.56736}, {"avg_exec_time": 3239.95136, "bots_use_count": 415, "call_amount": 1964, "description": "上传视频到oss", "id": "7450399075838754816", "name": "upload_video_oss", "parameters": [{"description": "video_url", "name": "video_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99719}, {"avg_exec_time": 7745.09605, "bots_use_count": 225, "call_amount": 1259, "description": "输入图片和时长直接变成视频。", "id": "7501920772559044658", "name": "img_to_video", "parameters": [{"description": "提取地址：https://www.51aigc.cc/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "图片地址。", "name": "img_url", "required": true, "sub_params": [], "type": "string"}, {"description": "单位秒1-60秒，默认1秒", "name": "time", "required": false, "sub_params": [], "type": "number"}], "run_mode": 0, "success_rate": 0.9887}, {"avg_exec_time": 6144.18219, "bots_use_count": 3124, "call_amount": 165087, "description": "把图片和音频合并成视频\n输入图片和音频\n生成的视频链接", "id": "7424348612005494822", "name": "img_audio_video", "parameters": [{"description": "点击获取：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "图片内容", "name": "img_url", "required": true, "sub_params": [], "type": "string"}, {"description": "运动类型，0 静态 1 关键帧随机放大", "name": "motion", "required": false, "sub_params": [], "type": "integer"}, {"description": "音频内容", "name": "mp3_url", "required": true, "sub_params": [], "type": "string"}, {"description": "-20 到20 ，声音分贝，默认0 ", "name": "volume_db", "required": false, "sub_params": [], "type": "integer"}], "run_mode": 0, "success_rate": 0.98938}], "total_api_count": 5}, "user_behavior": {"used_at": "0", "viewed_at": "0"}}, {"commercial_setting": {"commercial_type": 0}, "meta_info": {"category": {"active_icon_url": "", "count": 0, "icon_url": "", "id": "7327137275714748453", "index": 0, "name": "图像"}, "description": "豆包图生视频", "entity_id": "7504579163953774592", "entity_type": 2, "entity_version": "0", "favorite_count": 117, "heat": 176, "icon_url": "https://p3-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/1609ed4d3e0d45a9afb7c08c9ddd65b0~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1751200802&x-signature=weT%2BYaVLxyKVQS0%2FxtYZz%2BvEI38%3D", "id": "7504578949696061492", "is_favorited": false, "is_free": true, "is_official": false, "is_professional": false, "is_template": false, "labels": [], "listed_at": "1748507468", "medium_icon_url": "", "name": "豆包图生视频", "origin_icon_url": "", "readme": "{\"0\":{\"ops\":[{\"insert\":\"豆包官方图生视频API\\n\"}],\"zoneId\":\"0\",\"zoneType\":\"Z\"}}", "seller": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "id": "0", "name": "速推AIGC"}, "status": 1, "user_info": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "name": "速推AIGC", "user_id": "0", "user_name": "sutui"}}, "plugin_extra": {"associated_bots_use_count": 0, "avg_exec_time": 457.2613533333333, "bots_use_count": 59, "call_amount": 66944, "connectors": [], "is_default_icon": false, "is_official": false, "is_premium": false, "plugin_type": 0, "space_id": "0", "success_rate": 0.99966, "tools": [{"avg_exec_time": 109.38182, "bots_use_count": 21, "call_amount": 42046, "description": "获取任务详情，即将下架", "id": "7504579163953823744", "name": "task_info", "parameters": [{"description": "等待次数，默认不等待", "name": "wait_cnt", "required": false, "sub_params": [], "type": "integer"}, {"description": "任务ID", "name": "task_id", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99983}, {"avg_exec_time": 992.40224, "bots_use_count": 54, "call_amount": 24779, "description": "豆包生成视频插件，插件比较贵1块一次，请谨慎使用。", "id": "7504579163953790976", "name": "img2_video", "parameters": [{"description": "是否固定摄像头。枚举值： true：固定摄像头。平台会在用户提示词中追加固定摄像头，实际效果不保证。 false：不固定摄像头。", "name": "camerafixed", "required": false, "sub_params": [], "type": "boolean"}, {"description": "时长，目前只能：5/10秒", "name": "duration", "required": false, "sub_params": [], "type": "integer"}, {"description": "图片链接", "name": "image_url", "required": true, "sub_params": [], "type": "string"}, {"description": "模型：doubao-seedance-1-0-lite-i2v-250428    doubao-seaweed-241128", "name": "model", "required": false, "sub_params": [], "type": "string"}, {"description": "提示词", "name": "prompt", "required": true, "sub_params": [], "type": "string"}, {"description": "提取地址：https://www.51aigc.cc/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "是否同步返回内容，开启后这个节点会等待成功才结束，有可能会出现超时失败。请按需使用。", "name": "asyn", "required": false, "sub_params": [], "type": "boolean"}], "run_mode": 0, "success_rate": 0.99915}, {"avg_exec_time": 270, "bots_use_count": 0, "call_amount": 119, "description": "获取任务信息", "id": "7509780733309894665", "name": "task_info_v2", "parameters": [{"description": "任务id", "name": "task_id", "required": true, "sub_params": [], "type": "string"}, {"description": "等待次数", "name": "wait_cnt", "required": false, "sub_params": [], "type": "integer"}], "run_mode": 0, "success_rate": 1}], "total_api_count": 3}, "user_behavior": {"used_at": "0", "viewed_at": "0"}}, {"commercial_setting": {"commercial_type": 0}, "meta_info": {"category": {"active_icon_url": "", "count": 0, "icon_url": "", "id": "7327137275714846757", "index": 0, "name": "游戏与娱乐"}, "description": "视频搜索", "entity_id": "7472938609872666665", "entity_type": 2, "entity_version": "0", "favorite_count": 286, "heat": 5287, "icon_url": "https://p9-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/2c357bb680564153a311da576312bf02~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1751200991&x-signature=7pDVrWmnvVX55sWIXDQoaOWuERk%3D", "id": "7472938301821878291", "is_favorited": false, "is_free": true, "is_official": false, "is_professional": false, "is_template": false, "labels": [], "listed_at": "1748502224", "medium_icon_url": "", "name": "视频搜索", "origin_icon_url": "", "readme": "{\"0\":{\"ops\":[{\"insert\":\"'''\\n\"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"插件功能：\"},{\"insert\":\" \\n\"},{\"insert\":\"本插件可用于视频搜索，能通过关键词搜索视频，并可设置视频时长、发布时间、排序类型等参数。\\n\"},{\"insert\":\"\\n\"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"工具介绍：\"},{\"insert\":\" \\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"number1\",\"start\":\"1\",\"origin-start\":\"1\",\"ol-id\":\"BvHCxvaP\"},\"insert\":\"*\"},{\"insert\":\"工具名称：search\\n\"},{\"insert\":\" - \"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"用途\"},{\"insert\":\"：通过关键词搜索视频。\\n\"}],\"zoneId\":\"0\",\"zoneType\":\"Z\"}}", "seller": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "id": "0", "name": "速推AIGC"}, "status": 1, "user_info": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "name": "速推AIGC", "user_id": "0", "user_name": "sutui"}}, "plugin_extra": {"associated_bots_use_count": 0, "avg_exec_time": 3616.218484, "bots_use_count": 5001, "call_amount": 101287, "connectors": [], "is_default_icon": false, "is_official": false, "is_premium": false, "plugin_type": 0, "space_id": "0", "success_rate": 0.978845, "tools": [{"avg_exec_time": 4172.27463, "bots_use_count": 803, "call_amount": 76108, "description": "抖作品数据", "id": "7472938609872683049", "name": "douyin_data", "parameters": [{"description": "获取：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "分享链接", "name": "douyin_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.96005}, {"avg_exec_time": 5875.40813, "bots_use_count": 225, "call_amount": 6752, "description": "获取用户主页视频", "id": "7477055395467673652", "name": "get_user_video_list", "parameters": [{"description": "最大数量", "name": "count", "required": false, "sub_params": [], "type": "integer"}, {"description": "最大游标，用于翻页，第一页为0，第二页为第一次响应中的max_cursor值。", "name": "max_cursor", "required": false, "sub_params": [], "type": "integer"}, {"description": "用户sec_user_id，可以从douyin_data方法的返回里面提取", "name": "sec_user_id", "required": true, "sub_params": [], "type": "string"}, {"description": "获取：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.98323}, {"avg_exec_time": 2450.87647, "bots_use_count": 109, "call_amount": 464, "description": "获取主页信息数据", "id": "7481099405513293862", "name": "get_user_infos", "parameters": [{"description": "获取：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "用户sec_user_id，可以从douyin_data方法的返回里面提取", "name": "sec_user_id", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.92941}, {"avg_exec_time": 5374.56944, "bots_use_count": 344, "call_amount": 361, "description": "获取关键词搜索榜", "id": "7476736926217961522", "name": "get_keyword_rank", "parameters": [{"description": "页码", "name": "page", "required": false, "sub_params": [], "type": "integer"}, {"description": "size", "name": "page_size", "required": false, "sub_params": [], "type": "integer"}, {"description": "获取：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": " 时间窗口，1 按小时 2 按天", "name": "date_window", "required": false, "sub_params": [], "type": "integer"}, {"description": "搜索关键字", "name": "keyword", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 1}, {"avg_exec_time": 414.84076, "bots_use_count": 1886, "call_amount": 648, "description": "获取话题榜", "id": "7476736926217945138", "name": "get_hot_topic", "parameters": [{"description": "每页数量", "name": "page_size", "required": false, "sub_params": [], "type": "integer"}, {"description": "https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "时间窗口，1 按小时 2 按天", "name": "date_window", "required": false, "sub_params": [], "type": "integer"}, {"description": "页码", "name": "page", "required": false, "sub_params": [], "type": "integer"}], "run_mode": 0, "success_rate": 0.99363}, {"avg_exec_time": 4257.12381, "bots_use_count": 111, "call_amount": 107, "description": "抖音评论", "id": "7500143832181342260", "name": "douyin_comment", "parameters": [{"description": "提取地址：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "数量", "name": "count", "required": false, "sub_params": [], "type": "integer"}, {"description": "页码", "name": "cursor", "required": false, "sub_params": [], "type": "integer"}, {"description": "抖分享链接", "name": "share_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 1}, {"avg_exec_time": 1607.37255, "bots_use_count": 672, "call_amount": 282, "description": "获取飙升的关键词榜单", "id": "7476736926217928754", "name": "get_high_keyword_rank", "parameters": [{"description": "关键词", "name": "keyword", "required": true, "sub_params": [], "type": "string"}, {"description": "page", "name": "page", "required": false, "sub_params": [], "type": "integer"}, {"description": "size", "name": "page_size", "required": false, "sub_params": [], "type": "integer"}, {"description": "获取：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": " 时间窗口，1 按小时 2 按天", "name": "date_window", "required": false, "sub_params": [], "type": "integer"}], "run_mode": 0, "success_rate": 1}, {"avg_exec_time": 1284.61202, "bots_use_count": 1252, "call_amount": 1294, "description": "获取飙升话题榜", "id": "7476736926217912370", "name": "get_high_hot_topic", "parameters": [{"description": "获取：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "时间窗口，1 按小时 2 按天", "name": "date_window", "required": false, "sub_params": [], "type": "integer"}, {"description": "页码", "name": "page", "required": false, "sub_params": [], "type": "integer"}, {"description": "数量", "name": "page_size", "required": false, "sub_params": [], "type": "integer"}], "run_mode": 0, "success_rate": 0.99454}, {"avg_exec_time": 5778.62226, "bots_use_count": 1749, "call_amount": 11693, "description": "关键词搜索视频（如果不出数据可使用：关键词视频搜索升级版douyin_search_v2）", "id": "7472938609872699433", "name": "douyin_search", "parameters": [{"description": "排序类型，可用值: _0(综合), _1(最多点赞), _2(最新发布)", "name": "sort_type", "required": false, "sub_params": [], "type": "string"}, {"description": "获取：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "视频时长，可用值: _0(不限), _1(1分钟以下), _2(1-5分钟), _3(5分钟以上)", "name": "filter_duration", "required": false, "sub_params": [], "type": "string"}, {"description": "关键词", "name": "keyword", "required": true, "sub_params": [], "type": "string"}, {"description": "默认1", "name": "page", "required": false, "sub_params": [], "type": "integer"}, {"description": "发布时间，可用值: _0(不限), _1(一天之内), _7(一周之内), _180(半年之内)", "name": "publish_time", "required": false, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.93476}, {"avg_exec_time": 4946.48477, "bots_use_count": 222, "call_amount": 3578, "description": "获取所有的用户视频", "id": "7479738075145486372", "name": "get_user_video_all", "parameters": [{"description": "获取：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "取多少页数据，一页50条，太多有时候会触发超时，50条扣费一次。", "name": "page", "required": false, "sub_params": [], "type": "integer"}, {"description": "用户sec_user_id，可以从douyin_data方法的返回里面提取", "name": "sec_user_id", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99283}], "total_api_count": 10}, "user_behavior": {"used_at": "0", "viewed_at": "0"}}, {"commercial_setting": {"commercial_type": 0}, "meta_info": {"category": {"active_icon_url": "", "count": 0, "icon_url": "", "id": "7327137275714748453", "index": 0, "name": "图像"}, "description": "即梦图片开口说话", "entity_id": "7507847231064735763", "entity_type": 2, "entity_version": "0", "favorite_count": 37, "heat": 47, "icon_url": "https://p6-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/cc9d4d770e3042678697cbc089f6d5e4~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1751201316&x-signature=ZYzzlPTsUIJ1qPjYTOWH1oyWMt8%3D", "id": "7507846139539111947", "is_favorited": false, "is_free": true, "is_official": false, "is_professional": false, "is_template": false, "labels": [], "listed_at": "1748436278", "medium_icon_url": "", "name": "即梦图片开口说话", "origin_icon_url": "", "readme": "{\"0\":{\"ops\":[{\"insert\":\"即梦数字人\\n\"},{\"attributes\":{\"lmkr\":\"1\"},\"insert\":\"*\"},{\"insert\":\"即梦图片对口型\\n\"},{\"attributes\":{\"lmkr\":\"1\"},\"insert\":\"*\"},{\"insert\":\"即梦开口说话\\n\"}],\"zoneId\":\"0\",\"zoneType\":\"Z\"}}", "seller": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "id": "0", "name": "速推AIGC"}, "status": 1, "user_info": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "name": "速推AIGC", "user_id": "0", "user_name": "sutui"}}, "plugin_extra": {"associated_bots_use_count": 0, "avg_exec_time": 437.54172, "bots_use_count": 10, "call_amount": 8572, "connectors": [], "is_default_icon": false, "is_official": false, "is_premium": false, "plugin_type": 0, "space_id": "0", "success_rate": 0.99979, "tools": [{"avg_exec_time": 182.56276, "bots_use_count": 6, "call_amount": 8124, "description": "获取任务信息", "id": "7507847231064768531", "name": "lips_task_info", "parameters": [{"description": "task_id", "name": "task_id", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99958}, {"avg_exec_time": 692.52068, "bots_use_count": 4, "call_amount": 448, "description": "图片开口说话", "id": "7507847231064752147", "name": "img_lips", "parameters": [{"description": "音频链接，音频不能大于15s", "name": "mp3_url", "required": true, "sub_params": [], "type": "string"}, {"description": "获取地址：https://www.51aigc.cc/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "图片链接", "name": "img_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 1}], "total_api_count": 2}, "user_behavior": {"used_at": "0", "viewed_at": "0"}}, {"commercial_setting": {"commercial_type": 0}, "meta_info": {"category": {"active_icon_url": "", "count": 0, "icon_url": "", "id": "7327137275714748453", "index": 0, "name": "图像"}, "description": "即梦图生视频", "entity_id": "7506116067216015394", "entity_type": 2, "entity_version": "0", "favorite_count": 124, "heat": 166, "icon_url": "https://p3-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/fd2936ba0cc2437db79d87a4b14d8d2b~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1751201131&x-signature=NiK8bd8EVypok%2FjuhQkEtSw%2Bpj0%3D", "id": "7506115717880791074", "is_favorited": false, "is_free": true, "is_official": false, "is_professional": false, "is_template": false, "labels": [], "listed_at": "1748436271", "medium_icon_url": "", "name": "即梦图生视频", "origin_icon_url": "", "readme": "{\"0\":{\"ops\":[{\"insert\":\"即梦图生视频\\n\"},{\"attributes\":{\"lmkr\":\"1\"},\"insert\":\"*\"},{\"insert\":\"配合token：\"},{\"insert\":\"https://www.51aigc.cc/#/userInfo\",\"attributes\":{\"clientside-auto-url\":\"https://www.51aigc.cc/#/userInfo\"}},{\"insert\":\"\\n\"}],\"zoneId\":\"0\",\"zoneType\":\"Z\"}}", "seller": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "id": "0", "name": "速推AIGC"}, "status": 1, "user_info": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "name": "速推AIGC", "user_id": "0", "user_name": "sutui"}}, "plugin_extra": {"associated_bots_use_count": 0, "avg_exec_time": 567.4515633333333, "bots_use_count": 42, "call_amount": 127823, "connectors": [], "is_default_icon": false, "is_official": false, "is_premium": false, "plugin_type": 0, "space_id": "0", "success_rate": 0.94274, "tools": [{"avg_exec_time": 494.22238, "bots_use_count": 21, "call_amount": 13437, "description": "图生视频", "id": "7506116067216031778", "name": "img2_video", "parameters": [{"description": "模型：视频3.0/视频S2.0/视频S2.0 Pro/视频P2.0 Pro/视频1.2", "name": "model", "required": false, "sub_params": [], "type": "string"}, {"description": "提示词", "name": "prompt", "required": false, "sub_params": [], "type": "string"}, {"description": "获取地址：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "图片链接", "name": "img_url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 1}, {"avg_exec_time": 143, "bots_use_count": 16, "call_amount": 115, "description": "获取视频记录", "id": "7507787150033879059", "name": "user_video", "parameters": [{"description": "获取地址：https://www.51aigc.cc/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "页码", "name": "page", "required": false, "sub_params": [], "type": "integer"}, {"description": "分页大小", "name": "size", "required": false, "sub_params": [], "type": "integer"}], "run_mode": 0, "success_rate": 0.9646}, {"avg_exec_time": 1065.13231, "bots_use_count": 15, "call_amount": 114271, "description": "获取任务信息", "id": "7506116067216048162", "name": "task_info", "parameters": [{"description": "提交任务返回task_id", "name": "task_id", "required": true, "sub_params": [], "type": "string"}, {"description": "等待次数，每次间隔10秒，最大18", "name": "wait_cnt", "required": false, "sub_params": [], "type": "integer"}], "run_mode": 0, "success_rate": 0.86362}], "total_api_count": 3}, "user_behavior": {"used_at": "0", "viewed_at": "0"}}, {"commercial_setting": {"commercial_type": 0}, "meta_info": {"category": {"active_icon_url": "", "count": 0, "icon_url": "", "id": "7327137275714748453", "index": 0, "name": "图像"}, "description": "海螺图生视频", "entity_id": "7432232943004745778", "entity_type": 2, "entity_version": "0", "favorite_count": 416, "heat": 4186, "icon_url": "https://p6-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/d81553c5fc3d4f778f3c7f1bb16c512b~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1751201656&x-signature=CEljM2hGv2XCPf7fm1Ypoix2llI%3D", "id": "7432231425165852699", "is_favorited": false, "is_free": true, "is_official": false, "is_professional": false, "is_template": false, "labels": [], "listed_at": "1748316736", "medium_icon_url": "", "name": "海螺图生视频", "origin_icon_url": "", "readme": "{\"0\":{\"ops\":[{\"attributes\":{\"bold\":\"true\"},\"insert\":\"插件功能：\"},{\"insert\":\" 海螺 AI 是一款强大的人工智能插件，为用户提供了多种功能。\\n\"},{\"insert\":\"\\n\"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"工具介绍：\"},{\"insert\":\"\\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"number1\",\"start\":\"1\",\"origin-start\":\"1\"},\"insert\":\"*\"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"get_task_info\"},{\"insert\":\"：\\n\"},{\"insert\":\" - \"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"使用场景\"},{\"insert\":\"：当你需要获取特定任务的详细信息时，例如任务的状态、结果等。\\n\"},{\"insert\":\" - \"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"工具描述\"},{\"insert\":\"：通过提供任务的 ID 和有效的令牌，你可以获取该任务的相关信息。\\n\"},{\"insert\":\"\\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"number1\",\"start\":\"2\",\"origin-start\":\"2\",\"ol-id\":\"3qVwQK4v\"},\"insert\":\"*\"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"create_task_list\"},{\"insert\":\"：\\n\"},{\"insert\":\" - \"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"使用场景\"},{\"insert\":\"：如果你有多个任务需要一次性提交，或者需要批量处理任务，这个工具非常有用。\\n\"},{\"insert\":\" - \"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"工具描述\"},{\"insert\":\"：你可以提供一个包含图像 URL 和提示文本的列表，以及有效的令牌，插件将创建这些任务并进行处理。\\n\"},{\"insert\":\"\\n\"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"示例：\"},{\"insert\":\"\\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"number1\",\"start\":\"1\",\"origin-start\":\"1\",\"ol-id\":\"nHfsyZZG\"},\"insert\":\"*\"},{\"insert\":\"使用 get_task_info 获取任务信息：\\n\"},{\"insert\":\" - 假设你已经有一个任务 ID，例如 \\\"123\\\"，并且你拥有有效的令牌。\\n\"},{\"insert\":\" - 你可以调用 get_task_info 接口，将任务 ID 和令牌作为参数传递。\\n\"},{\"insert\":\" - 插件将返回该任务的详细信息，例如任务的状态、结果等。\\n\"},{\"insert\":\"\\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"number1\",\"start\":\"2\",\"origin-start\":\"2\",\"ol-id\":\"XWVhILiX\"},\"insert\":\"*\"},{\"insert\":\"使用 create_task_list 批量提交任务：\\n\"},{\"insert\":\" - 假设你有一个包含多个任务的列表，每个任务包含图像 URL 和提示文本。\\n\"},{\"insert\":\" - 你可以调用 create_task_list 接口，将任务列表和令牌作为参数传递。\\n\"},{\"insert\":\" - 插件将创建这些任务并进行处理，你可以在后续的步骤中跟踪任务的状态和结果。\\n\"},{\"insert\":\"\\n\"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"注意事项：\"},{\"insert\":\"\\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"number1\",\"start\":\"1\",\"origin-start\":\"1\",\"ol-id\":\"guihR5pC\"},\"insert\":\"*\"},{\"insert\":\"在使用插件之前，请确保你已经获取了有效的令牌。\\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"number1\",\"start\":\"2\",\"origin-start\":\"2\",\"ol-id\":\"guihR5pC\"},\"insert\":\"*\"},{\"insert\":\"对于 get_task_info 接口，确保提供的任务 ID 是正确的，否则可能无法获取到正确的任务信息。\\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"number1\",\"start\":\"3\",\"origin-start\":\"3\",\"ol-id\":\"guihR5pC\"},\"insert\":\"*\"},{\"insert\":\"对于 create_task_list 接口，确保任务列表的格式正确，并且图像 URL 是可访问的。\\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"number1\",\"start\":\"4\",\"origin-start\":\"4\",\"ol-id\":\"guihR5pC\"},\"insert\":\"*\"},{\"insert\":\"在使用插件时，请遵守相关的使用条款和规定。\\n\"}],\"zoneId\":\"0\",\"zoneType\":\"Z\"}}", "seller": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "id": "0", "name": "速推AIGC"}, "status": 1, "user_info": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "name": "速推AIGC", "user_id": "0", "user_name": "sutui"}}, "plugin_extra": {"associated_bots_use_count": 0, "avg_exec_time": 1104.6739325, "bots_use_count": 3770, "call_amount": 650966, "connectors": [], "is_default_icon": false, "is_official": false, "is_premium": false, "plugin_type": 0, "space_id": "0", "success_rate": 0.99716, "tools": [{"avg_exec_time": 278.95691, "bots_use_count": 165, "call_amount": 515544, "description": "获取任务信息。", "id": "7491869556743536677", "name": "st_get_task_info", "parameters": [{"description": "任务ID", "name": "task_id", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99988}, {"avg_exec_time": 2249.33939, "bots_use_count": 518, "call_amount": 68801, "description": "海螺创建图生视频任务。", "id": "7491869556743520293", "name": "st_create_task_list", "parameters": [{"description": "模型：对应网页版本的模型名字，默认不填写， T2V-01-Director 填写 23000 ， t2v-01 填写 23010 ，t2v-01-director 填写 23102 ，i2v-01-live 填写 23011 ，i2v-01 填写 23001 ", "name": "modelID", "required": false, "sub_params": [], "type": "string"}, {"description": "提示词", "name": "prompt", "required": false, "sub_params": [], "type": "string"}, {"description": "提取地址：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "图片", "name": "image_url", "required": false, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.98998}, {"avg_exec_time": 414.13148, "bots_use_count": 507, "call_amount": 58595, "description": "获取任务信息", "id": "7432232943004778546", "name": "get_task_info", "parameters": [{"description": "id", "name": "task_id", "required": true, "sub_params": [], "type": "string"}, {"description": "token：http://chat.fyshark.com/public/#/accountList", "name": "token", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99979}, {"avg_exec_time": 1476.26795, "bots_use_count": 1034, "call_amount": 8026, "description": "批量提交任务", "id": "7432232943004762162", "name": "create_task_list", "parameters": [{"description": "token：http://chat.fyshark.com/public/#/accountList", "name": "token", "required": true, "sub_params": [], "type": "string"}, {"description": "img", "name": "image_url", "required": false, "sub_params": [], "type": "string"}, {"description": "23000 T2V-01-Director 23010  t2v-01  23102 t2v -01-director     23011 i2v-01-live   23001  i2v-01", "name": "model_id", "required": false, "sub_params": [], "type": "string"}, {"description": "text", "name": "prompt", "required": false, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99899}], "total_api_count": 4}, "user_behavior": {"used_at": "0", "viewed_at": "0"}}, {"commercial_setting": {"commercial_type": 0}, "meta_info": {"category": {"active_icon_url": "", "count": 0, "icon_url": "", "id": "7327137275714748453", "index": 0, "name": "图像"}, "description": "输入字幕和音频，对齐时间线", "entity_id": "7475821548809502758", "entity_type": 2, "entity_version": "0", "favorite_count": 125, "heat": 315, "icon_url": "https://p9-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/6bed3de41f994829a1229838ca0c9d1b~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1751200712&x-signature=qRYpKqeO9VmZR3yHv03ryvxHLfA%3D", "id": "7475813983916703771", "is_favorited": false, "is_free": true, "is_official": false, "is_professional": false, "is_template": false, "labels": [], "listed_at": "1747869950", "medium_icon_url": "", "name": "字幕音频对齐", "origin_icon_url": "", "readme": "{\"0\":{\"ops\":[{\"insert\":\"'''\\n\"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"插件功能：\"},{\"insert\":\" 用于将输入的文本和音频进行时间线对齐。\\n\"},{\"attributes\":{\"bold\":\"true\"},\"insert\":\"工具介绍：\"},{\"insert\":\"\\n\"},{\"attributes\":{\"lmkr\":\"1\",\"list\":\"number1\",\"start\":\"1\",\"origin-start\":\"1\"},\"insert\":\"*\"},{\"insert\":\"工具名称：alignTextToAudio\\n\"},{\"insert\":\" - 功能介绍：通过输入用户标识、音频和文本，实现文本和音频的对齐。\\n\"},{\"insert\":\" - 使用场景：适用于需要对特定音频和相关文本进行精确时间对应分析的情况，比如对有声读物的文字与语音进行校对，对歌曲的歌词和演唱进行同步等。\\n\"},{\"insert\":\"'''\\n\"}],\"zoneId\":\"0\",\"zoneType\":\"Z\"}}", "seller": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "id": "0", "name": "速推AIGC"}, "status": 1, "user_info": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "name": "速推AIGC", "user_id": "0", "user_name": "sutui"}}, "plugin_extra": {"associated_bots_use_count": 0, "avg_exec_time": 1580.12805, "bots_use_count": 190, "call_amount": 106415, "connectors": [], "is_default_icon": false, "is_official": false, "is_premium": false, "plugin_type": 0, "space_id": "0", "success_rate": 0.98151, "tools": [{"avg_exec_time": 1580.12805, "bots_use_count": 190, "call_amount": 106415, "description": "字幕音频对齐工具", "id": "7475821548809519142", "name": "alignTextToAudio", "parameters": [{"description": "用户标识，可以在https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "音频", "name": "audio_url", "required": true, "sub_params": [], "type": "string"}, {"description": "每行最大字数", "name": "max_chars_per_line", "required": false, "sub_params": [], "type": "integer"}, {"description": "文本，可以使用\\n换行断句", "name": "text", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.98151}], "total_api_count": 1}, "user_behavior": {"used_at": "0", "viewed_at": "0"}}, {"commercial_setting": {"commercial_type": 0}, "meta_info": {"category": {"active_icon_url": "", "count": 0, "icon_url": "", "id": "7327137275714764837", "index": 0, "name": "实用工具"}, "description": "用来长时间等待的异步工具。\n最长可以等待24小时。\n使用说明：https://krxc4izye0.feishu.cn/wiki/HIoYwasmyiYn09kalCfcUDBxnxe?from=from_copylink", "entity_id": "7496481999658696745", "entity_type": 2, "entity_version": "0", "favorite_count": 46, "heat": 186, "icon_url": "https://p6-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/4adb6f1418744df992414d6626b0a9b4~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1751201445&x-signature=O%2Bbft4G%2BF%2BhXy1lHxaJVm0%2BS2%2BU%3D", "id": "7496481532262170663", "is_favorited": false, "is_free": true, "is_official": false, "is_professional": false, "is_template": false, "labels": [], "listed_at": "1746849346", "medium_icon_url": "", "name": "定时器等待狗", "origin_icon_url": "", "readme": "{\"0\":{\"ops\":[{\"insert\":\"用来长时间等待的异步工具。\\n\"},{\"insert\":\"最长可以等待24小时。\\n\"},{\"attributes\":{\"lmkr\":\"1\"},\"insert\":\"*\"},{\"insert\":\"使用说明：\\n\"},{\"attributes\":{\"lmkr\":\"1\"},\"insert\":\"*\"},{\"insert\":\"https://krxc4izye0.feishu.cn/wiki/HIoYwasmyiYn09kalCfcUDBxnxe?from=from_copylink\",\"attributes\":{\"clientside-auto-url\":\"https://krxc4izye0.feishu.cn/wiki/HIoYwasmyiYn09kalCfcUDBxnxe?from=from_copylink\"}},{\"insert\":\"\\n\"}],\"zoneId\":\"0\",\"zoneType\":\"Z\"}}", "seller": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "id": "0", "name": "速推AIGC"}, "status": 1, "user_info": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "name": "速推AIGC", "user_id": "0", "user_name": "sutui"}}, "plugin_extra": {"associated_bots_use_count": 0, "avg_exec_time": 1257.9868933333335, "bots_use_count": 140, "call_amount": 4807, "connectors": [], "is_default_icon": false, "is_official": false, "is_premium": false, "plugin_type": 0, "space_id": "0", "success_rate": 0.7458533333333334, "tools": [{"avg_exec_time": 582.93568, "bots_use_count": 77, "call_amount": 4740, "description": "循环执行工作流。配合coze api来使用。", "id": "7496481999658713129", "name": "wait", "parameters": [{"description": "循环执行完成后通知执行的工作流ID。", "name": "callback_workflow_id", "required": false, "sub_params": [], "type": "string"}, {"description": "扣子token，提取地址：https://www.coze.cn/open/oauth/pats", "name": "coze_token", "required": false, "sub_params": [], "type": "string"}, {"description": "工作流对于的多个输入参数", "name": "input_data", "required": false, "sub_params": [], "type": "string"}, {"description": "后续用来查询执行记录，提取地址：https://www.51aigc.cc/#/userInfo", "name": "api_token", "required": false, "sub_params": [], "type": "string"}, {"description": "判断是否立刻执行回调。默认判断done参数", "name": "callback_key", "required": false, "sub_params": [], "type": "string"}, {"description": "工作流对于的输入参数，多个参数使用input_data", "name": "input", "required": false, "sub_params": [], "type": "string"}, {"description": "最大循环次数。默认300，每次执行间隔1分钟。", "name": "max_cnt", "required": false, "sub_params": [], "type": "integer"}, {"description": "需要循环执行的工作流ID：https://www.coze.cn/work_flow?space_id=7391771569926127635&workflow_id=xxxxx ，这个xxxx就是工作流ID", "name": "workflow_id", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.86256}, {"avg_exec_time": 136.4, "bots_use_count": 11, "call_amount": 54, "description": "获取任务执行信息。", "id": "7496780903813939263", "name": "task_info", "parameters": [{"description": "wait返回的task_id", "name": "task_id", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 1}, {"avg_exec_time": 3054.625, "bots_use_count": 60, "call_amount": 13, "description": "通过api_token获取任务列表。", "id": "7496780903813922879", "name": "get_task_list", "parameters": [{"description": "分页数量", "name": "pageSize", "required": false, "sub_params": [], "type": "integer"}, {"description": "提取地址：https://ts.fyshark.com/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "页数", "name": "page", "required": false, "sub_params": [], "type": "integer"}], "run_mode": 0, "success_rate": 0.375}], "total_api_count": 3}, "user_behavior": {"used_at": "0", "viewed_at": "0"}}, {"commercial_setting": {"commercial_type": 0}, "meta_info": {"category": {"active_icon_url": "", "count": 0, "icon_url": "", "id": "7327137275714764837", "index": 0, "name": "实用工具"}, "description": "记录生成的文件到账号中。解决coze有些链接失效快的问题。", "entity_id": "7501479195944730661", "entity_type": 2, "entity_version": "0", "favorite_count": 11, "heat": 47, "icon_url": "https://p6-flow-product-sign.byteimg.com/tos-cn-i-13w3uml6bg/594dbe347fa04f09b9b43d90d16022d9~tplv-13w3uml6bg-resize:128:128.image?rk3s=2e2596fd&x-expires=1751202413&x-signature=gDPTjtY%2FXMD5lV7wOlwOrWMvMDU%3D", "id": "7501474762439524362", "is_favorited": false, "is_free": true, "is_official": false, "is_professional": false, "is_template": false, "labels": [], "listed_at": "1746771441", "medium_icon_url": "", "name": "文本保存", "origin_icon_url": "", "readme": "{\"0\":{\"ops\":[{\"insert\":\"记录生成的文件到账号中。解决coze有些链接失效快的问题。\\n\"}],\"zoneId\":\"0\",\"zoneType\":\"Z\"}}", "seller": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "id": "0", "name": "速推AIGC"}, "status": 1, "user_info": {"avatar_url": "https://p9-passport.byteacctimg.com/img/user-avatar/64633947d8e8cd80fed49da3c19c5aee~300x300.image", "name": "速推AIGC", "user_id": "0", "user_name": "sutui"}}, "plugin_extra": {"associated_bots_use_count": 0, "avg_exec_time": 925.58625, "bots_use_count": 36, "call_amount": 381429, "connectors": [], "is_default_icon": false, "is_official": false, "is_premium": false, "plugin_type": 0, "space_id": "0", "success_rate": 0.99996, "tools": [{"avg_exec_time": 925.58625, "bots_use_count": 36, "call_amount": 381429, "description": "文件保存", "id": "7501479248150921253", "name": "file_save", "parameters": [{"description": "提取地址：https://www.51aigc.cc/#/userInfo", "name": "api_token", "required": true, "sub_params": [], "type": "string"}, {"description": "默认自动识别，文件后缀，图片使用.png 音频使用.mp3 视频使用.mp4", "name": "file_type", "required": false, "sub_params": [], "type": "string"}, {"description": "音频，图片，视频等链接", "name": "url", "required": true, "sub_params": [], "type": "string"}], "run_mode": 0, "success_rate": 0.99996}], "total_api_count": 1}, "user_behavior": {"used_at": "0", "viewed_at": "0"}}]}, "message": ""}