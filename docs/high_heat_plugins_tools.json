[{"name": "<PERSON><PERSON>", "id": "7328314516272463923", "tools": [{"name": "calculate", "description": "逐步计算数学表达式。", "parameters": [{"name": "input", "description": "输入一个数学表达式。如果输入不是英文，请先将其翻译成英文。", "required": true, "type": "string"}]}, {"name": "query", "description": "计算数学表达式的结果", "parameters": [{"name": "i", "description": "数学表达式，需要转成英文，不接受与数学无关的问题 ", "required": false, "type": "string"}]}]}, {"name": "语音转文本", "id": "7342819789989052470", "tools": [{"name": "SpeechToText", "description": "读取音频url链接，并将音频转为文字", "parameters": [{"name": "url", "description": "音频url链接", "required": true, "type": "string"}]}]}, {"name": "创客贴智能设计", "id": "7351267469065011241", "tools": [{"name": "ckt_intelligent_design", "description": "输入设计需求，即刻生成精美设计图；涵盖手机海报、宣传图、电商图、店铺广告、日签、社交媒体配图等多样场景。", "parameters": [{"name": "query", "description": "用户输入主题", "required": true, "type": "string"}]}]}, {"name": "头条视频搜索", "id": "7345693026326446080", "tools": [{"name": "ToutiaoVideoSearch", "description": "当你需要查找头条上的视频时，可以使用此插件", "parameters": [{"name": "count", "description": "响应中返回的搜索结果数量。默认值为5，最大值为20。实际返回结果的数量可能会少于请求的数量。", "required": false, "type": "string"}, {"name": "offset", "description": "从结果中返回前要跳过的基于零的偏移量。默认为0。", "required": false, "type": "string"}, {"name": "query", "description": "用户想要搜索视频的查询词。查询词不能为空。", "required": true, "type": "string"}]}]}, {"name": "板栗看板", "id": "7358789295274049572", "tools": [{"name": "generateTasks", "description": "根据用户提问拆解任务，生成任务看板", "parameters": [{"name": "input", "description": "用户的完整输入内容，只支持中文", "required": true, "type": "string"}]}]}, {"name": "飞常准", "id": "7328314169139380234", "tools": [{"name": "getRoute", "description": "获取航班列表", "parameters": [{"name": "arr", "description": "到达城市名称，仅接受名称，不接受代码", "required": true, "type": "string"}, {"name": "dep", "description": "出发城市名称，不接受代码。", "required": true, "type": "string"}, {"name": "depTime", "description": "出发日期，默认为今天日期，例如：2023-12-19。注意格式需要是2023-01-03，不可以是2023-1-3等其他格式", "required": true, "type": "string"}]}]}, {"name": "天眼查", "id": "7407722060627247143", "tools": [{"name": "change_log", "description": "可以通过公司名称（需公司全称）或ID获取企业变更记录，变更记录包括工商变更事项、变更前后信息等字段的详细信息。", "parameters": [{"name": "pageNum", "description": "当前页数（默认第1页）", "required": false, "type": "integer"}, {"name": "pageSize", "description": "每页条数（默认20条，最大20条）", "required": false, "type": "integer"}, {"name": "keyword", "description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "required": true, "type": "string"}]}, {"name": "company_detail", "description": "可以通过公司名称（需公司全称）或ID获取企业基本信息和企业联系方式，包括公司名称或ID、类型、成立日期、电话、邮箱、网址等字段的详细信息。", "parameters": [{"name": "keyword", "description": "搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "required": true, "type": "string"}]}, {"name": "search", "description": "可以通过关键词获取企业列表，企业列表包括公司名称或ID、类型、成立日期、经营状态、统一社会信用代码等字段的详细信息。", "parameters": [{"name": "word", "description": "关键词", "required": true, "type": "string"}, {"name": "pageSize", "description": "每页条数（默认20条，最大20条）", "required": false, "type": "integer"}, {"name": "pageNum", "description": "当前页数（默认第1页）", "required": false, "type": "integer"}]}, {"name": "news", "description": "可以通过公司名称（需公司全称）或ID精确匹配获取新闻列表。", "parameters": [{"name": "endTime", "description": "结束时间", "required": false, "type": "string"}, {"name": "pageNum", "description": "当前页数（默认第1页）", "required": false, "type": "integer"}, {"name": "tags", "description": "标签（多个逗号分隔，默认返回全部）", "required": false, "type": "string"}, {"name": "name", "description": "公司名称（精确匹配、name和id其一为必填即可）", "required": false, "type": "string"}, {"name": "pageSize", "description": "每页条数（默认20条，最大20条）", "required": false, "type": "integer"}, {"name": "startTime", "description": "开始时间", "required": false, "type": "string"}, {"name": "id", "description": "公司id（name和id其一为必填即可）", "required": false, "type": "integer"}]}, {"name": "abnormal_operation", "description": "可以通过公司名称（需公司全称）或ID获取企业经营异常信息，经营异常信息包括列入/移除原因、时间、做出决定机关等字段的详细信息。", "parameters": [{"name": "pageNum", "description": "当前页数（默认第1页）", "required": false, "type": "integer"}, {"name": "keyword", "description": "String\t搜索关键字（公司名称、公司id、注册号或社会统一信用代码）", "required": true, "type": "string"}, {"name": "pageSize", "description": "每页条数（默认20条，最大20条）", "required": false, "type": "integer"}]}]}, {"name": "百度地图", "id": "7394300204087787570", "tools": [{"name": "ip_to_address", "description": "根据用户输入的IP地址，能够快速的帮用户定位IP的所在位置。", "parameters": [{"name": "ip", "description": "用户输入的ip。", "required": true, "type": "string"}]}, {"name": "search_around", "description": "提供指定地点额关键词搜索周边能力。", "parameters": [{"name": "radius", "description": "圆形区域检索半径，单位为米。", "required": false, "type": "integer"}, {"name": "keyword", "description": "检索关键字。如“美食”。", "required": false, "type": "string"}, {"name": "location", "description": "待搜索点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "required": true, "type": "string"}]}, {"name": "plan_driving", "description": "驾驶路线规划。", "parameters": [{"name": "dest_location", "description": "目的地。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "required": true, "type": "string"}, {"name": "origin_location", "description": "出发点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "required": true, "type": "string"}, {"name": "steps_info", "description": "是否下发step详情 1：下发step详情；0：不下发step详情。", "required": false, "type": "integer"}, {"name": "tactics", "description": "路线偏好。默认值：0。 可选值：0：常规路线(时间最短)；1：不走高速；2：躲避拥堵；3：最短距离；4：花费最少；5：大路优先；6：表示避开轮渡。", "required": false, "type": "integer"}, {"name": "way_points_location", "description": "途径点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "required": false, "type": "array"}]}, {"name": "search_region", "description": "提供指定区域关键词搜索的能力。", "parameters": [{"name": "keyword", "description": "检索关键字。如“美食”。", "required": false, "type": "string"}, {"name": "region", "description": "检索行政区划区域，可输入行政区划名。", "required": true, "type": "string"}]}, {"name": "geocode_to_poi", "description": "逆地理编码，将经纬度转换为详细结构化的地址。", "parameters": [{"name": "location", "description": "经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121", "required": true, "type": "string"}]}, {"name": "poi_to_geocode", "description": "地理编码，将详细的结构化地址转换为百度经纬度坐标。", "parameters": [{"name": "address", "description": "待解析的地址。可以输入两种样式的值，分别是：标准的结构化地址信息，如北京市海淀区上地十街十号。与“*路与*路交叉口”描述方式，如北一环路和阜阳路的交叉路口。", "required": true, "type": "string"}, {"name": "city", "description": "地址所在的城市名。", "required": false, "type": "string"}]}, {"name": "plan_bicycle", "description": "骑行路线规划。", "parameters": [{"name": "riding_type", "description": "骑行类型。默认0，0：普通自行车；1：电动自行车。", "required": false, "type": "string"}, {"name": "steps_info", "description": "是否下发step详情 1：下发step详情；0：不下发step详情。", "required": false, "type": "integer"}, {"name": "dest_location", "description": "目的地。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "required": true, "type": "string"}, {"name": "origin_location", "description": "出发点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "required": true, "type": "string"}]}, {"name": "plan_walking", "description": "步行路线规划。", "parameters": [{"name": "dest_location", "description": "目的地。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "required": true, "type": "string"}, {"name": "origin_location", "description": "出发点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "required": true, "type": "string"}, {"name": "steps_info", "description": "是否下发step详情 1：下发step详情；0：不下发step详情。", "required": false, "type": "integer"}]}, {"name": "plan_integrated", "description": "公共交通路线规划。", "parameters": [{"name": "dest_location", "description": "目的地。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "required": true, "type": "string"}, {"name": "origin_location", "description": "出发点。格式为经纬度，维度在前，经度在后，用英文逗号分隔；例如 123,121。也支持按照POI进行输入，如“北京南站”。", "required": true, "type": "string"}, {"name": "steps_info", "description": "是否下发step详情 1：下发step详情；0：不下发step详情。", "required": false, "type": "integer"}]}]}, {"name": "新浪财经", "id": "7356445481058189327", "tools": [{"name": "SearchStockData", "description": "根据关键词或者股票代码搜索股票信息，包括财务状况以及市场信息。例子：贵州茅台、600519", "parameters": [{"name": "date", "description": "指定日期，默认今天。格式为YYYY-MM-DD", "required": false, "type": "string"}, {"name": "keyword", "description": "股票的关键词或者股票代码，例如：茅台、腾讯、600519", "required": true, "type": "string"}]}, {"name": "GetExchangeRate", "description": "该工具提供汇率查询能力，入参中的from和to只能是货币代码", "parameters": [{"name": "date", "description": "指定日期，默认今日。格式为YYYY-MM-DD", "required": false, "type": "string"}, {"name": "from", "description": "起始货币代码，如USD、CNY", "required": true, "type": "string"}, {"name": "to", "description": "目标货币代码，如CNY、USD", "required": true, "type": "string"}]}]}, {"name": "文库搜索", "id": "7376228190244618278", "tools": [{"name": "document_search", "description": "根据关键词检索豆柴文档", "parameters": [{"name": "keyword", "description": "用以检索的关键词", "required": true, "type": "string"}, {"name": "number", "description": "获得的数据条数，默认10条，最大50条", "required": false, "type": "string"}]}]}, {"name": "猎聘", "id": "7330155407547138089", "tools": [{"name": "job_recommendation", "description": "帮助用户搜索工作招聘，基于用户的工作经验、教育经历、地理位置、薪水、职位名称、工作性质等", "parameters": [{"name": "compNature", "description": "公司性质，填国企、私企", "required": false, "type": "string"}, {"name": "companyName", "description": "公司名称", "required": false, "type": "string"}, {"name": "eduLevel", "description": "教育经历，如硕士，博士等。如果用户没提及受教育程度，此项勿填", "required": false, "type": "string"}, {"name": "salaryCap", "description": "薪水上限，必须使用数字来代表，比如：100000", "required": false, "type": "string"}, {"name": "salaryFloor", "description": "薪水下限，必须使用数字来代表，比如：50000", "required": false, "type": "string"}, {"name": "salaryKind", "description": "薪水类别，只能填“月薪”或者“年薪”。", "required": false, "type": "string"}, {"name": "address", "description": "公司位置，如北京", "required": false, "type": "string"}, {"name": "job<PERSON>ame", "description": "职位名称，如java开发", "required": false, "type": "string"}, {"name": "workExperience", "description": "工作经历，填具体年限，如2年以上", "required": false, "type": "string"}]}]}, {"name": "Notion", "id": "7328314363604025371", "tools": [{"name": "get_database_info", "description": "使用notion中数据库的id或者数据库的url查找相应的数据库。", "parameters": [{"name": "id", "description": "输入数据库id或数据库url以搜索特定的notion数据库并检索其内容", "required": true, "type": "string"}]}, {"name": "search", "description": "根据标题关键字搜索notion页面，支持模糊搜索。", "parameters": [{"name": "keyword", "description": "使用标题关键字搜索notion以找到相应的页面", "required": false, "type": "string"}, {"name": "type", "description": "通过输入\"page\" 或 \"database\"作为参数来选择页面类型", "required": true, "type": "string"}]}, {"name": "search_database_item", "description": "查询数据库数据", "parameters": [{"name": "page_size", "description": "页面大小", "required": false, "type": "integer"}, {"name": "sorts", "description": "指定组织数据的排序标准", "required": false, "type": "array"}, {"name": "database_id", "description": "notion数据库的ID或URL", "required": true, "type": "string"}, {"name": "page", "description": "页面", "required": false, "type": "integer"}]}, {"name": "create_page", "description": "创建一个新页面，用户可以在其中输入他们想要的内容", "parameters": [{"name": "parent_id", "description": "指定父页面的page_id或页面的url来控制新页面的位置", "required": true, "type": "string"}, {"name": "text", "description": "用户想要输入到页面中的文本", "required": false, "type": "string"}, {"name": "title", "description": "指定页面的标题", "required": true, "type": "string"}]}, {"name": "add_database_item", "description": "向Notion数据库添加新行数据", "parameters": [{"name": "properties", "description": "数据库的列属性", "required": true, "type": "array"}, {"name": "parent_id", "description": "输入notion数据库ID或URL以向指定数据库添加新行", "required": false, "type": "string"}]}, {"name": "create_database", "description": "在Notion中创建新数据库", "parameters": [{"name": "parent_id", "description": "指定父页面的page_id以控制新页面的位置。如果留空，新页面将在“coze space”下创建。", "required": false, "type": "string"}, {"name": "properties", "description": "数据库的列属性", "required": true, "type": "array"}, {"name": "title", "description": "指定要创建的新数据库的标题, default value is default", "required": true, "type": "string"}]}, {"name": "write_page", "description": "向特定页面添加内容", "parameters": [{"name": "page_id", "description": "指定父页面的page_id以控制新页面的位置", "required": true, "type": "string"}, {"name": "text", "description": "用户想要输入到页面中的文本", "required": true, "type": "string"}]}, {"name": "read_page", "description": "使用notion中的page_id或者页面url查找相应的页面", "parameters": [{"name": "id", "description": "输入page_id或notion页面url以搜索特定的notion页面并检索其内容", "required": true, "type": "string"}]}]}, {"name": "中文文本转语音", "id": "7361314113294712842", "tools": [{"name": "roumei_nvsheng", "description": "输入一段文本，转化为柔美女生的声音", "parameters": [{"name": "text", "description": "想要转化为语音的文本", "required": true, "type": "string"}]}, {"name": "gaoleng_yujie", "description": "将你的文本转化为高冷御姐的声音", "parameters": [{"name": "text", "description": "想要转化为语音的文本", "required": true, "type": "string"}]}, {"name": "yangguang_qingnian", "description": "将你输入的文本转化为阳光青年的声音", "parameters": [{"name": "text", "description": "用户想要转化为声音的文本", "required": true, "type": "string"}]}, {"name": "wennuan_ahu", "description": "将输入的文本转化为温柔的男声", "parameters": [{"name": "text", "description": "用户想要转化为声音的文本", "required": true, "type": "string"}]}]}, {"name": "搜狐热闻", "id": "7343894357063385100", "tools": [{"name": "top_news", "description": "帮助用户获取搜狐网上的每日热闻", "parameters": [{"name": "count", "description": "获取新闻条数", "required": true, "type": "integer"}, {"name": "q", "description": "搜索关键词", "required": false, "type": "string"}]}]}]