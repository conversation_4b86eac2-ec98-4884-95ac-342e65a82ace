# Coze 插件 API 参考文档

## 高热度插件详细API说明

### 1. Wolfram Alpha (ID: 7328314516272463923)
**热度**: 41,498 | **分类**: 实用工具 | **免费**: ✅

#### 工具: calculate
- **功能**: 逐步计算数学表达式
- **参数**:
  - `input` (string, 必填): 数学表达式，如果不是英文需先翻译
- **使用示例**: "2+3的结果"、"solve x^2 + 5x + 6 = 0"

#### 工具: query  
- **功能**: 计算数学表达式结果
- **参数**:
  - `i` (string, 可选): 数学表达式，需转成英文，不接受非数学问题
- **使用示例**: "integrate x^2 dx"、"derivative of sin(x)"

---

### 2. 语音转文本 (ID: 7342819789989052470)
**热度**: 29,562 | **分类**: 实用工具 | **免费**: ✅

#### 工具: SpeechToText
- **功能**: 读取音频URL并转为文字
- **参数**:
  - `url` (string, 必填): 音频文件URL链接
- **支持格式**: MP3, WAV, M4A等主流音频格式
- **使用示例**: "https://example.com/audio.mp3"

---

### 3. 创客贴智能设计 (ID: 7351267469065011241)
**热度**: 21,581 | **分类**: 实用工具 | **免费**: ✅

#### 工具: ckt_intelligent_design
- **功能**: 根据设计需求生成精美设计图
- **参数**:
  - `query` (string, 必填): 设计主题或需求描述
- **支持场景**: 手机海报、宣传图、电商图、店铺广告、日签、社交媒体配图
- **使用示例**: "春节促销海报"、"科技感产品宣传图"

---

### 4. 头条视频搜索 (ID: 7345693026326446080)
**热度**: 20,885 | **分类**: 新闻阅读 | **免费**: ✅

#### 工具: ToutiaoVideoSearch
- **功能**: 搜索头条平台视频内容
- **参数**:
  - `query` (string, 必填): 搜索关键词
  - `count` (string, 可选): 返回结果数量，默认5，最大20
  - `offset` (string, 可选): 跳过的结果数量，默认0
- **使用示例**: query="人工智能", count="10"

---

### 5. 板栗看板 (ID: 7358789295274049572)
**热度**: 20,280 | **分类**: 实用工具 | **免费**: ✅

#### 工具: generateTasks
- **功能**: 根据用户需求拆解任务并生成看板
- **参数**:
  - `input` (string, 必填): 用户完整输入内容，仅支持中文
- **使用示例**: "设计需求对接的看板，包括公告栏、设计需求池，设计评审、驳回、验收合格的板块"

---

### 6. 百度地图 (ID: 7394300204087787570)
**热度**: 19,432 | **分类**: 便利生活 | **免费**: ✅

#### 工具: poi_to_geocode
- **功能**: 地址转经纬度坐标
- **参数**:
  - `address` (string, 必填): 待解析地址
  - `city` (string, 可选): 地址所在城市
- **使用示例**: address="北京市海淀区上地十街十号"

#### 工具: plan_driving
- **功能**: 驾车路线规划
- **参数**:
  - `origin_location` (string, 必填): 出发点经纬度或POI名称
  - `dest_location` (string, 必填): 目的地经纬度或POI名称
  - `tactics` (integer, 可选): 路线偏好 (0:常规 1:不走高速 2:躲避拥堵)
  - `steps_info` (integer, 可选): 是否返回详细步骤 (0/1)

#### 工具: search_around
- **功能**: 周边搜索
- **参数**:
  - `location` (string, 必填): 中心点坐标或POI名称
  - `keyword` (string, 可选): 搜索关键词如"美食"
  - `radius` (integer, 可选): 搜索半径(米)

---

### 7. 新浪财经 (ID: 7356445481058189327)
**热度**: 18,911 | **分类**: 实用工具 | **免费**: ✅

#### 工具: SearchStockData
- **功能**: 搜索股票信息和财务数据
- **参数**:
  - `keyword` (string, 必填): 股票关键词或代码
  - `date` (string, 可选): 指定日期，格式YYYY-MM-DD
- **使用示例**: keyword="贵州茅台"或"600519"

#### 工具: GetExchangeRate
- **功能**: 汇率查询
- **参数**:
  - `from` (string, 必填): 起始货币代码如USD
  - `to` (string, 必填): 目标货币代码如CNY
  - `date` (string, 可选): 指定日期，格式YYYY-MM-DD

---

### 8. Notion (ID: 7328314363604025371)
**热度**: 16,518 | **分类**: 实用工具 | **免费**: ✅

#### 工具: create_page
- **功能**: 创建新页面
- **参数**:
  - `parent_id` (string, 必填): 父页面ID或URL
  - `title` (string, 必填): 页面标题
  - `text` (string, 可选): 页面内容

#### 工具: read_page
- **功能**: 读取页面内容
- **参数**:
  - `id` (string, 必填): 页面ID或URL

#### 工具: create_database
- **功能**: 创建数据库
- **参数**:
  - `title` (string, 必填): 数据库标题
  - `properties` (array, 必填): 列属性定义
    - `name` (string): 列名
    - `type` (string): 数据类型(rich_text/checkbox/number/date)

---

## 参数类型说明

### 基础数据类型
- **string**: 文本字符串
- **integer**: 整数
- **number**: 数值(包含小数)
- **boolean**: 布尔值(true/false)
- **array**: 数组/列表

### 特殊格式要求
- **经纬度**: "纬度,经度" 格式，如"39.9042,116.4074"
- **日期**: "YYYY-MM-DD" 格式，如"2025-05-30"
- **URL**: 完整的HTTP/HTTPS链接
- **货币代码**: 三位字母代码，如USD、CNY、EUR

### 常见参数模式
- **分页参数**: count/limit(数量) + offset/page(偏移)
- **搜索参数**: query/keyword(关键词) + filters(筛选条件)
- **位置参数**: location(坐标) + radius(半径)
- **时间参数**: date/time(时间点) + range(时间范围)

## 错误处理建议

### 常见错误类型
1. **参数错误**: 检查必填参数和格式
2. **权限错误**: 确认插件授权状态
3. **配额限制**: 注意调用频率限制
4. **网络超时**: 设置合理的超时时间

### 最佳实践
1. **参数验证**: 调用前验证参数格式
2. **异常捕获**: 实现完整的错误处理逻辑
3. **重试机制**: 对临时性错误进行重试
4. **降级方案**: 准备备用插件或方案

---

*本API参考基于热度>15,000的高价值插件，涵盖最常用的功能和参数说明。*
