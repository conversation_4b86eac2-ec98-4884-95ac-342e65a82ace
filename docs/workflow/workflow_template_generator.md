# 工作流模板生成器

## 🛠️ 自动化模板生成工具

基于多媒体信息聚合器的结构分析，我们提供了一套自动化的模板生成工具，可以根据用户需求快速生成完整的工作流配置文件。

## 📋 模板生成配置表

### 快速配置向导

#### 1. 基础信息配置
```yaml
workflow_basic:
  name: "我的工作流名称"
  description: "工作流功能描述"
  category: "信息聚合" # 可选: 信息聚合、内容创作、数据分析、多媒体处理
  space_id: "你的工作空间ID"
```

#### 2. 插件选择配置
```yaml
plugins_config:
  parallel_plugin_1:
    name: "头条视频搜索"
    plugin_id: "7345693026326446080"
    api_id: "7345693026326462464"
    api_name: "ToutiaoVideoSearch"
    main_param: "query"
    position_y: 250
    
  parallel_plugin_2:
    name: "搜狐热闻"
    plugin_id: "7343894357063385100" 
    api_id: "7343894357063401484"
    api_name: "SohuHotNews"
    main_param: "keyword"
    position_y: 450
    
  storage_plugin:
    name: "Notion"
    plugin_id: "7328314363604025371"
    api_id: "7328314363604041755"
    api_name: "create_page"
    config_required: ["parent_id"]
```

#### 3. LLM配置
```yaml
llm_config:
  model_name: "DeepSeek-V3-0324"
  temperature: 0.7
  max_tokens: 6144
  system_prompt: "你是一位专业的信息分析师..."
  analysis_prompt: "请基于以下收集到的信息，进行整理和分析..."
```

#### 4. 输出配置
```yaml
output_config:
  final_message: "📊 **信息聚合完成！**\n\n🎯 **查询主题**：{{topic}}\n\n🔍 **分析报告**：\n{{analysis}}"
  streaming: false
  include_storage_result: true
```

## 🔧 模板生成脚本

### Python生成器示例
```python
import json
import uuid
from datetime import datetime

class WorkflowTemplateGenerator:
    def __init__(self):
        self.base_template = self.load_base_template()
        
    def generate_workflow(self, config):
        """根据配置生成完整的工作流JSON"""
        workflow = self.base_template.copy()
        
        # 生成基础信息
        workflow["source"]["workflowId"] = self.generate_workflow_id()
        workflow["source"]["spaceId"] = config.get("space_id", "7485774234682507273")
        
        # 生成节点
        nodes = []
        nodes.append(self.generate_start_node(config))
        nodes.extend(self.generate_parallel_plugin_nodes(config))
        nodes.append(self.generate_processing_llm_node(config))
        nodes.append(self.generate_storage_node(config))
        nodes.append(self.generate_output_node(config))
        
        workflow["json"]["nodes"] = nodes
        workflow["json"]["edges"] = self.generate_edges()
        workflow["json"]["bounds"] = self.calculate_bounds(nodes)
        
        return workflow
    
    def generate_workflow_id(self):
        """生成唯一的工作流ID"""
        return str(int(datetime.now().timestamp() * 1000))
    
    def generate_start_node(self, config):
        """生成开始节点"""
        return {
            "id": "100001",
            "type": "1",
            "meta": {"position": {"x": -800, "y": 350}},
            "data": {
                "nodeMeta": {
                    "title": "开始",
                    "description": "工作流开始节点"
                },
                "inputs": {},
                "outputs": [
                    {
                        "name": "input",
                        "type": "string",
                        "description": config.get("input_description", "用户输入的查询主题")
                    }
                ]
            }
        }
    
    def generate_parallel_plugin_nodes(self, config):
        """生成并行插件节点"""
        nodes = []
        plugin_configs = config.get("plugins_config", {})
        
        # 插件1
        if "parallel_plugin_1" in plugin_configs:
            plugin1_config = plugin_configs["parallel_plugin_1"]
            nodes.append(self.create_plugin_node(
                node_id="110001",
                position={"x": -500, "y": plugin1_config.get("position_y", 250)},
                config=plugin1_config
            ))
        
        # 插件2  
        if "parallel_plugin_2" in plugin_configs:
            plugin2_config = plugin_configs["parallel_plugin_2"]
            nodes.append(self.create_plugin_node(
                node_id="120001", 
                position={"x": -500, "y": plugin2_config.get("position_y", 450)},
                config=plugin2_config
            ))
            
        return nodes
    
    def create_plugin_node(self, node_id, position, config):
        """创建插件节点"""
        return {
            "id": node_id,
            "type": "4",
            "meta": {"position": position},
            "data": {
                "nodeMeta": {
                    "title": config["name"],
                    "subtitle": f"插件:{config['name']}"
                },
                "inputs": {
                    "apiParam": [
                        {
                            "name": "apiID",
                            "input": {
                                "type": "string",
                                "value": {
                                    "type": "literal",
                                    "content": config["api_id"],
                                    "rawMeta": {"type": 1}
                                }
                            }
                        },
                        {
                            "name": "apiName",
                            "input": {
                                "type": "string", 
                                "value": {
                                    "type": "literal",
                                    "content": config["api_name"],
                                    "rawMeta": {"type": 1}
                                }
                            }
                        },
                        {
                            "name": "pluginID",
                            "input": {
                                "type": "string",
                                "value": {
                                    "type": "literal",
                                    "content": config["plugin_id"],
                                    "rawMeta": {"type": 1}
                                }
                            }
                        },
                        {
                            "name": "pluginName",
                            "input": {
                                "type": "string",
                                "value": {
                                    "type": "literal",
                                    "content": config["name"],
                                    "rawMeta": {"type": 1}
                                }
                            }
                        }
                    ],
                    "inputParameters": [
                        {
                            "name": config["main_param"],
                            "input": {
                                "type": "string",
                                "value": {
                                    "type": "ref",
                                    "content": {
                                        "source": "block-output",
                                        "blockID": "100001",
                                        "name": "input"
                                    },
                                    "rawMeta": {"type": 1}
                                }
                            }
                        }
                    ]
                },
                "outputs": [
                    {
                        "name": "output",
                        "type": "string",
                        "description": f"{config['name']}搜索结果"
                    }
                ],
                "settingOnError": {
                    "switch": True,
                    "processType": 1,
                    "timeoutMs": 180000,
                    "retryTimes": 1
                }
            }
        }
    
    def generate_processing_llm_node(self, config):
        """生成信息处理LLM节点"""
        llm_config = config.get("llm_config", {})
        
        return {
            "id": "130001",
            "type": "3", 
            "meta": {"position": {"x": -150, "y": 350}},
            "data": {
                "nodeMeta": {
                    "title": "信息整理分析",
                    "description": "整理和分析收集到的信息"
                },
                "inputs": {
                    "inputParameters": [
                        {
                            "name": "prompt",
                            "input": {
                                "type": "string",
                                "value": {
                                    "type": "literal",
                                    "content": llm_config.get("analysis_prompt", "请分析以下信息..."),
                                    "rawMeta": {"type": 1}
                                }
                            }
                        },
                        {
                            "name": "topic",
                            "input": {
                                "type": "string",
                                "value": {
                                    "type": "ref",
                                    "content": {
                                        "source": "block-output",
                                        "blockID": "100001",
                                        "name": "input"
                                    },
                                    "rawMeta": {"type": 1}
                                }
                            }
                        },
                        {
                            "name": "source_1_content",
                            "input": {
                                "type": "string",
                                "value": {
                                    "type": "ref",
                                    "content": {
                                        "source": "block-output",
                                        "blockID": "110001",
                                        "name": "output"
                                    },
                                    "rawMeta": {"type": 1}
                                }
                            }
                        },
                        {
                            "name": "source_2_content", 
                            "input": {
                                "type": "string",
                                "value": {
                                    "type": "ref",
                                    "content": {
                                        "source": "block-output",
                                        "blockID": "120001",
                                        "name": "output"
                                    },
                                    "rawMeta": {"type": 1}
                                }
                            }
                        }
                    ],
                    "llmParam": [
                        {
                            "name": "modleName",
                            "input": {
                                "type": "string",
                                "value": {
                                    "type": "literal",
                                    "content": llm_config.get("model_name", "DeepSeek-V3-0324"),
                                    "rawMeta": {"type": 1}
                                }
                            }
                        },
                        {
                            "name": "temperature",
                            "input": {
                                "type": "float",
                                "value": {
                                    "type": "literal",
                                    "content": llm_config.get("temperature", 0.7),
                                    "rawMeta": {"type": 4}
                                }
                            }
                        },
                        {
                            "name": "maxTokens",
                            "input": {
                                "type": "integer",
                                "value": {
                                    "type": "literal",
                                    "content": llm_config.get("max_tokens", 6144),
                                    "rawMeta": {"type": 2}
                                }
                            }
                        },
                        {
                            "name": "systemPrompt",
                            "input": {
                                "type": "string",
                                "value": {
                                    "type": "literal",
                                    "content": llm_config.get("system_prompt", "你是一位专业的信息分析师..."),
                                    "rawMeta": {"type": 1}
                                }
                            }
                        }
                    ]
                },
                "outputs": [
                    {
                        "name": "output",
                        "type": "string",
                        "description": "分析报告"
                    }
                ],
                "settingOnError": {
                    "switch": True,
                    "processType": 1,
                    "timeoutMs": 600000,
                    "retryTimes": 1
                }
            }
        }

# 使用示例
def create_custom_workflow():
    generator = WorkflowTemplateGenerator()
    
    config = {
        "space_id": "你的工作空间ID",
        "input_description": "用户输入的查询主题",
        "plugins_config": {
            "parallel_plugin_1": {
                "name": "头条视频搜索",
                "plugin_id": "7345693026326446080",
                "api_id": "7345693026326462464",
                "api_name": "ToutiaoVideoSearch",
                "main_param": "query",
                "position_y": 250
            },
            "parallel_plugin_2": {
                "name": "搜狐热闻",
                "plugin_id": "7343894357063385100",
                "api_id": "7343894357063401484", 
                "api_name": "SohuHotNews",
                "main_param": "keyword",
                "position_y": 450
            }
        },
        "llm_config": {
            "model_name": "DeepSeek-V3-0324",
            "temperature": 0.7,
            "max_tokens": 6144,
            "system_prompt": "你是一位专业的信息分析师...",
            "analysis_prompt": "请基于以下收集到的信息，进行整理和分析..."
        }
    }
    
    workflow_json = generator.generate_workflow(config)
    
    # 保存到文件
    with open("generated_workflow.json", "w", encoding="utf-8") as f:
        json.dump(workflow_json, f, ensure_ascii=False, indent=2)
    
    return workflow_json
```

## 🎯 预设场景模板

### 1. 市场研究工作流
```yaml
market_research_template:
  name: "市场研究分析器"
  plugins:
    - name: "头条视频搜索"
      focus: "视频内容趋势"
    - name: "搜狐热闻" 
      focus: "新闻热点分析"
  analysis_focus: "市场趋势和竞争态势分析"
  output_format: "市场研究报告"
```

### 2. 内容灵感收集工作流
```yaml
content_inspiration_template:
  name: "内容灵感聚合器"
  plugins:
    - name: "文库搜索"
      focus: "专业文档内容"
    - name: "古诗词搜索"
      focus: "文学素材"
  analysis_focus: "创意内容整理和灵感提取"
  output_format: "创意素材库"
```

### 3. 技术调研工作流
```yaml
tech_research_template:
  name: "技术调研助手"
  plugins:
    - name: "文库搜索"
      focus: "技术文档"
    - name: "头条视频搜索"
      focus: "技术教程视频"
  analysis_focus: "技术方案对比和可行性分析"
  output_format: "技术调研报告"
```

## 📱 Web界面配置工具

### HTML表单示例
```html
<!DOCTYPE html>
<html>
<head>
    <title>Coze工作流模板生成器</title>
    <style>
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; }
        .form-group input, .form-group select, .form-group textarea { 
            width: 100%; padding: 8px; 
        }
        .plugin-config { 
            border: 1px solid #ddd; 
            padding: 15px; 
            margin: 10px 0; 
        }
        .generate-btn { 
            background: #007bff; 
            color: white; 
            padding: 12px 24px; 
            border: none; 
            cursor: pointer; 
        }
    </style>
</head>
<body>
    <h1>工作流模板生成器</h1>
    
    <form id="workflowForm">
        <div class="form-group">
            <label>工作流名称:</label>
            <input type="text" name="workflow_name" required>
        </div>
        
        <div class="form-group">
            <label>工作空间ID:</label>
            <input type="text" name="space_id" required>
        </div>
        
        <div class="form-group">
            <label>工作流类型:</label>
            <select name="workflow_type">
                <option value="information_aggregation">信息聚合</option>
                <option value="content_creation">内容创作</option>
                <option value="data_analysis">数据分析</option>
            </select>
        </div>
        
        <div class="plugin-config">
            <h3>插件1配置</h3>
            <div class="form-group">
                <label>插件名称:</label>
                <select name="plugin1_name">
                    <option value="头条视频搜索">头条视频搜索</option>
                    <option value="文库搜索">文库搜索</option>
                    <option value="古诗词搜索">古诗词搜索</option>
                </select>
            </div>
        </div>
        
        <div class="plugin-config">
            <h3>插件2配置</h3>
            <div class="form-group">
                <label>插件名称:</label>
                <select name="plugin2_name">
                    <option value="搜狐热闻">搜狐热闻</option>
                    <option value="新浪财经">新浪财经</option>
                    <option value="百度地图">百度地图</option>
                </select>
            </div>
        </div>
        
        <div class="form-group">
            <label>分析重点:</label>
            <textarea name="analysis_focus" rows="3" placeholder="描述希望LLM重点分析的内容..."></textarea>
        </div>
        
        <button type="button" class="generate-btn" onclick="generateWorkflow()">
            生成工作流配置
        </button>
    </form>
    
    <div id="result" style="margin-top: 20px;"></div>
    
    <script>
        function generateWorkflow() {
            const formData = new FormData(document.getElementById('workflowForm'));
            const config = Object.fromEntries(formData);
            
            // 这里调用后端API生成工作流JSON
            fetch('/generate-workflow', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('result').innerHTML = 
                    '<h3>生成的工作流配置:</h3>' +
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>' +
                    '<button onclick="downloadConfig()">下载配置文件</button>';
            });
        }
        
        function downloadConfig() {
            // 下载生成的JSON文件
            const element = document.createElement('a');
            const file = new Blob([JSON.stringify(generatedConfig, null, 2)], 
                                 {type: 'application/json'});
            element.href = URL.createObjectURL(file);
            element.download = 'workflow_config.json';
            element.click();
        }
    </script>
</body>
</html>
```

---

*这套模板生成器基于多媒体信息聚合器的成功架构，提供了从配置到生成的完整自动化流程，可以大幅降低工作流创建的技术门槛。*
