# Coze 工作流通用模板体系

## 📋 模板抽象分析

基于五星厨师工作流的深度分析，我们抽象出了一套通用的工作流模板体系，可以大幅降低新工作流的创建难度。

### 🏗️ 结构层次分析

#### 1. 基础框架结构
```json
{
  "type": "coze-workflow-clipboard-data",
  "source": {
    "workflowId": "自动生成",
    "flowMode": 0,
    "spaceId": "用户工作空间ID",
    "isDouyin": false,
    "host": "www.coze.cn"
  },
  "json": {
    "nodes": [...],
    "edges": [...],
    "bounds": {...}
  }
}
```

#### 2. 节点类型模式
| 节点类型 | Type值 | 用途 | 出现频率 |
|----------|--------|------|----------|
| 开始节点 | 1 | 工作流入口 | 必须 |
| 大模型节点 | 3 | LLM调用 | 高频 |
| 插件节点 | 4 | 外部服务 | 高频 |
| 输出节点 | 13 | 结果输出 | 必须 |

#### 3. 数据流模式
```
线性模式: A → B → C → D
分支模式: A → B → (C || D) → E
聚合模式: (A || B) → C → D
循环模式: A → B → C ↺ B
```

## 🧩 基础模板库

### 模板1: 基础线性工作流
```json
{
  "type": "coze-workflow-clipboard-data",
  "source": {
    "workflowId": "{{WORKFLOW_ID}}",
    "flowMode": 0,
    "spaceId": "{{SPACE_ID}}",
    "isDouyin": false,
    "host": "www.coze.cn"
  },
  "json": {
    "nodes": [
      {
        "id": "100001",
        "type": "1",
        "meta": {"position": {"x": -600, "y": 300}},
        "data": {
          "nodeMeta": {"title": "开始", "description": "工作流开始节点"},
          "inputs": {},
          "outputs": [{"name": "input", "type": "string", "description": "用户输入"}]
        }
      },
      {
        "id": "110001",
        "type": "3",
        "meta": {"position": {"x": -300, "y": 280}},
        "data": {
          "nodeMeta": {"title": "{{LLM_NODE_TITLE}}", "description": "{{LLM_NODE_DESC}}"},
          "inputs": {
            "inputParameters": [
              {
                "name": "prompt",
                "input": {
                  "type": "string",
                  "value": {
                    "type": "literal",
                    "content": "{{PROMPT_TEMPLATE}}",
                    "rawMeta": {"type": 1}
                  }
                }
              }
            ],
            "llmParam": [
              {
                "name": "modleName",
                "input": {
                  "type": "string",
                  "value": {"type": "literal", "content": "{{MODEL_NAME}}", "rawMeta": {"type": 1}}
                }
              },
              {
                "name": "temperature",
                "input": {
                  "type": "float",
                  "value": {"type": "literal", "content": "{{TEMPERATURE}}", "rawMeta": {"type": 4}}
                }
              },
              {
                "name": "maxTokens",
                "input": {
                  "type": "integer",
                  "value": {"type": "literal", "content": "{{MAX_TOKENS}}", "rawMeta": {"type": 2}}
                }
              },
              {
                "name": "systemPrompt",
                "input": {
                  "type": "string",
                  "value": {"type": "literal", "content": "{{SYSTEM_PROMPT}}", "rawMeta": {"type": 1}}
                }
              }
            ]
          },
          "outputs": [{"name": "output", "type": "string", "description": "LLM输出"}],
          "settingOnError": {
            "switch": true,
            "processType": 1,
            "timeoutMs": 600000,
            "retryTimes": 1
          }
        }
      },
      {
        "id": "120001",
        "type": "13",
        "meta": {"position": {"x": 0, "y": 300}},
        "data": {
          "inputs": {
            "content": {
              "type": "string",
              "value": {"type": "literal", "content": "{{OUTPUT_TEMPLATE}}", "rawMeta": {"type": 1}}
            },
            "streamingOutput": false
          }
        }
      }
    ],
    "edges": [
      {"sourceNodeID": "100001", "targetNodeID": "110001"},
      {"sourceNodeID": "110001", "targetNodeID": "120001"}
    ],
    "bounds": {"x": -600, "y": 280, "width": 600, "height": 20}
  }
}
```

### 模板2: 并行分支工作流
```json
{
  "type": "coze-workflow-clipboard-data",
  "source": {
    "workflowId": "{{WORKFLOW_ID}}",
    "flowMode": 0,
    "spaceId": "{{SPACE_ID}}",
    "isDouyin": false,
    "host": "www.coze.cn"
  },
  "json": {
    "nodes": [
      {
        "id": "100001",
        "type": "1",
        "meta": {"position": {"x": -700, "y": 350}},
        "data": {
          "nodeMeta": {"title": "开始", "description": "工作流开始节点"},
          "outputs": [{"name": "input", "type": "string"}]
        }
      },
      {
        "id": "110001",
        "type": "3",
        "meta": {"position": {"x": -400, "y": 330}},
        "data": {
          "nodeMeta": {"title": "{{MAIN_LLM_TITLE}}", "description": "{{MAIN_LLM_DESC}}"},
          "inputs": {
            "inputParameters": [
              {
                "name": "input",
                "input": {
                  "type": "string",
                  "value": {
                    "type": "ref",
                    "content": {"source": "block-output", "blockID": "100001", "name": "input"},
                    "rawMeta": {"type": 1}
                  }
                }
              }
            ],
            "llmParam": [
              {"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "{{MODEL_NAME}}"}}},
              {"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": "{{TEMPERATURE}}"}}},
              {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "{{SYSTEM_PROMPT}}"}}}
            ]
          },
          "outputs": [{"name": "output", "type": "string"}]
        }
      },
      {
        "id": "120001",
        "type": "13",
        "meta": {"position": {"x": -100, "y": 350}},
        "data": {
          "inputs": {
            "content": {"type": "string", "value": {"type": "literal", "content": "{{PROGRESS_MESSAGE}}"}},
            "streamingOutput": true
          }
        }
      },
      {
        "id": "130001",
        "type": "4",
        "meta": {"position": {"x": 200, "y": 250}},
        "data": {
          "nodeMeta": {"title": "{{PLUGIN_A_TITLE}}", "subtitle": "{{PLUGIN_A_SUBTITLE}}"},
          "inputs": {
            "apiParam": [
              {"name": "pluginID", "input": {"type": "string", "value": {"type": "literal", "content": "{{PLUGIN_A_ID}}"}}},
              {"name": "apiName", "input": {"type": "string", "value": {"type": "literal", "content": "{{PLUGIN_A_API}}"}}}
            ],
            "inputParameters": [
              {
                "name": "{{PLUGIN_A_PARAM}}",
                "input": {
                  "type": "string",
                  "value": {
                    "type": "ref",
                    "content": {"source": "block-output", "blockID": "110001", "name": "output"}
                  }
                }
              }
            ]
          },
          "outputs": [{"name": "output", "type": "string"}]
        }
      },
      {
        "id": "140001",
        "type": "4",
        "meta": {"position": {"x": 200, "y": 450}},
        "data": {
          "nodeMeta": {"title": "{{PLUGIN_B_TITLE}}", "subtitle": "{{PLUGIN_B_SUBTITLE}}"},
          "inputs": {
            "apiParam": [
              {"name": "pluginID", "input": {"type": "string", "value": {"type": "literal", "content": "{{PLUGIN_B_ID}}"}}},
              {"name": "apiName", "input": {"type": "string", "value": {"type": "literal", "content": "{{PLUGIN_B_API}}"}}}
            ],
            "inputParameters": [
              {
                "name": "{{PLUGIN_B_PARAM}}",
                "input": {
                  "type": "string",
                  "value": {
                    "type": "ref",
                    "content": {"source": "block-output", "blockID": "110001", "name": "output"}
                  }
                }
              }
            ]
          },
          "outputs": [{"name": "output", "type": "string"}]
        }
      },
      {
        "id": "150001",
        "type": "13",
        "meta": {"position": {"x": 500, "y": 350}},
        "data": {
          "inputs": {
            "content": {
              "type": "string",
              "value": {"type": "literal", "content": "{{FINAL_OUTPUT_TEMPLATE}}"}
            },
            "inputParameters": [
              {
                "name": "result_a",
                "input": {
                  "type": "string",
                  "value": {"type": "ref", "content": {"source": "block-output", "blockID": "130001", "name": "output"}}
                }
              },
              {
                "name": "result_b",
                "input": {
                  "type": "string",
                  "value": {"type": "ref", "content": {"source": "block-output", "blockID": "140001", "name": "output"}}
                }
              }
            ],
            "streamingOutput": false
          }
        }
      }
    ],
    "edges": [
      {"sourceNodeID": "100001", "targetNodeID": "110001"},
      {"sourceNodeID": "110001", "targetNodeID": "120001"},
      {"sourceNodeID": "120001", "targetNodeID": "130001"},
      {"sourceNodeID": "120001", "targetNodeID": "140001"},
      {"sourceNodeID": "130001", "targetNodeID": "150001"},
      {"sourceNodeID": "140001", "targetNodeID": "150001"}
    ],
    "bounds": {"x": -700, "y": 250, "width": 1200, "height": 200}
  }
}
```

## 🎨 节点配置模板

### LLM节点标准配置
```json
{
  "llm_node_template": {
    "id": "{{NODE_ID}}",
    "type": "3",
    "meta": {"position": {"x": "{{X_POSITION}}", "y": "{{Y_POSITION}}"}},
    "data": {
      "nodeMeta": {
        "title": "{{NODE_TITLE}}",
        "description": "{{NODE_DESCRIPTION}}"
      },
      "inputs": {
        "inputParameters": [
          {
            "name": "prompt",
            "input": {
              "type": "string",
              "value": {
                "type": "literal",
                "content": "{{PROMPT_CONTENT}}",
                "rawMeta": {"type": 1}
              }
            }
          }
        ],
        "llmParam": [
          {
            "name": "modleName",
            "input": {
              "type": "string",
              "value": {"type": "literal", "content": "DeepSeek-V3-0324", "rawMeta": {"type": 1}}
            }
          },
          {
            "name": "temperature",
            "input": {
              "type": "float",
              "value": {"type": "literal", "content": 0.8, "rawMeta": {"type": 4}}
            }
          },
          {
            "name": "maxTokens",
            "input": {
              "type": "integer",
              "value": {"type": "literal", "content": 4096, "rawMeta": {"type": 2}}
            }
          },
          {
            "name": "systemPrompt",
            "input": {
              "type": "string",
              "value": {"type": "literal", "content": "{{SYSTEM_PROMPT}}", "rawMeta": {"type": 1}}
            }
          },
          {
            "name": "enableChatHistory",
            "input": {
              "type": "boolean",
              "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}
            }
          }
        ]
      },
      "outputs": [{"name": "output", "type": "string", "description": "LLM输出结果"}],
      "settingOnError": {
        "switch": true,
        "processType": 1,
        "timeoutMs": 600000,
        "retryTimes": 1
      }
    }
  }
}
```

### 插件节点标准配置
```json
{
  "plugin_node_template": {
    "id": "{{NODE_ID}}",
    "type": "4",
    "meta": {"position": {"x": "{{X_POSITION}}", "y": "{{Y_POSITION}}"}},
    "data": {
      "nodeMeta": {
        "title": "{{PLUGIN_TITLE}}",
        "subtitle": "{{PLUGIN_SUBTITLE}}"
      },
      "inputs": {
        "apiParam": [
          {"name": "apiID", "input": {"type": "string", "value": {"type": "literal", "content": "{{API_ID}}"}}},
          {"name": "apiName", "input": {"type": "string", "value": {"type": "literal", "content": "{{API_NAME}}"}}},
          {"name": "pluginID", "input": {"type": "string", "value": {"type": "literal", "content": "{{PLUGIN_ID}}"}}},
          {"name": "pluginName", "input": {"type": "string", "value": {"type": "literal", "content": "{{PLUGIN_NAME}}"}}}
        ],
        "inputDefs": [
          {
            "description": "{{PARAM_DESCRIPTION}}",
            "name": "{{PARAM_NAME}}",
            "required": true,
            "type": "string"
          }
        ],
        "inputParameters": [
          {
            "name": "{{PARAM_NAME}}",
            "input": {
              "type": "string",
              "value": {
                "type": "ref",
                "content": {"source": "block-output", "blockID": "{{SOURCE_NODE_ID}}", "name": "output"}
              }
            }
          }
        ]
      },
      "outputs": [{"name": "output", "type": "string", "description": "插件输出结果"}],
      "settingOnError": {
        "switch": true,
        "processType": 1,
        "timeoutMs": 180000,
        "retryTimes": 1
      }
    }
  }
}
```

### 输出节点标准配置
```json
{
  "output_node_template": {
    "id": "{{NODE_ID}}",
    "type": "13",
    "meta": {"position": {"x": "{{X_POSITION}}", "y": "{{Y_POSITION}}"}},
    "data": {
      "inputs": {
        "content": {
          "type": "string",
          "value": {"type": "literal", "content": "{{OUTPUT_CONTENT}}", "rawMeta": {"type": 1}}
        },
        "inputParameters": [
          {
            "name": "{{VARIABLE_NAME}}",
            "input": {
              "type": "string",
              "value": {
                "type": "ref",
                "content": {"source": "block-output", "blockID": "{{SOURCE_NODE_ID}}", "name": "output"}
              }
            }
          }
        ],
        "streamingOutput": false,
        "callTransferVoice": false,
        "chatHistoryWriting": "historyWrite"
      }
    }
  }
}
```

## 🔧 配置参数模板

### 常用LLM参数配置
```json
{
  "creative_content": {
    "temperature": 0.8,
    "maxTokens": 8192,
    "model": "DeepSeek-V3-0324"
  },
  "factual_content": {
    "temperature": 0.3,
    "maxTokens": 4096,
    "model": "Claude-3"
  },
  "analysis_content": {
    "temperature": 0.7,
    "maxTokens": 6144,
    "model": "DeepSeek-V3-0324"
  }
}
```

### 高性能插件配置
```json
{
  "wolfram_alpha": {
    "pluginID": "7328314516272463923",
    "apiName": "calculate",
    "success_rate": "97.55%",
    "use_case": "数学计算、数据验证"
  },
  "ckt_design": {
    "pluginID": "7351267469065011241",
    "apiName": "ckt_intelligent_design",
    "success_rate": "97.697%",
    "use_case": "图片设计、配图生成"
  },
  "speech_to_text": {
    "pluginID": "7342819789989052470",
    "apiName": "SpeechToText",
    "success_rate": "99.997%",
    "use_case": "音频转录"
  }
}
```

## 📐 布局模板

### 节点位置计算规则
```javascript
// 水平布局计算
function calculateHorizontalLayout(nodeCount, startX = -600, spacing = 350) {
  const positions = [];
  for (let i = 0; i < nodeCount; i++) {
    positions.push({
      x: startX + (i * spacing),
      y: 300
    });
  }
  return positions;
}

// 并行分支布局计算
function calculateParallelLayout(mainNodes, branchNodes, startX = -600) {
  const layout = {
    main: [],
    branches: []
  };
  
  // 主线节点
  for (let i = 0; i < mainNodes; i++) {
    layout.main.push({
      x: startX + (i * 350),
      y: 300
    });
  }
  
  // 分支节点
  const branchStartX = layout.main[layout.main.length - 1].x + 350;
  const branchSpacing = 200;
  const centerY = 300;
  
  for (let i = 0; i < branchNodes; i++) {
    layout.branches.push({
      x: branchStartX,
      y: centerY + ((i - (branchNodes - 1) / 2) * branchSpacing)
    });
  }
  
  return layout;
}
```

### 边界计算模板
```javascript
function calculateBounds(nodes) {
  const positions = nodes.map(node => node.meta.position);
  const minX = Math.min(...positions.map(p => p.x)) - 180; // 节点宽度一半
  const maxX = Math.max(...positions.map(p => p.x)) + 180;
  const minY = Math.min(...positions.map(p => p.y)) - 100;
  const maxY = Math.max(...positions.map(p => p.y)) + 100;
  
  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY
  };
}
```

## 🎯 场景化模板

### 内容创作类模板
```json
{
  "content_creation_template": {
    "description": "适用于文章、文案、创意内容生成",
    "nodes": [
      {"type": "start", "title": "主题输入"},
      {"type": "llm", "title": "内容生成", "config": "creative_content"},
      {"type": "plugin", "title": "数据验证", "plugin": "wolfram_alpha"},
      {"type": "plugin", "title": "配图生成", "plugin": "ckt_design"},
      {"type": "output", "title": "最终输出"}
    ],
    "flow": "linear_with_parallel_branches"
  }
}
```

### 信息处理类模板
```json
{
  "information_processing_template": {
    "description": "适用于信息收集、分析、整理",
    "nodes": [
      {"type": "start", "title": "查询输入"},
      {"type": "plugin", "title": "信息搜索A", "plugin": "search_plugin_a"},
      {"type": "plugin", "title": "信息搜索B", "plugin": "search_plugin_b"},
      {"type": "llm", "title": "信息整理", "config": "analysis_content"},
      {"type": "plugin", "title": "结果存储", "plugin": "notion"},
      {"type": "output", "title": "分析报告"}
    ],
    "flow": "parallel_to_convergence"
  }
}
```

### 多媒体处理类模板
```json
{
  "multimedia_processing_template": {
    "description": "适用于音频、图片、视频处理",
    "nodes": [
      {"type": "start", "title": "媒体输入"},
      {"type": "plugin", "title": "音频转录", "plugin": "speech_to_text"},
      {"type": "llm", "title": "内容分析", "config": "factual_content"},
      {"type": "plugin", "title": "图片生成", "plugin": "ckt_design"},
      {"type": "output", "title": "处理结果"}
    ],
    "flow": "linear_processing"
  }
}
```

---

*这套模板体系基于五星厨师工作流的成功模式抽象而来，提供了从基础结构到完整场景的全套模板，可以大幅降低工作流创建的复杂度和时间成本。*
