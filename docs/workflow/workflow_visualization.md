# 五星厨师工作流可视化结构说明

## 🎨 工作流整体布局

### 画布概览
```
总尺寸: 2586.65 × 323.86 像素
布局方向: 水平流向 (从左到右)
分支结构: 中后段并行分支
节点总数: 7个
连接总数: 6个
```

### 坐标系统
- **原点**: 画布左上角 (0, 0)
- **X轴**: 向右为正方向
- **Y轴**: 向下为正方向
- **单位**: 像素

## 📍 节点位置分布

### 节点坐标表
| 节点ID | 节点名称 | X坐标 | Y坐标 | 功能描述 |
|--------|----------|-------|-------|----------|
| 117173 | 大模型 | -692.48 | 256.12 | 菜谱内容生成 |
| 144087 | 输出 | -265.37 | 282.12 | 中间结果输出 |
| 159465 | 大模型_1 | 180.22 | 256.12 | HTML格式转换 |
| 114757 | 输出_1 | 597.10 | 282.12 | 分支控制点 |
| 122007 | html2url | 1317.22 | 202.34 | URL生成服务 |
| 183892 | gen_img | 1061.07 | 387.51 | 图片生成服务 |
| 188604 | 预览图片 | 1534.17 | 399.51 | 图片预览输出 |

### 可视化布局图
```
                                                    ┌─────────────┐
                                                    │  html2url   │
                                                    │ (1317, 202) │
                                                    └─────────────┘
                                                           ↑
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   大模型    │ →  │    输出     │ →  │  大模型_1   │ →  │   输出_1    │
│ (-692, 256) │    │ (-265, 282) │    │ (180, 256)  │    │ (597, 282)  │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                                                                  ↓
                                                    ┌─────────────┐    ┌─────────────┐
                                                    │   gen_img   │ →  │  预览图片   │
                                                    │ (1061, 387) │    │ (1534, 399) │
                                                    └─────────────┘    └─────────────┘
```

## 🔄 数据流向分析

### 主要执行路径

#### 路径1: 主干流程
```
用户输入 → 大模型(117173) → 输出(144087) → 大模型_1(159465) → 输出_1(114757)
```

#### 路径2: URL生成分支
```
输出_1(114757) → html2url(122007)
```

#### 路径3: 图片生成分支
```
输出_1(114757) → gen_img(183892) → 预览图片(188604)
```

### 连接关系详解

#### 串行连接 (Sequential)
| 源节点 | 目标节点 | 数据类型 | 传递内容 |
|--------|----------|----------|----------|
| 117173 | 144087 | string | 生成的菜谱文本 |
| 144087 | 159465 | string | 菜谱文本 |
| 159465 | 114757 | string | HTML格式的菜谱 |
| 183892 | 188604 | string | 图片URL |

#### 并行分支 (Parallel)
| 源节点 | 目标节点 | 分支类型 | 处理内容 |
|--------|----------|----------|----------|
| 114757 | 122007 | 分支A | HTML → URL |
| 114757 | 183892 | 分支B | HTML → 图片 |

## 🎯 节点功能详解

### 输入层 (Input Layer)
```
┌─────────────────────────────────────┐
│              用户输入                │
│         (菜名: string)              │
└─────────────────────────────────────┘
                    ↓
```

### 处理层 (Processing Layer)

#### 第一阶段: 内容生成
```
┌─────────────────────────────────────┐
│            大模型 (117173)           │
│  ┌─────────────────────────────────┐ │
│  │ 系统提示词: 五星厨师角色        │ │
│  │ 用户提示词: {{input}}           │ │
│  │ 模型: DeepSeek-V3-0324          │ │
│  │ 温度: 0.8                       │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────┐
│            输出 (144087)             │
│  ┌─────────────────────────────────┐ │
│  │ 内容: {{output}} + 进度提示     │ │
│  │ 流式输出: true                  │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 第二阶段: 格式转换
```
┌─────────────────────────────────────┐
│          大模型_1 (159465)           │
│  ┌─────────────────────────────────┐ │
│  │ 系统提示词: HTML转换助手        │ │
│  │ 用户提示词: {{input}}           │ │
│  │ 功能: 文本 → HTML               │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────┐
│           输出_1 (114757)            │
│  ┌─────────────────────────────────┐ │
│  │ 内容: "开始制作卡片"            │ │
│  │ 流式输出: false                 │ │
│  │ 功能: 分支控制点                │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 输出层 (Output Layer)

#### 分支A: URL生成
```
┌─────────────────────────────────────┐
│          html2url (122007)           │
│  ┌─────────────────────────────────┐ │
│  │ 输入: HTML内容                  │ │
│  │ 功能: 生成可访问URL             │ │
│  │ 输出: url, message              │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 分支B: 图片生成
```
┌─────────────────────────────────────┐
│           gen_img (183892)           │
│  ┌─────────────────────────────────┐ │
│  │ 输入: HTML内容                  │ │
│  │ 功能: HTML → 图片               │ │
│  │ 输出: image_url, message        │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────┐
│          预览图片 (188604)           │
│  ┌─────────────────────────────────┐ │
│  │ 内容: ![]({{output}})           │ │
│  │ 功能: 图片预览显示              │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## ⏱️ 执行时序图

### 时间轴分析
```
时间轴: 0s ────────── 30s ────────── 60s ────────── 90s ────────── 120s

阶段1:  [大模型生成菜谱内容]
        ├─ 0-30s: LLM处理用户输入
        └─ 30s: 输出菜谱文本

阶段2:  [HTML格式转换]
        ├─ 30-50s: LLM转换为HTML格式
        └─ 50s: 输出HTML内容

阶段3:  [并行处理]
        ├─ 分支A: [50-65s] html2url生成URL
        └─ 分支B: [50-80s] gen_img生成图片
                   └─ [80-85s] 预览图片输出

总耗时: 约85秒 (理想情况)
```

### 关键时间节点
| 时间点 | 事件 | 用户反馈 |
|--------|------|----------|
| 0s | 开始执行 | 接收用户输入 |
| 30s | 菜谱生成完成 | "内容生成完毕，开始制作卡片..." |
| 50s | HTML转换完成 | "开始制作卡片" |
| 65s | URL生成完成 | 可访问的网页链接 |
| 85s | 图片生成完成 | 菜谱预览图片 |

## 🔀 分支处理机制

### 并行分支设计
```
                    输出_1 (分发节点)
                         │
                    ┌────┴────┐
                    ↓         ↓
              html2url    gen_img
                 │           │
                 ↓           ↓
            URL输出      图片预览
```

### 分支特点
- **独立执行**: 两个分支可以并行处理
- **无依赖**: 分支间没有数据依赖关系
- **容错性**: 一个分支失败不影响另一个分支
- **性能优化**: 减少总体执行时间

## 🎨 视觉设计元素

### 节点样式
```
┌─────────────────────────────────────┐
│  🤖 大模型                          │  ← 图标 + 标题
│  ┌─────────────────────────────────┐ │
│  │ 调用大语言模型,使用变量和提示词 │ │  ← 描述文字
│  │ 生成回复                        │ │
│  └─────────────────────────────────┘ │
│  📥 输入: input (string)            │  ← 输入参数
│  📤 输出: output (string)           │  ← 输出参数
└─────────────────────────────────────┘
```

### 连接线样式
```
节点A ────────────────────────────────→ 节点B
      ↑                                ↑
   实线箭头                         目标节点
   表示数据流向
```

### 颜色编码
- **蓝色 (#5C62FF)**: LLM节点和输出节点
- **紫色 (#CA61FF)**: 插件节点
- **灰色**: 连接线
- **绿色**: 成功状态
- **红色**: 错误状态

## 📱 响应式布局

### 不同屏幕尺寸适配
```
桌面端 (>1200px):
┌─────┐ → ┌─────┐ → ┌─────┐ → ┌─────┐ ┬→ ┌─────┐
│ 节点1│   │ 节点2│   │ 节点3│   │ 节点4│ │  │ 节点5│
└─────┘   └─────┘   └─────┘   └─────┘ └→ ┌─────┐
                                         │ 节点6│
                                         └─────┘

平板端 (768-1200px):
┌─────┐ → ┌─────┐
│ 节点1│   │ 节点2│
└─────┘   └─────┘
    ↓         ↓
┌─────┐ → ┌─────┐
│ 节点3│   │ 节点4│
└─────┘   └─────┘

手机端 (<768px):
┌─────┐
│ 节点1│
└─────┘
    ↓
┌─────┐
│ 节点2│
└─────┘
    ↓
  ...
```

## 🔧 交互设计

### 节点交互
- **点击**: 查看节点详细配置
- **拖拽**: 调整节点位置
- **连接**: 拖拽创建节点间连接
- **删除**: 右键菜单删除节点

### 连接交互
- **点击**: 查看连接属性
- **拖拽**: 调整连接路径
- **删除**: 选中后按Delete键

### 画布交互
- **缩放**: 鼠标滚轮或手势缩放
- **平移**: 拖拽画布移动视图
- **框选**: 拖拽选择多个节点
- **右键**: 显示上下文菜单

## 📊 性能可视化

### 执行状态指示
```
⏳ 等待执行    🔄 正在执行    ✅ 执行成功    ❌ 执行失败
```

### 进度条显示
```
节点执行进度:
[████████████████████████████████████████] 100%

整体工作流进度:
[██████████████████████                  ] 65%
```

### 实时监控
- **执行时间**: 显示每个节点的执行耗时
- **资源使用**: 显示Token消耗和API调用次数
- **错误信息**: 实时显示错误和警告信息

---

*本可视化说明文档详细描述了五星厨师工作流的视觉结构、数据流向和交互设计，帮助用户更好地理解和使用工作流。*
