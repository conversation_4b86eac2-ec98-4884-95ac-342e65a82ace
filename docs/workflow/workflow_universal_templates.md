# 基于多媒体信息聚合器的通用工作流模板

## 🎯 模板抽象分析

基于"多媒体信息聚合器"工作流的结构分析，我们抽象出了一套通用的模板体系，可以大幅降低新工作流的创建难度。

### 📊 结构特点分析

#### 工作流架构模式
```
并行收集 → 信息聚合 → 结果存储 → 最终输出
```

#### 节点分布特点
- **开始节点**: 1个 (用户输入)
- **并行插件节点**: 2个 (多源信息收集)
- **LLM处理节点**: 1个 (信息整理分析)
- **存储节点**: 1个 (结果持久化)
- **输出节点**: 1个 (最终展示)

#### 数据流模式
```
输入 ┬→ 插件A (视频搜索)
     └→ 插件B (新闻搜索)
              ↓
         LLM整理分析
              ↓
         Notion存储
              ↓
          最终输出
```

## 🧩 通用模板框架

### 基础结构模板
```json
{
  "type": "coze-workflow-clipboard-data",
  "source": {
    "workflowId": "{{WORKFLOW_ID}}",
    "flowMode": 0,
    "spaceId": "{{SPACE_ID}}",
    "isDouyin": false,
    "host": "www.coze.cn"
  },
  "json": {
    "nodes": [
      "{{START_NODE}}",
      "{{PARALLEL_PLUGIN_NODES}}",
      "{{PROCESSING_LLM_NODE}}",
      "{{STORAGE_NODE}}",
      "{{OUTPUT_NODE}}"
    ],
    "edges": [
      "{{PARALLEL_CONNECTIONS}}",
      "{{CONVERGENCE_CONNECTIONS}}",
      "{{LINEAR_CONNECTIONS}}"
    ],
    "bounds": "{{AUTO_CALCULATED}}"
  }
}
```

### 节点ID生成规则
```javascript
// 标准节点ID分配
const NODE_ID_MAPPING = {
  start: "100001",
  parallel_plugin_1: "110001", 
  parallel_plugin_2: "120001",
  processing_llm: "130001",
  storage_plugin: "140001",
  final_output: "150001"
};

// 位置计算规则
const POSITION_LAYOUT = {
  start: { x: -800, y: 350 },
  parallel_1: { x: -500, y: 250 },
  parallel_2: { x: -500, y: 450 },
  processing: { x: -150, y: 350 },
  storage: { x: 200, y: 350 },
  output: { x: 550, y: 350 }
};
```

## 🔧 节点配置模板

### 1. 开始节点模板
```json
{
  "id": "100001",
  "type": "1",
  "meta": {
    "position": {"x": -800, "y": 350}
  },
  "data": {
    "nodeMeta": {
      "title": "开始",
      "description": "工作流开始节点"
    },
    "inputs": {},
    "outputs": [
      {
        "name": "input",
        "type": "string", 
        "description": "{{INPUT_DESCRIPTION}}"
      }
    ]
  }
}
```

### 2. 并行插件节点模板
```json
{
  "parallel_plugin_template": {
    "id": "{{PLUGIN_NODE_ID}}",
    "type": "4",
    "meta": {
      "position": {"x": -500, "y": "{{Y_POSITION}}"}
    },
    "data": {
      "nodeMeta": {
        "title": "{{PLUGIN_TITLE}}",
        "subtitle": "插件:{{PLUGIN_NAME}}"
      },
      "inputs": {
        "apiParam": [
          {
            "name": "apiID",
            "input": {
              "type": "string",
              "value": {
                "type": "literal",
                "content": "{{API_ID}}",
                "rawMeta": {"type": 1}
              }
            }
          },
          {
            "name": "apiName", 
            "input": {
              "type": "string",
              "value": {
                "type": "literal",
                "content": "{{API_NAME}}",
                "rawMeta": {"type": 1}
              }
            }
          },
          {
            "name": "pluginID",
            "input": {
              "type": "string",
              "value": {
                "type": "literal",
                "content": "{{PLUGIN_ID}}",
                "rawMeta": {"type": 1}
              }
            }
          },
          {
            "name": "pluginName",
            "input": {
              "type": "string",
              "value": {
                "type": "literal",
                "content": "{{PLUGIN_NAME}}",
                "rawMeta": {"type": 1}
              }
            }
          }
        ],
        "inputDefs": "{{PLUGIN_INPUT_DEFINITIONS}}",
        "inputParameters": [
          {
            "name": "{{MAIN_PARAM_NAME}}",
            "input": {
              "type": "string",
              "value": {
                "type": "ref",
                "content": {
                  "source": "block-output",
                  "blockID": "100001",
                  "name": "input"
                },
                "rawMeta": {"type": 1}
              }
            }
          }
        ]
      },
      "outputs": [
        {
          "name": "output",
          "type": "string",
          "description": "{{OUTPUT_DESCRIPTION}}"
        }
      ],
      "settingOnError": {
        "switch": true,
        "processType": 1,
        "timeoutMs": 180000,
        "retryTimes": 1
      }
    }
  }
}
```

### 3. 信息处理LLM节点模板
```json
{
  "processing_llm_template": {
    "id": "130001",
    "type": "3",
    "meta": {
      "position": {"x": -150, "y": 350}
    },
    "data": {
      "nodeMeta": {
        "title": "{{PROCESSING_TITLE}}",
        "description": "{{PROCESSING_DESCRIPTION}}"
      },
      "inputs": {
        "inputParameters": [
          {
            "name": "prompt",
            "input": {
              "type": "string",
              "value": {
                "type": "literal",
                "content": "{{ANALYSIS_PROMPT_TEMPLATE}}",
                "rawMeta": {"type": 1}
              }
            }
          },
          {
            "name": "topic",
            "input": {
              "type": "string",
              "value": {
                "type": "ref",
                "content": {
                  "source": "block-output",
                  "blockID": "100001",
                  "name": "input"
                },
                "rawMeta": {"type": 1}
              }
            }
          },
          {
            "name": "source_1_content",
            "input": {
              "type": "string",
              "value": {
                "type": "ref",
                "content": {
                  "source": "block-output",
                  "blockID": "110001",
                  "name": "output"
                },
                "rawMeta": {"type": 1}
              }
            }
          },
          {
            "name": "source_2_content",
            "input": {
              "type": "string",
              "value": {
                "type": "ref",
                "content": {
                  "source": "block-output",
                  "blockID": "120001",
                  "name": "output"
                },
                "rawMeta": {"type": 1}
              }
            }
          }
        ],
        "llmParam": [
          {
            "name": "modleName",
            "input": {
              "type": "string",
              "value": {
                "type": "literal",
                "content": "DeepSeek-V3-0324",
                "rawMeta": {"type": 1}
              }
            }
          },
          {
            "name": "temperature",
            "input": {
              "type": "float",
              "value": {
                "type": "literal",
                "content": 0.7,
                "rawMeta": {"type": 4}
              }
            }
          },
          {
            "name": "maxTokens",
            "input": {
              "type": "integer",
              "value": {
                "type": "literal",
                "content": 6144,
                "rawMeta": {"type": 2}
              }
            }
          },
          {
            "name": "systemPrompt",
            "input": {
              "type": "string",
              "value": {
                "type": "literal",
                "content": "{{SYSTEM_PROMPT_TEMPLATE}}",
                "rawMeta": {"type": 1}
              }
            }
          }
        ]
      },
      "outputs": [
        {
          "name": "output",
          "type": "string",
          "description": "分析报告"
        }
      ],
      "settingOnError": {
        "switch": true,
        "processType": 1,
        "timeoutMs": 600000,
        "retryTimes": 1
      }
    }
  }
}
```

### 4. 存储节点模板
```json
{
  "storage_node_template": {
    "id": "140001",
    "type": "4",
    "meta": {
      "position": {"x": 200, "y": 350}
    },
    "data": {
      "nodeMeta": {
        "title": "{{STORAGE_TITLE}}",
        "subtitle": "插件:{{STORAGE_PLUGIN_NAME}}"
      },
      "inputs": {
        "apiParam": [
          {
            "name": "pluginID",
            "input": {
              "type": "string",
              "value": {
                "type": "literal",
                "content": "{{STORAGE_PLUGIN_ID}}",
                "rawMeta": {"type": 1}
              }
            }
          }
        ],
        "inputParameters": [
          {
            "name": "title",
            "input": {
              "type": "string",
              "value": {
                "type": "literal",
                "content": "{{STORAGE_TITLE_TEMPLATE}}",
                "rawMeta": {"type": 1}
              }
            }
          },
          {
            "name": "content",
            "input": {
              "type": "string",
              "value": {
                "type": "ref",
                "content": {
                  "source": "block-output",
                  "blockID": "130001",
                  "name": "output"
                },
                "rawMeta": {"type": 1}
              }
            }
          }
        ]
      },
      "outputs": [
        {
          "name": "output",
          "type": "string",
          "description": "存储结果"
        }
      ],
      "settingOnError": {
        "switch": true,
        "processType": 1,
        "timeoutMs": 180000,
        "retryTimes": 1
      }
    }
  }
}
```

### 5. 最终输出节点模板
```json
{
  "final_output_template": {
    "id": "150001",
    "type": "13",
    "meta": {
      "position": {"x": 550, "y": 350}
    },
    "data": {
      "inputs": {
        "content": {
          "type": "string",
          "value": {
            "type": "literal",
            "content": "{{FINAL_OUTPUT_TEMPLATE}}",
            "rawMeta": {"type": 1}
          }
        },
        "inputParameters": [
          {
            "name": "topic",
            "input": {
              "type": "string",
              "value": {
                "type": "ref",
                "content": {
                  "source": "block-output",
                  "blockID": "100001",
                  "name": "input"
                }
              }
            }
          },
          {
            "name": "analysis",
            "input": {
              "type": "string",
              "value": {
                "type": "ref",
                "content": {
                  "source": "block-output",
                  "blockID": "130001",
                  "name": "output"
                }
              }
            }
          },
          {
            "name": "storage_result",
            "input": {
              "type": "string",
              "value": {
                "type": "ref",
                "content": {
                  "source": "block-output",
                  "blockID": "140001",
                  "name": "output"
                }
              }
            }
          }
        ],
        "streamingOutput": false,
        "callTransferVoice": false,
        "chatHistoryWriting": "historyWrite"
      }
    }
  }
}
```

## 🔗 连接关系模板

### 边连接配置
```json
{
  "edges_template": [
    {
      "sourceNodeID": "100001",
      "targetNodeID": "110001",
      "description": "输入到并行插件1"
    },
    {
      "sourceNodeID": "100001", 
      "targetNodeID": "120001",
      "description": "输入到并行插件2"
    },
    {
      "sourceNodeID": "110001",
      "targetNodeID": "130001",
      "description": "插件1结果到LLM处理"
    },
    {
      "sourceNodeID": "120001",
      "targetNodeID": "130001", 
      "description": "插件2结果到LLM处理"
    },
    {
      "sourceNodeID": "130001",
      "targetNodeID": "140001",
      "description": "LLM结果到存储"
    },
    {
      "sourceNodeID": "140001",
      "targetNodeID": "150001",
      "description": "存储结果到最终输出"
    }
  ]
}
```

### 边界计算模板
```json
{
  "bounds_template": {
    "x": -800,
    "y": 250,
    "width": 1350,
    "height": 200
  }
}
```

## 📝 配置变量模板

### 基础配置变量
```json
{
  "workflow_config": {
    "WORKFLOW_ID": "自动生成或手动指定",
    "SPACE_ID": "用户工作空间ID",
    "INPUT_DESCRIPTION": "用户输入的查询主题",
    "PROCESSING_TITLE": "信息整理分析",
    "PROCESSING_DESCRIPTION": "整理和分析收集到的信息"
  }
}
```

### 插件配置变量
```json
{
  "plugin_configs": {
    "plugin_1": {
      "PLUGIN_ID": "7345693026326446080",
      "API_ID": "7345693026326462464", 
      "API_NAME": "ToutiaoVideoSearch",
      "PLUGIN_NAME": "头条视频搜索",
      "PLUGIN_TITLE": "视频内容搜索",
      "Y_POSITION": 250
    },
    "plugin_2": {
      "PLUGIN_ID": "7343894357063385100",
      "API_ID": "7343894357063401484",
      "API_NAME": "SohuHotNews", 
      "PLUGIN_NAME": "搜狐热闻",
      "PLUGIN_TITLE": "新闻资讯搜索",
      "Y_POSITION": 450
    },
    "storage_plugin": {
      "STORAGE_PLUGIN_ID": "7328314363604025371",
      "STORAGE_PLUGIN_NAME": "Notion",
      "STORAGE_TITLE": "知识库存储"
    }
  }
}
```

### 提示词模板变量
```json
{
  "prompt_templates": {
    "ANALYSIS_PROMPT_TEMPLATE": "请基于以下收集到的信息，进行整理和分析：\n\n**查询主题**：{{topic}}\n\n**来源1内容**：\n{{source_1_content}}\n\n**来源2内容**：\n{{source_2_content}}\n\n请提供：\n1. 信息摘要和关键点\n2. 趋势分析和见解\n3. 相关建议和行动建议\n4. 信息来源的可靠性评估",
    
    "SYSTEM_PROMPT_TEMPLATE": "你是一位专业的信息分析师，擅长从多源信息中提取关键洞察，进行趋势分析和提供实用建议。请以客观、专业的角度分析信息，提供有价值的见解。",
    
    "FINAL_OUTPUT_TEMPLATE": "📊 **{{WORKFLOW_TYPE}}完成！**\n\n🎯 **查询主题**：{{topic}}\n\n🔍 **深度分析报告**：\n{{analysis}}\n\n💾 **存储结果**：\n{{storage_result}}\n\n✨ 信息已成功处理并保存！"
  }
}
```

## 🎨 场景化应用模板

### 1. 信息聚合类工作流
```json
{
  "information_aggregation": {
    "workflow_type": "信息聚合",
    "parallel_plugins": ["搜索插件A", "搜索插件B"],
    "processing_focus": "信息整理和趋势分析",
    "storage_type": "知识库存储",
    "use_cases": ["市场研究", "竞品分析", "行业调研"]
  }
}
```

### 2. 内容收集类工作流  
```json
{
  "content_collection": {
    "workflow_type": "内容收集",
    "parallel_plugins": ["内容源A", "内容源B"],
    "processing_focus": "内容筛选和分类",
    "storage_type": "内容库存储", 
    "use_cases": ["素材收集", "灵感整理", "资料归档"]
  }
}
```

### 3. 数据分析类工作流
```json
{
  "data_analysis": {
    "workflow_type": "数据分析",
    "parallel_plugins": ["数据源A", "数据源B"],
    "processing_focus": "数据清洗和分析",
    "storage_type": "分析报告存储",
    "use_cases": ["业务分析", "用户研究", "效果评估"]
  }
}
```

---

*这套通用模板基于多媒体信息聚合器的成功架构抽象而来，提供了完整的并行处理→信息聚合→结果存储的工作流模式，可以快速适配各种信息处理场景。*
