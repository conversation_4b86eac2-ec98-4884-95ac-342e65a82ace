# Coze 工作流节点类型参考手册

## 节点类型概览

基于"五星厨师"工作流分析，Coze平台支持以下主要节点类型：

| 节点类型 | 类型编码 | 功能描述 | 使用场景 |
|----------|----------|----------|----------|
| 大语言模型 | 3 | LLM内容生成 | 文本创作、对话、分析 |
| 插件调用 | 4 | 外部服务集成 | API调用、第三方功能 |
| 输出节点 | 13 | 消息输出控制 | 结果展示、流程控制 |

## 详细节点配置

### 🤖 大语言模型节点 (Type 3)

#### 基本结构
```json
{
  "id": "节点ID",
  "type": "3",
  "data": {
    "nodeMeta": {
      "title": "大模型",
      "description": "调用大语言模型,使用变量和提示词生成回复"
    },
    "inputs": {
      "inputParameters": [...],
      "llmParam": [...]
    },
    "outputs": [...]
  }
}
```

#### 核心参数配置

##### LLM参数 (llmParam)
| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `modleName` | string | "DeepSeek-V3-0324" | 模型名称 |
| `modelType` | integer | 1742989917 | 模型类型编码 |
| `temperature` | float | 0.8 | 创造性控制 (0.0-2.0) |
| `maxTokens` | integer | 16384 | 最大输出长度 |
| `responseFormat` | integer | 2 | 响应格式类型 |
| `enableChatHistory` | boolean | false | 是否启用对话历史 |
| `chatHistoryRound` | integer | 3 | 对话历史轮数 |

##### 提示词配置
```json
{
  "name": "prompt",
  "input": {
    "type": "string",
    "value": {
      "type": "literal",
      "content": "**用户输入**：{{input}}\n**你的输出**：  "
    }
  }
}
```

##### 系统提示词示例
```json
{
  "name": "systemPrompt",
  "input": {
    "type": "string",
    "value": {
      "type": "literal",
      "content": "你是一位精通中餐制作的资深厨师..."
    }
  }
}
```

#### 错误处理配置
```json
{
  "settingOnError": {
    "switch": false,
    "processType": 1,
    "timeoutMs": 600000,
    "retryTimes": 0
  }
}
```

### 🔌 插件调用节点 (Type 4)

#### 基本结构
```json
{
  "id": "节点ID",
  "type": "4",
  "data": {
    "nodeMeta": {
      "title": "插件名称",
      "subtitle": "插件:API名称"
    },
    "inputs": {
      "apiParam": [...],
      "inputDefs": [...],
      "inputParameters": [...]
    },
    "outputs": [...]
  }
}
```

#### API参数配置 (apiParam)
| 参数名 | 说明 | 示例值 |
|--------|------|--------|
| `apiID` | API唯一标识 | "7486017394901581876" |
| `apiName` | API名称 | "html2url" |
| `pluginID` | 插件ID | "7486017394901565492" |
| `pluginName` | 插件名称 | "上传html" |
| `pluginVersion` | 插件版本 | "" |

#### 输入定义 (inputDefs)
```json
{
  "description": "网页内容",
  "name": "html",
  "required": true,
  "type": "string"
}
```

#### 输入参数 (inputParameters)
```json
{
  "name": "html",
  "input": {
    "type": "string",
    "value": {
      "type": "ref",
      "content": {
        "source": "block-output",
        "blockID": "159465",
        "name": "output"
      }
    }
  }
}
```

#### 常用插件配置

##### html2url插件
- **功能**: 上传HTML内容生成可访问URL
- **插件ID**: 7486017394901565492
- **输入**: html (string, 必填)
- **输出**: url (string), message (string)

##### gen_img插件  
- **功能**: HTML转图片
- **插件ID**: 7486447880522760227
- **输入**: html (string, 必填), width (integer, 可选), height (integer, 可选)
- **输出**: image_url (string), message (string)

### 📤 输出节点 (Type 13)

#### 基本结构
```json
{
  "id": "节点ID",
  "type": "13",
  "data": {
    "inputs": {
      "content": {...},
      "inputParameters": [...],
      "streamingOutput": boolean,
      "callTransferVoice": boolean,
      "chatHistoryWriting": "historyWrite"
    }
  }
}
```

#### 输出配置参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `content` | object | 输出内容配置 |
| `streamingOutput` | boolean | 是否流式输出 |
| `callTransferVoice` | boolean | 是否支持语音转换 |
| `chatHistoryWriting` | string | 对话历史写入模式 |

#### 内容配置示例
```json
{
  "content": {
    "type": "string",
    "value": {
      "type": "literal",
      "content": "{{output}}\n\n内容生成完毕，开始制作卡片，等待1-2分钟....."
    }
  }
}
```

## 变量引用系统

### 引用类型

#### 字面量引用 (literal)
```json
{
  "type": "literal",
  "content": "固定文本内容",
  "rawMeta": {"type": 1}
}
```

#### 节点输出引用 (ref)
```json
{
  "type": "ref",
  "content": {
    "source": "block-output",
    "blockID": "源节点ID",
    "name": "输出变量名"
  },
  "rawMeta": {"type": 1}
}
```

#### 工作流输入引用
```json
{
  "type": "ref",
  "content": {
    "source": "block-output",
    "blockID": "100001",
    "name": "input"
  }
}
```

### 数据类型映射
| rawMeta.type | 对应类型 | 说明 |
|--------------|----------|------|
| 1 | string | 字符串类型 |
| 2 | integer | 整数类型 |
| 3 | boolean | 布尔类型 |
| 4 | float | 浮点数类型 |

## 节点连接规则

### 连接定义
```json
{
  "sourceNodeID": "源节点ID",
  "targetNodeID": "目标节点ID"
}
```

### 连接约束
1. **类型兼容**: 输出类型必须与输入类型匹配
2. **循环检测**: 不允许形成循环依赖
3. **分支支持**: 一个节点可以连接多个目标节点
4. **合并限制**: 多个源节点不能直接合并到一个输入

### 执行顺序
- **串行执行**: 按连接顺序依次执行
- **并行分支**: 同一源节点的多个目标可并行执行
- **依赖等待**: 节点等待所有输入就绪后执行

## 位置和布局

### 坐标系统
- **原点**: 画布左上角
- **单位**: 像素
- **X轴**: 向右为正
- **Y轴**: 向下为正

### 节点尺寸
- **标准宽度**: 360像素
- **高度变化**: 根据内容自适应
- **边界**: 包含图标、标题和参数区域

### 布局最佳实践
1. **水平间距**: 建议200-400像素
2. **垂直间距**: 建议100-200像素
3. **分支布局**: 垂直分散避免重叠
4. **对齐原则**: 保持视觉整洁

## 性能优化建议

### 节点配置优化
1. **合理设置超时**: 根据任务复杂度调整
2. **控制Token数量**: 避免不必要的长输出
3. **选择合适模型**: 平衡性能和成本
4. **缓存策略**: 重复调用考虑缓存

### 工作流设计优化
1. **减少串行依赖**: 增加并行处理
2. **错误处理**: 设置合理的重试和降级
3. **资源管理**: 避免资源密集型操作集中
4. **监控告警**: 关键节点添加状态输出

---

*本参考手册基于五星厨师工作流的实际配置，提供了Coze平台节点配置的详细说明和最佳实践。*
