# 五星厨师工作流导入与使用指南

## 📋 导入前准备

### 系统要求
- **平台**: Coze工作流平台 (www.coze.cn)
- **账户权限**: 具有工作流创建和编辑权限
- **网络环境**: 稳定的网络连接
- **浏览器**: 现代浏览器 (Chrome、Firefox、Safari等)

### 依赖检查

#### 必需插件
在导入前请确认以下插件在您的工作空间中可用：

| 插件名称 | 插件ID | 功能 | 获取方式 | 状态 |
|----------|--------|------|----------|------|
| html2url | 7486017394901565492 | HTML内容托管 | Coze插件市场 | ⚠️ 专用插件 |
| gen_img (html2img) | 7486447880522760227 | HTML转图片 | Coze插件市场 | ⚠️ 专用插件 |

> **注意**: 这两个插件不在公开的插件市场列表中，可能是专用或内部插件。如果无法获取，请参考下方的替代方案。

#### 模型访问权限
- **DeepSeek-V3-0324**: 确保您的账户有权限使用此模型
- **备用模型**: 如无权限，可替换为其他可用的LLM模型

## 🚀 导入步骤

### 步骤1: 获取工作流文件
1. 下载 `workflow/五星厨师.json` 文件
2. 确认文件完整性和格式正确性
3. 备份原始文件以防导入失败

### 步骤2: 访问Coze平台
1. 登录 [Coze工作流平台](https://www.coze.cn)
2. 进入您的工作空间
3. 导航到工作流管理页面

### 步骤3: 导入工作流
1. 点击"导入工作流"或"从剪贴板导入"按钮
2. 选择上传JSON文件或粘贴文件内容
3. 等待系统解析和验证工作流配置

### 步骤4: 配置验证
导入后系统会自动验证以下内容：
- ✅ 节点配置完整性
- ✅ 插件可用性
- ✅ 模型访问权限
- ✅ 连接关系正确性

### 步骤5: 权限配置
根据提示配置必要的权限：
- **插件授权**: 授权使用html2url和gen_img插件
- **模型权限**: 确认LLM模型使用权限
- **网络访问**: 允许访问外部API服务

## ⚙️ 配置调整

### 模型替换 (如需要)

如果您无法使用DeepSeek-V3-0324模型，可以替换为其他模型：

#### 支持的替代模型
- **GPT-4**: 高质量输出，成本较高
- **Claude-3**: 平衡性能和成本
- **通义千问**: 中文优化，成本友好
- **文心一言**: 百度模型，中文支持好

#### 替换步骤
1. 进入大模型节点配置
2. 修改 `modleName` 参数
3. 调整 `modelType` 参数 (如需要)
4. 测试新模型的输出质量

### 插件配置调整

#### html2url插件配置
```json
{
  "apiID": "7486017394901581876",
  "apiName": "html2url",
  "pluginID": "7486017394901565492",
  "pluginName": "上传html"
}
```

#### gen_img插件配置
```json
{
  "apiID": "7486447880522776611",
  "apiName": "gen_img",
  "pluginID": "7486447880522760227",
  "pluginName": "html2img"
}
```

### 超时时间调整

根据您的网络环境和性能要求调整超时设置：

| 节点类型 | 默认超时 | 建议范围 | 调整原因 |
|----------|----------|----------|----------|
| LLM节点 | 600秒 | 300-900秒 | 根据模型响应速度 |
| 插件节点 | 180秒 | 120-300秒 | 根据网络环境 |

## 🧪 测试验证

### 功能测试

#### 基础功能测试
1. **输入测试**: 输入简单菜名如"西红柿炒鸡蛋"
2. **输出检查**: 验证是否生成完整菜谱
3. **格式验证**: 检查HTML格式是否正确
4. **链接测试**: 验证生成的URL是否可访问
5. **图片预览**: 确认图片是否正常生成和显示

#### 边界条件测试
- **复杂菜名**: 测试"红烧狮子头"等复杂菜品
- **模糊输入**: 测试"家常菜"等模糊描述
- **特殊字符**: 测试包含特殊字符的输入
- **长文本**: 测试较长的菜名或描述

### 性能测试

#### 响应时间测试
| 测试项目 | 预期时间 | 实际时间 | 状态 |
|----------|----------|----------|------|
| 菜谱生成 | 30-60秒 | ___ | ⏳ |
| HTML转换 | 10-20秒 | ___ | ⏳ |
| URL生成 | 5-10秒 | ___ | ⏳ |
| 图片生成 | 15-30秒 | ___ | ⏳ |

#### 并发测试
- 同时运行多个工作流实例
- 观察系统资源使用情况
- 检查是否出现排队或超时

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 插件不可用
**问题**: 提示插件未找到或无权限
**解决方案**:
- 检查插件是否已安装到工作空间
- 确认插件版本兼容性
- 联系管理员获取插件权限

#### 2. 模型调用失败
**问题**: LLM节点执行失败
**解决方案**:
- 检查模型访问权限
- 验证API配额是否充足
- 尝试替换为其他可用模型

#### 3. 超时错误
**问题**: 节点执行超时
**解决方案**:
- 增加超时时间设置
- 检查网络连接稳定性
- 优化提示词减少处理时间

#### 4. 输出格式错误
**问题**: 生成的HTML格式不正确
**解决方案**:
- 检查系统提示词配置
- 调整LLM参数设置
- 增加输出格式验证

### 调试技巧

#### 启用调试模式
1. 在输出节点中添加调试信息
2. 输出中间处理结果
3. 记录关键变量值

#### 分段测试
1. **单节点测试**: 逐个测试每个节点
2. **分段测试**: 测试部分工作流
3. **端到端测试**: 完整流程测试

## 📊 使用指南

### 输入格式要求

#### 标准输入格式
- **菜名**: 简洁明确的中文菜名
- **示例**: "宫保鸡丁"、"麻婆豆腐"、"糖醋里脊"

#### 支持的输入类型
- **经典菜品**: 传统中式菜肴
- **家常菜**: 日常家庭料理
- **地方特色**: 各地特色菜品
- **创新菜品**: 现代创新料理

### 输出内容说明

#### 文本输出
- **菜谱结构**: 标题、食材、步骤、技巧、变体
- **语言风格**: 口语化、生活化表达
- **实用性**: 详细的制作指导

#### HTML输出
- **可视化**: 结构化的网页展示
- **响应式**: 适配不同设备屏幕
- **美观性**: 现代化的设计风格

#### 图片输出
- **格式**: PNG/JPEG图片
- **尺寸**: 默认800像素宽度
- **内容**: HTML页面的可视化截图

### 最佳使用实践

#### 输入优化
1. **明确具体**: 使用具体的菜名而非模糊描述
2. **中文输入**: 使用标准中文菜名
3. **避免歧义**: 避免一词多义的菜名

#### 结果利用
1. **保存链接**: 及时保存生成的URL链接
2. **下载图片**: 保存图片到本地备用
3. **分享传播**: 利用多种格式进行分享

## 🔄 维护更新

### 定期维护

#### 插件更新
- 定期检查插件版本更新
- 关注插件功能变更
- 及时更新插件配置

#### 模型优化
- 关注新模型发布
- 测试模型性能表现
- 根据需要切换模型

### 性能监控

#### 关键指标
- **成功率**: 工作流执行成功率
- **响应时间**: 平均执行时间
- **用户满意度**: 输出质量评价

#### 优化建议
- 根据使用数据调整配置
- 优化提示词提高质量
- 升级硬件提升性能

## 📞 技术支持

### 获取帮助
- **官方文档**: 查阅Coze平台官方文档
- **社区论坛**: 参与用户社区讨论
- **技术支持**: 联系平台技术支持团队

### 反馈渠道
- **问题报告**: 通过官方渠道报告问题
- **功能建议**: 提出改进建议
- **使用心得**: 分享使用经验

---

*本导入指南提供了五星厨师工作流的完整导入、配置和使用说明，帮助用户快速上手并充分利用工作流功能。*
